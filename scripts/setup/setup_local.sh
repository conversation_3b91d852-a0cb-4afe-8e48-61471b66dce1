#!/bin/bash

# 本地开发环境设置脚本
# 用于快速设置和测试 Reddit 集成

echo "🚀 游戏监控系统 - 本地环境设置"
echo "================================"

# 检查当前目录
if [ ! -f "requirements.txt" ]; then
    echo "❌ 错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检测可用的 Python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "❌ 错误：未找到 Python"
    exit 1
fi

echo "✅ 使用 Python: $($PYTHON_CMD --version)"

# 方法1：使用现有虚拟环境
if [ -d "venv" ]; then
    echo ""
    echo "📦 发现现有虚拟环境 (venv/)"
    echo "激活命令："
    echo "  source venv/bin/activate"
    
    # 检查是否已激活
    if [[ "$VIRTUAL_ENV" == *"venv"* ]]; then
        echo "✅ 虚拟环境已激活"
    else
        echo "⚠️  请先激活虚拟环境"
        source venv/bin/activate 2>/dev/null || true
    fi
fi

# 方法2：使用 uv
if command -v uv &> /dev/null; then
    echo ""
    echo "🚀 检测到 uv (快速包管理器)"
    echo "使用 uv 的命令："
    echo "  uv venv          # 创建虚拟环境"
    echo "  source .venv/bin/activate"
    echo "  uv pip install -r requirements.txt"
fi

# 检查必要的包
echo ""
echo "📋 检查必要的包..."

check_package() {
    package=$1
    $PYTHON_CMD -c "import $package" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ $package 已安装"
    else
        echo "❌ $package 未安装"
        return 1
    fi
}

# 检查核心包
packages=("flask" "praw" "pytrends" "pymysql" "redis")
missing_packages=0

for pkg in "${packages[@]}"; do
    if ! check_package "$pkg"; then
        ((missing_packages++))
    fi
done

# 环境变量检查
echo ""
echo "🔐 检查环境变量..."

check_env() {
    var_name=$1
    var_value=${!var_name}
    
    if [ -z "$var_value" ]; then
        echo "❌ $var_name 未设置"
        return 1
    else
        echo "✅ $var_name 已设置"
        return 0
    fi
}

# 检查 .env 文件
if [ -f ".env" ]; then
    echo "✅ 找到 .env 文件"
    # 加载 .env 文件
    export $(grep -v '^#' .env | xargs)
    
    # 检查 Reddit 配置
    check_env "REDDIT_CLIENT_ID"
    check_env "REDDIT_CLIENT_SECRET"
else
    echo "⚠️  未找到 .env 文件"
    echo "运行: cp .env.example .env"
fi

# 提供安装建议
if [ $missing_packages -gt 0 ]; then
    echo ""
    echo "📦 安装缺失的包："
    echo ""
    echo "选项 1 - 使用虚拟环境（推荐）:"
    echo "  source venv/bin/activate"
    echo "  pip install -r requirements.txt"
    echo ""
    echo "选项 2 - 使用 uv（更快）:"
    echo "  uv venv && source .venv/bin/activate"
    echo "  uv pip install -r requirements.txt"
    echo ""
    echo "选项 3 - 系统级安装（不推荐）:"
    echo "  pip3 install --user -r requirements.txt"
fi

# 测试 Reddit 连接
echo ""
echo "🧪 测试 Reddit 连接..."
echo "运行: python test_reddit_integration.py"

# 数据库迁移提醒
echo ""
echo "💾 数据库迁移："
echo "如果是首次运行，需要创建 Reddit 表："
echo "  mysql -u root -p game_monitor < sql/add_reddit_metrics.sql"

# 启动建议
echo ""
echo "🚀 启动应用："
echo "1. 开发模式: python app.py"
echo "2. Docker模式: docker-compose up -d"
echo ""
echo "📊 访问增强趋势页面："
echo "http://localhost:8088/enhanced-trends"