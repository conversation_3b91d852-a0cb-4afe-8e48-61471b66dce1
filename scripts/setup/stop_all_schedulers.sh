#!/bin/bash
# 停止所有调度器的快捷脚本

echo "正在停止所有调度器..."

# 使用管理脚本停止
if [ -f "manage_schedulers.py" ]; then
    python manage_schedulers.py stop all
fi

# 额外检查并杀死可能残留的进程
echo ""
echo "检查残留进程..."
ps aux | grep -E "scheduler_(scraper|reddit|trends|reports).py" | grep -v grep | awk '{print $2}' | while read pid; do
    echo "发现残留进程 PID: $pid，正在终止..."
    kill -9 $pid 2>/dev/null
done

# 清理PID文件
rm -f /tmp/game_monitor_scheduler_*.pid

echo "✅ 所有调度器已停止"