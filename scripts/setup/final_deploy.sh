#!/bin/bash

# Game Monitor 最终部署脚本
# 基于测试优化的配置，确保稳定运行

set -e

PROJECT_DIR="/www/wwwroot/game-monitor"
PYTHON_BIN="$PROJECT_DIR/venv/bin/python"

echo "🚀 开始最终部署 Game Monitor..."

# 检查环境
cd "$PROJECT_DIR"
if [ ! -d "venv" ]; then
    echo "❌ 虚拟环境不存在"
    exit 1
fi

# 激活虚拟环境
source venv/bin/activate

echo "📦 确保依赖已安装..."
pip install -q requests beautifulsoup4 pymysql sqlalchemy flask schedule python-dotenv

echo "🗄️ 优化数据库结构..."
python -c "
from app import app
from models import db
from sqlalchemy import text

with app.app_context():
    try:
        # 检查关键字段
        result = db.session.execute(text('''
            SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'games'
        '''))
        columns = [row[0] for row in result]
        
        missing_columns = []
        required_columns = ['embed_url', 'source_url', 'genre', 'tags', 'author', 'player_count', 'rating', 'scrape_method']
        
        for col in required_columns:
            if col not in columns:
                missing_columns.append(col)
        
        # 添加缺失字段
        if 'embed_url' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN embed_url VARCHAR(500)'))
        if 'source_url' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN source_url VARCHAR(500)'))
        if 'genre' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN genre VARCHAR(100)'))
        if 'tags' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN tags TEXT'))
        if 'author' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN author VARCHAR(255)'))
        if 'player_count' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN player_count VARCHAR(50)'))
        if 'rating' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN rating DECIMAL(3,2)'))
        if 'scrape_method' not in columns:
            db.session.execute(text('ALTER TABLE games ADD COLUMN scrape_method VARCHAR(20)'))
            
        db.session.commit()
        
        # 确保所有表存在
        db.create_all()
        print('✅ 数据库结构已优化')
        
    except Exception as e:
        print(f'❌ 数据库优化失败: {e}')
        db.session.rollback()
"

echo "🧪 测试优化后的爬虫..."
timeout 60 python -c "
from production_scraper import ProductionGameScraper
scraper = ProductionGameScraper()
try:
    # 测试几个主要网站
    test_sites = [
        'https://itch.io/games/new-and-popular',
        'https://www.yiv.com/hot-games',
        'https://y8.com/new/games'
    ]
    
    total_games = 0
    success_count = 0
    
    for url in test_sites:
        try:
            html = scraper.scrape_url(url)
            if html:
                games = scraper.parse_games(html, url)
                total_games += len(games)
                if len(games) > 0:
                    success_count += 1
                    print(f'✅ {url}: {len(games)} games')
                else:
                    print(f'⚠️  {url}: 0 games')
        except Exception as e:
            print(f'❌ {url}: {e}')
    
    print(f'\\n测试结果: {success_count}/{len(test_sites)} 成功, 总计 {total_games} 游戏')
    
    if success_count >= 2:
        print('✅ 爬虫测试通过')
    else:
        print('⚠️  爬虫测试需要关注')
        
finally:
    scraper.close()
" || echo "⚠️  测试超时，继续部署"

echo "🛑 停止旧服务..."
sudo systemctl stop game-monitor-scheduler 2>/dev/null || true

echo "⏰ 设置定时任务..."
# 创建简单可靠的爬虫脚本
cat > run_final_scraper.sh << 'EOF'
#!/bin/bash
cd /www/wwwroot/game-monitor
source venv/bin/activate

# 运行爬虫并记录结果
echo "$(date): 开始爬虫任务" >> logs/scraper_status.log
python production_scraper.py >> logs/scraper_$(date +%Y%m%d).log 2>&1
EXIT_CODE=$?

if [ $EXIT_CODE -eq 0 ]; then
    echo "$(date): 爬虫任务成功完成" >> logs/scraper_status.log
else
    echo "$(date): 爬虫任务失败 (退出码: $EXIT_CODE)" >> logs/scraper_status.log
fi
EOF

chmod +x run_final_scraper.sh

# 更新 crontab
echo "设置每小时执行的定时任务..."
(crontab -l 2>/dev/null | grep -v game-monitor; echo "# Game Monitor 最终版本"; echo "0 * * * * $PROJECT_DIR/run_final_scraper.sh") | crontab -

echo "🌐 重启 Web 服务..."
sudo systemctl restart game-monitor 2>/dev/null || {
    echo "Web 服务需要手动启动:"
    echo "nohup $PYTHON_BIN app.py > logs/web.log 2>&1 &"
}

echo "🧹 清理旧日志..."
find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null || true

echo ""
echo "✅ 最终部署完成！"
echo ""
echo "📊 部署摘要:"
echo "  爬虫网站: 25个 (移除了2个需要JavaScript的网站)"
echo "  执行频率: 每小时"
echo "  部署方式: cron 定时任务"
echo "  爬虫引擎: requests + BeautifulSoup (轻量级)"
echo ""
echo "🔧 管理命令:"
echo "  手动运行: $PROJECT_DIR/run_final_scraper.sh"
echo "  查看日志: tail -f $PROJECT_DIR/logs/scraper_\$(date +%Y%m%d).log"
echo "  查看状态: tail -f $PROJECT_DIR/logs/scraper_status.log"
echo "  检查定时: crontab -l"
echo ""
echo "📈 期望结果:"
echo "  成功率: 18-20/25 网站 (72-80%)"
echo "  每次抓取: 200-500个游戏"
echo "  新游戏: 10-50个"
echo ""

deactivate