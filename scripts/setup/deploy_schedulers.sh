#!/bin/bash
# 调度器部署脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 获取脚本所在目录和项目根目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
ROOT_DIR="$( cd "$SCRIPT_DIR/../.." && pwd )"
cd "$ROOT_DIR"

# 检查Python环境
check_python() {
    echo -e "${YELLOW}检查Python环境...${NC}"
    
    # 优先使用虚拟环境
    if [ -f "venv/bin/python" ]; then
        PYTHON_CMD="venv/bin/python"
        echo -e "${GREEN}✓ 使用虚拟环境 Python${NC}"
    else
        PYTHON_CMD="python3"
        echo -e "${YELLOW}⚠ 使用系统 Python${NC}"
    fi
    
    # 检查依赖
    echo "检查依赖包..."
    $PYTHON_CMD -c "import schedule, psutil" 2>/dev/null
    if [ $? -ne 0 ]; then
        echo -e "${RED}✗ 缺少必要的Python包${NC}"
        echo "请激活虚拟环境并安装依赖："
        echo "  source venv/bin/activate"
        echo "  pip install schedule psutil"
        exit 1
    fi
    echo -e "${GREEN}✓ 依赖检查通过${NC}"
}

# 创建systemd服务文件
create_systemd_service() {
    local service_name=$1
    local script_name=$2
    local description=$3
    
    cat > /tmp/game-monitor-${service_name}.service << EOF
[Unit]
Description=${description}
After=network.target mysql.service

[Service]
Type=simple
User=$USER
WorkingDirectory=$ROOT_DIR
ExecStart=$PYTHON_CMD $ROOT_DIR/${script_name}
Restart=always
RestartSec=10
StandardOutput=append:$ROOT_DIR/logs/scheduler_${service_name}.log
StandardError=append:$ROOT_DIR/logs/scheduler_${service_name}.log

[Install]
WantedBy=multi-user.target
EOF

    echo -e "${GREEN}创建了systemd服务文件: /tmp/game-monitor-${service_name}.service${NC}"
}

# 部署为systemd服务
deploy_systemd() {
    echo -e "${YELLOW}部署为systemd服务...${NC}"
    
    # 创建服务文件
    create_systemd_service "scraper" "src/scheduler_scraper.py" "Game Monitor Scraper Scheduler"
    create_systemd_service "reddit" "src/scheduler_reddit.py" "Game Monitor Reddit Scheduler"
    create_systemd_service "trends" "src/scheduler_trends.py" "Game Monitor Trends Scheduler"
    create_systemd_service "reports" "src/scheduler_reports.py" "Game Monitor Reports Scheduler"
    create_systemd_service "monitor" "scripts/monitoring/scheduler_monitor.py" "Game Monitor Monitor Scheduler"
    
    echo ""
    echo -e "${YELLOW}要安装systemd服务，请运行以下命令:${NC}"
    echo "sudo cp /tmp/game-monitor-*.service /etc/systemd/system/"
    echo "sudo systemctl daemon-reload"
    echo "sudo systemctl enable game-monitor-scraper"
    echo "sudo systemctl enable game-monitor-reddit"
    echo "sudo systemctl enable game-monitor-trends"
    echo "sudo systemctl enable game-monitor-reports"
    echo "sudo systemctl enable game-monitor-monitor"
    echo ""
    echo -e "${YELLOW}启动服务:${NC}"
    echo "sudo systemctl start game-monitor-scraper"
    echo "sudo systemctl start game-monitor-reddit"
    echo "sudo systemctl start game-monitor-trends"
    echo "sudo systemctl start game-monitor-reports"
    echo "sudo systemctl start game-monitor-monitor"
}

# 部署为cron任务
deploy_cron() {
    echo -e "${YELLOW}部署为cron任务...${NC}"
    
    # 创建启动脚本
    cat > $ROOT_DIR/start_schedulers.sh << 'EOF'
#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate 2>/dev/null || true
python scripts/monitoring/manage_schedulers.py start all
EOF
    
    chmod +x $ROOT_DIR/start_schedulers.sh
    
    # 创建cron任务
    echo ""
    echo -e "${YELLOW}要添加到cron，请运行:${NC}"
    echo "crontab -e"
    echo ""
    echo -e "${YELLOW}然后添加以下行:${NC}"
    echo "@reboot $ROOT_DIR/start_schedulers.sh"
    echo ""
    echo -e "${GREEN}✓ 创建了启动脚本: start_schedulers.sh${NC}"
}

# 使用nohup部署
deploy_nohup() {
    echo -e "${YELLOW}使用nohup启动调度器...${NC}"
    
    # 确保日志目录存在
    mkdir -p logs
    
    # 启动所有调度器
    echo "启动爬虫调度器..."
    nohup $PYTHON_CMD src/scheduler_scraper.py > logs/scheduler_scraper_nohup.log 2>&1 &
    echo $! > /tmp/game_monitor_scheduler_scraper.pid
    
    echo "启动Reddit调度器..."
    nohup $PYTHON_CMD src/scheduler_reddit.py > logs/scheduler_reddit_nohup.log 2>&1 &
    echo $! > /tmp/game_monitor_scheduler_reddit.pid
    
    echo "启动趋势调度器..."
    nohup $PYTHON_CMD src/scheduler_trends.py > logs/scheduler_trends_nohup.log 2>&1 &
    echo $! > /tmp/game_monitor_scheduler_trends.pid
    
    echo "启动报告调度器..."
    nohup $PYTHON_CMD src/scheduler_reports.py > logs/scheduler_reports_nohup.log 2>&1 &
    echo $! > /tmp/game_monitor_scheduler_reports.pid
    
    echo "启动监控调度器..."
    nohup $PYTHON_CMD scripts/monitoring/scheduler_monitor.py > logs/scheduler_monitor_nohup.log 2>&1 &
    echo $! > /tmp/game_monitor_scheduler_monitor.pid
    
    echo -e "${GREEN}✓ 所有调度器已在后台启动${NC}"
    echo ""
    echo -e "${YELLOW}查看状态:${NC}"
    echo "python scripts/monitoring/manage_schedulers.py status"
}

# 主菜单
main_menu() {
    echo -e "${GREEN}===================================${NC}"
    echo -e "${GREEN}游戏监控系统调度器部署工具${NC}"
    echo -e "${GREEN}===================================${NC}"
    echo ""
    echo "请选择部署方式:"
    echo "1) 使用管理脚本手动管理"
    echo "2) 部署为systemd服务（推荐用于生产环境）"
    echo "3) 部署为cron任务（系统重启后自动启动）"
    echo "4) 使用nohup立即启动（快速测试）"
    echo "5) 退出"
    echo ""
    read -p "请输入选择 [1-5]: " choice
    
    case $choice in
        1)
            echo ""
            echo -e "${GREEN}使用管理脚本:${NC}"
            echo "启动所有: python scripts/monitoring/manage_schedulers.py start all"
            echo "停止所有: python scripts/monitoring/manage_schedulers.py stop all"
            echo "查看状态: python scripts/monitoring/manage_schedulers.py status"
            echo "查看日志: python scripts/monitoring/manage_schedulers.py logs <scheduler_name>"
            ;;
        2)
            deploy_systemd
            ;;
        3)
            deploy_cron
            ;;
        4)
            deploy_nohup
            ;;
        5)
            echo "退出"
            exit 0
            ;;
        *)
            echo -e "${RED}无效选择${NC}"
            exit 1
            ;;
    esac
}

# 主程序
check_python
main_menu