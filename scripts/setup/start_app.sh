#!/bin/bash

# 启动Flask应用的脚本

echo "🚀 启动游戏监控系统..."

# 激活虚拟环境
source venv/bin/activate

# 设置环境变量
export FLASK_ENV=development
export FLASK_DEBUG=True

# 检查端口
if lsof -Pi :8088 -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 8088 已被占用"
    echo "运行以下命令查看占用进程："
    echo "  lsof -i :8088"
    exit 1
fi

echo "✅ 环境准备就绪"
echo "📊 访问地址："
echo "  - 首页: http://localhost:8088"
echo "  - 增强趋势: http://localhost:8088/enhanced-trends"
echo "  - API: http://localhost:8088/api/v2/trends/unified"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动应用
python app.py