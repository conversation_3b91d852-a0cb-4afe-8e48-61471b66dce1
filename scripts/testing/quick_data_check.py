#!/usr/bin/env python3
"""
快速数据检查脚本 - 日常使用
"""

from app import app
from models import Game, Trend, Alert
import json
from datetime import datetime, timedelta

def quick_check():
    """快速数据检查"""
    
    with app.app_context():
        print("🔍 === 快速数据检查 ===\n")
        
        # 1. 基本统计
        total_games = Game.query.filter_by(is_active=True).count()
        total_trends = Trend.query.count()
        recent_trends = Trend.query.filter(
            Trend.created_at >= datetime.now() - timedelta(days=1)
        ).count()
        
        print(f"📊 数据概览:")
        print(f"   活跃游戏: {total_games}")
        print(f"   总趋势记录: {total_trends}")
        print(f"   今日新增: {recent_trends}")
        
        # 2. 最新趋势数据
        print(f"\n📈 最新趋势分析:")
        latest_trends = Trend.query.order_by(Trend.created_at.desc()).limit(5).all()
        
        for trend in latest_trends:
            status = "🚀" if trend.growth_rate > 100 else "📈" if trend.growth_rate > 50 else "📊"
            print(f"   {status} {trend.keyword}: {trend.growth_rate:.1f}% ({trend.date})")
        
        # 3. 数据质量检查
        print(f"\n✅ 数据质量:")
        
        # 检查trend_data完整性
        valid_data = 0
        for trend in Trend.query.all():
            if trend.trend_data:
                try:
                    data = json.loads(trend.trend_data)
                    if 'metrics' in data and 'chart_data' in data:
                        valid_data += 1
                except:
                    pass
        
        print(f"   有效trend_data: {valid_data}/{total_trends}")
        
        # 检查孤儿记录
        orphan_trends = Trend.query.filter(~Trend.game_id.in_(
            Game.query.with_entities(Game.id)
        )).count()
        print(f"   孤儿趋势记录: {orphan_trends}")
        
        # 4. 爆发游戏
        surge_games = Trend.query.filter(Trend.growth_rate >= 50).order_by(Trend.growth_rate.desc()).all()
        if surge_games:
            print(f"\n🚀 爆发增长游戏 ({len(surge_games)}个):")
            for trend in surge_games[:3]:  # 显示前3个
                print(f"   🔥 {trend.keyword}: +{trend.growth_rate:.1f}%")
        else:
            print(f"\n😴 暂无爆发增长游戏")

def check_specific_game(game_name):
    """检查特定游戏的数据"""
    
    with app.app_context():
        game = Game.query.filter(Game.name.ilike(f'%{game_name}%')).first()
        
        if not game:
            print(f"❌ 未找到游戏: {game_name}")
            return
        
        print(f"🎮 游戏详情: {game.name}")
        print(f"   ID: {game.id}")
        print(f"   来源: {game.source_site}")
        print(f"   创建时间: {game.created_at}")
        
        # 查找趋势数据
        trends = Trend.query.filter_by(game_id=game.id).all()
        
        if trends:
            print(f"\n📈 趋势数据 ({len(trends)}条):")
            for trend in trends:
                print(f"   关键词: {trend.keyword}")
                print(f"   增长率: {trend.growth_rate:.1f}%")
                print(f"   分析日期: {trend.date}")
                
                if trend.trend_data:
                    try:
                        data = json.loads(trend.trend_data)
                        metrics = data.get('metrics', {})
                        print(f"   当前值: {metrics.get('current_value', 'N/A')}")
                        print(f"   最大值: {metrics.get('max_value', 'N/A')}")
                        print(f"   热度级别: {metrics.get('heat_level', 'N/A')}")
                        print(f"   是否爆发: {'是' if metrics.get('is_surge') else '否'}")
                    except:
                        print(f"   ❌ trend_data解析失败")
                print()
        else:
            print(f"\n❌ 暂无趋势数据")

def main():
    import sys
    
    if len(sys.argv) > 1:
        # 检查特定游戏
        game_name = ' '.join(sys.argv[1:])
        check_specific_game(game_name)
    else:
        # 快速检查
        quick_check()

if __name__ == "__main__":
    main()