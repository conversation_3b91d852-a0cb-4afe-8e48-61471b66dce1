#!/usr/bin/env python3
"""
趋势数据准确性测试脚本
测试Google Trends数据的完整性和准确性
"""

import json
import sys
from datetime import datetime, timedelta
from app import app
from models import db, Game, Trend, Alert
from src.enhanced_trends_analyzer import EnhancedTrendsAnalyzer

def test_database_integrity():
    """测试数据库完整性"""
    print("🔍 === 数据库完整性测试 ===\n")
    
    with app.app_context():
        # 1. 基本数据统计
        games_count = Game.query.filter_by(is_active=True).count()
        trends_count = Trend.query.count()
        alerts_count = Alert.query.count()
        
        print(f"📊 数据统计:")
        print(f"   活跃游戏: {games_count}")
        print(f"   趋势记录: {trends_count}")
        print(f"   警报记录: {alerts_count}")
        
        # 2. 数据关联性检查
        games_with_trends = Game.query.join(Trend).distinct().count()
        orphan_trends = Trend.query.filter(~Trend.game_id.in_(
            db.session.query(Game.id)
        )).count()
        
        print(f"\n🔗 数据关联性:")
        print(f"   有趋势的游戏: {games_with_trends}/{games_count}")
        print(f"   孤儿趋势记录: {orphan_trends}")
        
        # 3. 数据时效性检查
        recent_trends = Trend.query.filter(
            Trend.created_at >= datetime.utcnow() - timedelta(days=7)
        ).count()
        
        print(f"\n⏰ 数据时效性:")
        print(f"   近7天趋势: {recent_trends}/{trends_count}")
        
        return {
            'games_count': games_count,
            'trends_count': trends_count,
            'games_with_trends': games_with_trends,
            'orphan_trends': orphan_trends,
            'recent_trends': recent_trends
        }

def test_trend_data_structure():
    """测试趋势数据结构完整性"""
    print("\n📋 === 趋势数据结构测试 ===\n")
    
    with app.app_context():
        trends = Trend.query.all()
        
        valid_trends = 0
        invalid_trends = 0
        structure_issues = []
        
        for trend in trends:
            print(f"🎯 测试: {trend.keyword}")
            
            # 基本字段检查
            issues = []
            if not trend.keyword:
                issues.append("keyword为空")
            if not trend.game_id:
                issues.append("game_id为空")
            if trend.growth_rate is None:
                issues.append("growth_rate为空")
            
            # trend_data JSON结构检查
            if trend.trend_data:
                try:
                    data = json.loads(trend.trend_data)
                    
                    # 检查必要字段
                    required_fields = ['metrics', 'chart_data']
                    for field in required_fields:
                        if field not in data:
                            issues.append(f"trend_data缺少{field}字段")
                    
                    if 'metrics' in data:
                        metrics = data['metrics']
                        metric_fields = ['growth_rate', 'current_value', 'trend_data']
                        for field in metric_fields:
                            if field not in metrics:
                                issues.append(f"metrics缺少{field}字段")
                    
                    if 'chart_data' in data:
                        chart = data['chart_data']
                        chart_fields = ['keyword', 'dates', 'values']
                        for field in chart_fields:
                            if field not in chart:
                                issues.append(f"chart_data缺少{field}字段")
                        
                        # 检查数据一致性
                        if 'dates' in chart and 'values' in chart:
                            if len(chart['dates']) != len(chart['values']):
                                issues.append("dates和values长度不匹配")
                
                except json.JSONDecodeError:
                    issues.append("trend_data不是有效JSON")
            else:
                issues.append("trend_data为空")
            
            if issues:
                invalid_trends += 1
                structure_issues.extend(issues)
                print(f"   ❌ 问题: {', '.join(issues)}")
            else:
                valid_trends += 1
                print(f"   ✅ 结构完整")
        
        print(f"\n📊 结构测试结果:")
        print(f"   有效记录: {valid_trends}")
        print(f"   无效记录: {invalid_trends}")
        
        if structure_issues:
            print(f"\n⚠️  发现的问题:")
            for issue in set(structure_issues):
                count = structure_issues.count(issue)
                print(f"   - {issue} ({count}次)")
        
        return {
            'valid_trends': valid_trends,
            'invalid_trends': invalid_trends,
            'issues': structure_issues
        }

def test_trend_calculation_accuracy():
    """测试趋势计算准确性"""
    print("\n🧮 === 趋势计算准确性测试 ===\n")
    
    with app.app_context():
        trends = Trend.query.limit(3).all()  # 测试前3个
        
        accuracy_results = []
        
        for trend in trends:
            print(f"🔢 验证: {trend.keyword}")
            
            if not trend.trend_data:
                print("   ❌ 无trend_data数据")
                continue
            
            try:
                data = json.loads(trend.trend_data)
                metrics = data.get('metrics', {})
                
                # 提取原始数据
                trend_values = metrics.get('trend_data', [])
                if not trend_values:
                    print("   ❌ 无trend_data数值")
                    continue
                
                # 重新计算指标
                current_value = trend_values[-1] if trend_values else 0
                recent_avg = sum(trend_values[-3:]) / 3 if len(trend_values) >= 3 else current_value
                baseline_avg = sum(trend_values[:-3]) / len(trend_values[:-3]) if len(trend_values) > 3 else recent_avg
                
                calculated_growth = ((recent_avg - baseline_avg) / baseline_avg * 100) if baseline_avg > 0 else 0
                
                # 对比存储的数据
                stored_current = metrics.get('current_value', 0)
                stored_growth = metrics.get('growth_rate', 0)
                stored_recent_avg = metrics.get('recent_avg', 0)
                
                print(f"   当前值: 计算={current_value}, 存储={stored_current}")
                print(f"   近期均值: 计算={recent_avg:.2f}, 存储={stored_recent_avg:.2f}")
                print(f"   增长率: 计算={calculated_growth:.2f}%, 存储={stored_growth:.2f}%")
                
                # 计算误差
                current_error = abs(current_value - stored_current)
                avg_error = abs(recent_avg - stored_recent_avg)
                growth_error = abs(calculated_growth - stored_growth)
                
                is_accurate = (current_error == 0 and avg_error < 0.1 and growth_error < 1.0)
                
                print(f"   ✅ 准确" if is_accurate else f"   ❌ 误差过大")
                
                accuracy_results.append({
                    'keyword': trend.keyword,
                    'accurate': is_accurate,
                    'current_error': current_error,
                    'avg_error': avg_error,
                    'growth_error': growth_error
                })
                
            except Exception as e:
                print(f"   ❌ 计算错误: {e}")
                accuracy_results.append({
                    'keyword': trend.keyword,
                    'accurate': False,
                    'error': str(e)
                })
            
            print()
        
        accurate_count = sum(1 for r in accuracy_results if r.get('accurate', False))
        print(f"📊 计算准确性结果:")
        print(f"   准确记录: {accurate_count}/{len(accuracy_results)}")
        
        return accuracy_results

def test_api_endpoints():
    """测试API端点数据一致性"""
    print("\n🌐 === API端点测试 ===\n")
    
    with app.test_client() as client:
        # 测试主要API端点
        endpoints = [
            '/api/trends/all',
            '/api/stats',
            '/api/trending-games'
        ]
        
        api_results = {}
        
        for endpoint in endpoints:
            print(f"🔗 测试: {endpoint}")
            try:
                response = client.get(endpoint)
                
                if response.status_code == 200:
                    data = response.get_json()
                    if data:
                        print(f"   ✅ 响应正常: {response.status_code}")
                        print(f"   📊 数据键: {list(data.keys())}")
                        
                        if 'trends' in data:
                            print(f"   📈 趋势数量: {len(data['trends'])}")
                    else:
                        print(f"   ⚠️  空响应: {response.status_code}")
                else:
                    print(f"   ❌ 错误响应: {response.status_code}")
                
                api_results[endpoint] = {
                    'status': response.status_code,
                    'has_data': bool(response.get_json())
                }
                
            except Exception as e:
                print(f"   ❌ 异常: {e}")
                api_results[endpoint] = {
                    'status': 500,
                    'error': str(e)
                }
            
            print()
        
        return api_results

def run_live_trends_test():
    """运行实时趋势分析测试"""
    print("\n🔄 === 实时趋势分析测试 ===\n")
    
    try:
        print("🚀 初始化增强趋势分析器...")
        analyzer = EnhancedTrendsAnalyzer()
        
        # 测试基本功能
        print("🧪 测试基准数据获取...")
        baseline_data = analyzer.get_baseline_data()
        if baseline_data:
            print(f"   ✅ 获取到{len(baseline_data)}个基准关键词")
        else:
            print(f"   ❌ 基准数据获取失败")
        
        # 测试单个关键词
        print("\n🎯 测试单个关键词分析...")
        test_keywords = ['minecraft']
        
        trends_df = analyzer.get_trends_data_with_retry(
            test_keywords, 
            timeframe='today 1-m',
            max_retries=1
        )
        
        if trends_df is not None and not trends_df.empty:
            print(f"   ✅ 成功获取趋势数据: {trends_df.shape}")
            
            # 计算指标
            metrics = analyzer.calculate_advanced_growth_metrics(trends_df, 'minecraft')
            if metrics:
                print(f"   📊 增长率: {metrics.get('growth_rate', 'N/A')}%")
                print(f"   🌡️  热度级别: {metrics.get('heat_level', 'N/A')}")
                print(f"   💪 趋势强度: {metrics.get('trend_strength', 'N/A')}")
                
                return True
            else:
                print(f"   ❌ 指标计算失败")
                return False
        else:
            print(f"   ❌ 趋势数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 实时测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 ========== 趋势数据准确性测试 ==========\n")
    
    results = {}
    
    # 运行所有测试
    try:
        results['database'] = test_database_integrity()
        results['structure'] = test_trend_data_structure()
        results['calculation'] = test_trend_calculation_accuracy()
        results['api'] = test_api_endpoints()
        results['live'] = run_live_trends_test()
        
        # 生成测试报告
        print("\n📋 ========== 测试报告总结 ==========\n")
        
        print("✅ 测试完成项目:")
        print("   - 数据库完整性检查")
        print("   - 趋势数据结构验证")
        print("   - 计算准确性验证")
        print("   - API端点测试")
        print("   - 实时分析功能测试")
        
        # 总体评估
        issues = 0
        if results['structure']['invalid_trends'] > 0:
            issues += 1
        if not results['live']:
            issues += 1
        
        print(f"\n🎯 整体评估: {'通过' if issues == 0 else f'发现{issues}个问题'}")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        return None

if __name__ == "__main__":
    results = main()
    if results is None:
        sys.exit(1)