#!/usr/bin/env python3
"""
系统功能验证测试脚本
验证所有核心功能是否正常工作
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def test_database_connection():
    """测试数据库连接"""
    print("🔧 测试数据库连接...")
    try:
        from app import app
        with app.app_context():
            from models import db, Game
            from sqlalchemy import text
            
            # 测试连接
            db.session.execute(text('SELECT 1'))
            
            # 检查表是否存在
            game_count = Game.query.count()
            print(f"✅ 数据库连接正常，现有游戏: {game_count} 个")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_scraper_functionality():
    """测试爬虫功能"""
    print("🕷️  测试爬虫功能...")
    try:
        from src.production_scraper import ProductionGameScraper
        
        scraper = ProductionGameScraper()
        
        # 测试3个主要网站
        test_sites = [
            'https://itch.io/games/new-and-popular',
            'https://www.yiv.com/hot-games', 
            'https://y8.com/new/games'
        ]
        
        total_games = 0
        success_count = 0
        
        for url in test_sites:
            try:
                print(f"  测试: {url}")
                html = scraper.scrape_url(url)
                if html:
                    games = scraper.parse_games(html, url)
                    total_games += len(games)
                    if len(games) > 0:
                        success_count += 1
                        print(f"    ✅ 成功: {len(games)} 个游戏")
                    else:
                        print(f"    ⚠️  解析失败: 0 个游戏")
                else:
                    print(f"    ❌ 抓取失败")
            except Exception as e:
                print(f"    ❌ 错误: {e}")
        
        scraper.close()
        
        success_rate = (success_count / len(test_sites)) * 100
        print(f"✅ 爬虫测试完成: {success_count}/{len(test_sites)} 成功 ({success_rate:.1f}%), 总计 {total_games} 个游戏")
        
        return success_count >= 2  # 至少2个网站成功
        
    except Exception as e:
        print(f"❌ 爬虫测试失败: {e}")
        return False

def test_web_application():
    """测试Web应用"""
    print("🌐 测试Web应用...")
    try:
        from app import app
        
        with app.test_client() as client:
            # 测试主页
            response = client.get('/')
            if response.status_code == 200:
                print("  ✅ 主页正常")
            else:
                print(f"  ❌ 主页错误: {response.status_code}")
                return False
            
            # 测试API
            response = client.get('/api/stats')
            if response.status_code == 200:
                print("  ✅ API正常")
            else:
                print(f"  ⚠️  API可能有问题: {response.status_code}")
            
        print("✅ Web应用测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Web应用测试失败: {e}")
        return False

def test_configuration():
    """测试配置"""
    print("⚙️  测试配置...")
    try:
        from config import Config
        
        # 检查关键配置
        required_configs = [
            'MYSQL_HOST', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE',
            'SECRET_KEY', 'PORT'
        ]
        
        missing_configs = []
        for config_name in required_configs:
            if not hasattr(Config, config_name) or not getattr(Config, config_name):
                missing_configs.append(config_name)
        
        if missing_configs:
            print(f"  ⚠️  缺少配置: {', '.join(missing_configs)}")
        else:
            print("  ✅ 所有必需配置都已设置")
        
        print(f"  数据库: {Config.MYSQL_HOST}:{Config.MYSQL_PORT}")
        print(f"  Web端口: {Config.PORT}")
        print(f"  Feishu通知: {'启用' if Config.ENABLE_FEISHU_ALERTS else '禁用'}")
        
        print("✅ 配置测试完成")
        return len(missing_configs) == 0
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_full_scraper():
    """测试完整爬虫流程"""
    print("🔄 测试完整爬虫流程...")
    try:
        from src.production_scraper import run_production_scrape
        
        print("  运行生产爬虫...")
        start_time = time.time()
        result = run_production_scrape()
        execution_time = time.time() - start_time
        
        if result.get('success', False):
            print(f"  ✅ 完整爬虫成功:")
            print(f"    处理网站: {result['successful_sites']}/{result['total_sites']}")
            print(f"    找到游戏: {result['total_games']}")
            print(f"    新游戏: {result['new_games']}")
            print(f"    执行时间: {execution_time:.1f}s")
            
            # 检查成功率
            success_rate = (result['successful_sites'] / result['total_sites']) * 100
            if success_rate >= 70:
                print(f"  ✅ 成功率良好: {success_rate:.1f}%")
                return True
            else:
                print(f"  ⚠️  成功率偏低: {success_rate:.1f}%")
                return False
        else:
            print(f"  ❌ 完整爬虫失败")
            return False
            
    except Exception as e:
        print(f"❌ 完整爬虫测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始系统功能验证测试")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        ("数据库连接", test_database_connection),
        ("配置检查", test_configuration), 
        ("爬虫功能", test_scraper_functionality),
        ("Web应用", test_web_application),
        ("完整流程", test_full_scraper)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name} 测试:")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"结果: {status}")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总报告
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！系统功能正常")
        return True
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，系统基本可用")
        return True
    else:
        print("❌ 多个测试失败，请检查系统配置")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)