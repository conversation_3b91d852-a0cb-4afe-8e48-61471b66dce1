"""
Reddit集成测试脚本
测试Reddit API连接和基本功能
"""

import asyncio
import os
from dotenv import load_dotenv
from reddit_analyzer import RedditAnalyzer
from app import create_app
from models import db, Game, RedditMetric
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

async def test_reddit_connection():
    """测试Reddit API连接"""
    print("\n=== 测试Reddit API连接 ===")
    
    try:
        # 检查环境变量
        client_id = os.getenv('REDDIT_CLIENT_ID')
        client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        
        if not client_id or not client_secret:
            print("❌ 错误: 未找到Reddit API凭据")
            print("请在.env文件中设置:")
            print("REDDIT_CLIENT_ID=your_client_id")
            print("REDDIT_CLIENT_SECRET=your_client_secret")
            return False
        
        print(f"✅ 找到Reddit API凭据")
        print(f"Client ID: {client_id[:10]}...")
        
        # 创建分析器
        analyzer = RedditAnalyzer()
        print("✅ Reddit分析器初始化成功")
        
        # 测试子版块验证
        test_subreddit = 'gaming'
        is_valid = analyzer.validate_subreddit(test_subreddit)
        if is_valid:
            print(f"✅ 成功访问子版块: r/{test_subreddit}")
        else:
            print(f"❌ 无法访问子版块: r/{test_subreddit}")
            
        return analyzer
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None


async def test_game_analysis(analyzer, game_name="Vampire Survivors"):
    """测试游戏分析功能"""
    print(f"\n=== 测试游戏分析: {game_name} ===")
    
    try:
        # 执行分析
        print("正在分析Reddit数据...")
        metrics = await analyzer.analyze_game(game_name)
        
        # 显示结果
        print(f"\n📊 分析结果:")
        print(f"游戏名称: {metrics.game_name}")
        print(f"热度评分: {metrics.heat_score}/100")
        print(f"增长率: {metrics.growth_rate:+.1f}%")
        print(f"24小时帖子数: {metrics.post_count_24h}")
        print(f"总赞数: {metrics.total_upvotes_24h}")
        print(f"总评论数: {metrics.total_comments_24h}")
        print(f"独立作者数: {metrics.unique_authors}")
        print(f"情感倾向: {metrics.sentiment}")
        
        if metrics.top_post_title:
            print(f"\n🔥 最热帖子:")
            print(f"标题: {metrics.top_post_title}")
            print(f"分数: {metrics.top_post_score}")
            print(f"链接: {metrics.top_post_url}")
        
        if metrics.subreddit_distribution:
            print(f"\n📍 子版块分布:")
            for sub, count in metrics.subreddit_distribution.items():
                print(f"  r/{sub}: {count}个帖子")
        
        return metrics
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None


async def test_database_save(metrics):
    """测试数据库保存"""
    print("\n=== 测试数据库保存 ===")
    
    try:
        app = create_app()
        with app.app_context():
            # 查找或创建游戏记录
            game = Game.query.filter_by(name=metrics.game_name).first()
            
            if not game:
                print(f"创建新游戏记录: {metrics.game_name}")
                game = Game(
                    name=metrics.game_name,
                    title=metrics.game_name,
                    source_site='Reddit Test',
                    game_url=f"https://example.com/{metrics.game_name}",
                    is_active=True
                )
                db.session.add(game)
                db.session.commit()
            
            # 保存Reddit指标
            reddit_metric = RedditMetric(
                game_id=game.id,
                heat_score=metrics.heat_score,
                growth_rate=metrics.growth_rate,
                post_count_24h=metrics.post_count_24h,
                total_upvotes_24h=metrics.total_upvotes_24h,
                total_comments_24h=metrics.total_comments_24h,
                top_post_title=metrics.top_post_title,
                top_post_score=metrics.top_post_score,
                top_post_url=metrics.top_post_url,
                avg_comments=metrics.avg_comments,
                unique_authors=metrics.unique_authors,
                sentiment=metrics.sentiment,
                subreddit_distribution=metrics.subreddit_distribution,
                status=metrics.status,
                error_message=metrics.error_message
            )
            
            db.session.add(reddit_metric)
            db.session.commit()
            
            print(f"✅ 成功保存Reddit数据到数据库 (ID: {reddit_metric.id})")
            
            # 验证保存
            saved_metric = RedditMetric.query.get(reddit_metric.id)
            if saved_metric:
                print(f"✅ 验证成功: 热度={saved_metric.heat_score}, 增长率={saved_metric.growth_rate}")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库保存失败: {e}")
        return False


async def test_api_endpoint():
    """测试API端点"""
    print("\n=== 测试API端点 ===")
    
    try:
        import requests
        
        # 测试统一趋势API
        response = requests.get('http://localhost:8088/api/v2/trends/unified?limit=5')
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API响应成功")
            print(f"返回 {len(data.get('data', []))} 条数据")
            
            # 显示第一条数据
            if data['data']:
                first = data['data'][0]
                print(f"\n第一条数据:")
                print(f"游戏: {first['game']['name']}")
                if first.get('reddit'):
                    print(f"Reddit热度: {first['reddit']['heat_score']}")
                if first.get('google'):
                    print(f"Google趋势: {first['google']['value']}")
                print(f"综合评分: {first['composite_score']}")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        print("提示: 请确保Flask应用正在运行 (http://localhost:8088)")


async def main():
    """主测试函数"""
    print("🚀 开始Reddit集成测试")
    print("=" * 50)
    
    # 1. 测试连接
    analyzer = await test_reddit_connection()
    if not analyzer:
        print("\n❌ 测试中止: 无法连接Reddit API")
        return
    
    # 2. 测试游戏分析
    test_games = ["Vampire Survivors", "Wordle", "Cookie Clicker"]
    
    for game_name in test_games[:1]:  # 测试第一个游戏
        metrics = await test_game_analysis(analyzer, game_name)
        
        if metrics and metrics.status == 'success':
            # 3. 测试数据库保存
            await test_database_save(metrics)
    
    # 4. 测试API端点（需要Flask应用运行）
    # await test_api_endpoint()
    
    print("\n✅ 测试完成!")
    print("\n下一步:")
    print("1. 确保在.env文件中配置了Reddit API凭据")
    print("2. 运行数据库迁移: mysql -u root -p game_monitor < sql/add_reddit_metrics.sql")
    print("3. 启动Flask应用: python app.py")
    print("4. 访问增强趋势页面: http://localhost:8088/enhanced-trends")


if __name__ == "__main__":
    asyncio.run(main())