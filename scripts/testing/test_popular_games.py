"""
测试热门游戏的Reddit数据
"""

from app import create_app
from models import db, Game, RedditMetric
from src.reddit_analyzer_sync import RedditAnalyzerSync
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 热门游戏列表
POPULAR_GAMES = [
    "Minecraft",
    "Among Us", 
    "Cookie Clicker",
    "Wordle",
    "2048",
    "Vampire Survivors",
    "Agar.io",
    "Slither.io",
    "Tetris",
    "Chess"
]

def test_popular_games():
    """测试热门游戏"""
    app = create_app()
    
    with app.app_context():
        # 初始化分析器
        analyzer = RedditAnalyzerSync()
        
        # 确保游戏存在于数据库
        for game_name in POPULAR_GAMES:
            game = Game.query.filter_by(name=game_name).first()
            if not game:
                logger.info(f"Creating game: {game_name}")
                game = Game(
                    name=game_name,
                    title=game_name,
                    source_site='Manual',
                    game_url=f"https://example.com/{game_name.lower().replace(' ', '-')}",
                    is_active=True
                )
                db.session.add(game)
        
        db.session.commit()
        
        # 分析每个游戏
        for game_name in POPULAR_GAMES:
            logger.info(f"\n{'='*50}")
            logger.info(f"Analyzing: {game_name}")
            
            try:
                # 获取游戏
                game = Game.query.filter_by(name=game_name).first()
                
                # 分析
                metrics = analyzer.analyze_game(game_name)
                
                # 显示结果
                logger.info(f"Heat Score: {metrics.heat_score}/100")
                logger.info(f"Posts (24h): {metrics.post_count_24h}")
                logger.info(f"Total Upvotes: {metrics.total_upvotes_24h}")
                logger.info(f"Total Comments: {metrics.total_comments_24h}")
                logger.info(f"Sentiment: {metrics.sentiment}")
                
                if metrics.top_post_title:
                    logger.info(f"Top Post: {metrics.top_post_title[:80]}...")
                
                # 保存到数据库
                if metrics.heat_score > 0:
                    reddit_metric = RedditMetric(
                        game_id=game.id,
                        heat_score=metrics.heat_score,
                        growth_rate=metrics.growth_rate,
                        post_count_24h=metrics.post_count_24h,
                        total_upvotes_24h=metrics.total_upvotes_24h,
                        total_comments_24h=metrics.total_comments_24h,
                        top_post_title=metrics.top_post_title,
                        top_post_score=metrics.top_post_score,
                        top_post_url=metrics.top_post_url,
                        avg_comments=metrics.avg_comments,
                        unique_authors=metrics.unique_authors,
                        sentiment=metrics.sentiment,
                        subreddit_distribution=metrics.subreddit_distribution,
                        status=metrics.status
                    )
                    db.session.add(reddit_metric)
                    logger.info("✅ Saved to database")
                
            except Exception as e:
                logger.error(f"Error: {e}")
        
        # 提交更改
        db.session.commit()
        
        # 显示统计
        logger.info(f"\n{'='*50}")
        logger.info("Summary:")
        hot_games = RedditMetric.query.filter(RedditMetric.heat_score > 50).count()
        logger.info(f"Hot games (score > 50): {hot_games}")
        
        # 显示最热的游戏
        top_games = db.session.query(
            Game.name, 
            RedditMetric.heat_score
        ).join(
            RedditMetric
        ).order_by(
            RedditMetric.heat_score.desc()
        ).limit(5).all()
        
        logger.info("\nTop 5 Hottest Games:")
        for name, score in top_games:
            logger.info(f"  {name}: {score}/100")


if __name__ == "__main__":
    test_popular_games()