#!/usr/bin/env python3
"""
专门测试Google Trends数据抓取功能
排查为什么无法成功获取真实趋势数据
"""

import os
import time
import random
import logging
from datetime import datetime
import pandas as pd
from pytrends.request import TrendReq
from pytrends.exceptions import TooManyRequestsError, ResponseError

# 设置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_basic_pytrends():
    """测试基础pytrends功能"""
    print("🔍 === 测试1: 基础pytrends连接 ===\n")
    
    try:
        # 最简单的初始化
        pytrends = TrendReq()
        print("✅ TrendReq初始化成功")
        
        # 测试最简单的查询
        test_keywords = ['python']
        print(f"🔍 测试关键词: {test_keywords}")
        
        pytrends.build_payload(test_keywords, timeframe='today 1-m')
        print("✅ build_payload成功")
        
        # 获取数据
        interest_df = pytrends.interest_over_time()
        
        if not interest_df.empty:
            print(f"✅ 成功获取数据: {interest_df.shape}")
            print(f"📊 列名: {list(interest_df.columns)}")
            print(f"📅 日期范围: {interest_df.index.min()} 到 {interest_df.index.max()}")
            print(f"📈 最近数据:\n{interest_df.tail()}")
            return True
        else:
            print("❌ 返回空数据")
            return False
            
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_different_configurations():
    """测试不同的配置参数"""
    print("\n🔍 === 测试2: 不同配置参数 ===\n")
    
    configs = [
        {
            'name': '默认配置',
            'params': {}
        },
        {
            'name': '美国地区',
            'params': {'hl': 'en-US', 'tz': 360}
        },
        {
            'name': '中国地区',
            'params': {'hl': 'zh-CN', 'tz': 480}
        },
        {
            'name': '延长超时',
            'params': {'timeout': (30, 60)}
        }
    ]
    
    for config in configs:
        print(f"🧪 测试配置: {config['name']}")
        
        try:
            pytrends = TrendReq(**config['params'])
            pytrends.build_payload(['minecraft'], timeframe='today 1-m')
            
            data = pytrends.interest_over_time()
            
            if not data.empty:
                print(f"   ✅ 成功: {data.shape}")
                return True
            else:
                print(f"   ❌ 空数据")
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
        
        # 添加延迟
        time.sleep(5)
    
    return False

def test_different_timeframes():
    """测试不同的时间范围"""
    print("\n🔍 === 测试3: 不同时间范围 ===\n")
    
    timeframes = [
        'now 1-d',      # 最近1天
        'now 7-d',      # 最近7天  
        'today 1-m',    # 最近1个月
        'today 3-m',    # 最近3个月
        'today 12-m',   # 最近12个月
        '2024-01-01 2024-12-31'  # 指定日期范围
    ]
    
    for timeframe in timeframes:
        print(f"🕐 测试时间范围: {timeframe}")
        
        try:
            pytrends = TrendReq()
            pytrends.build_payload(['game'], timeframe=timeframe)
            
            data = pytrends.interest_over_time()
            
            if not data.empty:
                print(f"   ✅ 成功: {data.shape}, 范围: {data.index.min()} - {data.index.max()}")
                return True
            else:
                print(f"   ❌ 空数据")
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
        
        time.sleep(10)  # 更长延迟避免限制
    
    return False

def test_different_keywords():
    """测试不同类型的关键词"""
    print("\n🔍 === 测试4: 不同关键词类型 ===\n")
    
    keyword_sets = [
        # 高热度关键词
        ['python', 'minecraft', 'covid'],
        # 游戏相关
        ['fortnite', 'minecraft', 'roblox'],
        # 低热度但存在的关键词
        ['game development', 'indie game'],
        # 品牌词
        ['google', 'apple', 'microsoft'],
        # 单个高频词
        ['game'],
        ['news'],
        ['weather']
    ]
    
    for keywords in keyword_sets:
        print(f"🔍 测试关键词: {keywords}")
        
        try:
            pytrends = TrendReq()
            pytrends.build_payload(keywords, timeframe='today 1-m')
            
            data = pytrends.interest_over_time()
            
            if not data.empty:
                print(f"   ✅ 成功: {data.shape}")
                print(f"   📊 关键词数据: {list(data.columns)}")
                
                # 显示每个关键词的数据情况
                for keyword in keywords:
                    if keyword in data.columns:
                        values = data[keyword].values
                        non_zero = values[values > 0]
                        print(f"      {keyword}: {len(non_zero)}/{len(values)} 非零值, 最大:{values.max()}")
                
                return True, keywords
            else:
                print(f"   ❌ 空数据")
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
        
        time.sleep(15)
    
    return False, None

def test_regional_settings():
    """测试不同地区设置"""
    print("\n🔍 === 测试5: 不同地区设置 ===\n")
    
    regions = [
        ('全球', ''),
        ('美国', 'US'), 
        ('中国', 'CN'),
        ('英国', 'GB'),
        ('日本', 'JP'),
        ('德国', 'DE')
    ]
    
    keyword = 'minecraft'  # 使用一个肯定有数据的关键词
    
    for region_name, geo_code in regions:
        print(f"🌍 测试地区: {region_name} ({geo_code})")
        
        try:
            pytrends = TrendReq()
            pytrends.build_payload([keyword], timeframe='today 3-m', geo=geo_code)
            
            data = pytrends.interest_over_time()
            
            if not data.empty:
                values = data[keyword].values
                non_zero = values[values > 0]
                print(f"   ✅ 成功: {len(non_zero)} 个有效数据点, 最大值: {values.max()}")
                return True
            else:
                print(f"   ❌ 空数据")
                
        except Exception as e:
            print(f"   ❌ 失败: {e}")
        
        time.sleep(10)
    
    return False

def test_network_and_proxy():
    """测试网络和代理设置"""
    print("\n🔍 === 测试6: 网络连接测试 ===\n")
    
    import requests
    
    # 测试基本网络连接
    print("🌐 测试网络连接:")
    try:
        response = requests.get('https://trends.google.com', timeout=10)
        print(f"   ✅ Google Trends网站可访问: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 网络连接失败: {e}")
        return False
    
    # 测试API端点
    print("\n🔗 测试Google Trends API端点:")
    try:
        # 这是pytrends内部使用的URL
        api_url = 'https://trends.google.com/trends/api/explore'
        response = requests.get(api_url, timeout=10)
        print(f"   ✅ API端点可访问: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API端点访问失败: {e}")
    
    # 测试不同User-Agent
    print("\n👤 测试不同User-Agent:")
    
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ]
    
    for ua in user_agents:
        try:
            # 自定义请求头
            session = requests.Session()
            session.headers.update({'User-Agent': ua})
            
            pytrends = TrendReq(requests_args={'session': session})
            pytrends.build_payload(['test'], timeframe='today 1-m')
            
            data = pytrends.interest_over_time()
            if not data.empty:
                print(f"   ✅ User-Agent成功: {ua[:50]}...")
                return True
            else:
                print(f"   ❌ 空数据: {ua[:50]}...")
                
        except Exception as e:
            print(f"   ❌ 失败: {ua[:30]}... - {e}")
        
        time.sleep(5)
    
    return False

def test_rate_limiting():
    """测试速率限制"""
    print("\n🔍 === 测试7: 速率限制测试 ===\n")
    
    print("⏱️  测试连续请求的影响:")
    
    keywords = ['python', 'java', 'javascript', 'go', 'rust']
    
    for i, keyword in enumerate(keywords):
        print(f"   请求 {i+1}: {keyword}")
        
        try:
            pytrends = TrendReq()
            pytrends.build_payload([keyword], timeframe='today 1-m')
            
            start_time = time.time()
            data = pytrends.interest_over_time()
            end_time = time.time()
            
            if not data.empty:
                print(f"      ✅ 成功 (耗时: {end_time-start_time:.2f}s)")
            else:
                print(f"      ❌ 空数据 (耗时: {end_time-start_time:.2f}s)")
                
        except TooManyRequestsError:
            print(f"      ⚠️  触发速率限制")
            break
        except Exception as e:
            print(f"      ❌ 其他错误: {e}")
        
        # 逐渐增加延迟
        delay = (i + 1) * 5
        if i < len(keywords) - 1:
            print(f"      ⏸️  等待 {delay}s...")
            time.sleep(delay)

def test_enhanced_analyzer_integration():
    """测试增强分析器集成"""
    print("\n🔍 === 测试8: 增强分析器集成 ===\n")
    
    try:
        # 测试是否可以导入
        from enhanced_trends_analyzer import EnhancedTrendsAnalyzer
        print("✅ 增强分析器导入成功")
        
        # 禁用代理测试
        os.environ['USE_PROXY'] = 'false'
        os.environ['WEBSHARE_ENABLED'] = 'false'
        
        analyzer = EnhancedTrendsAnalyzer()
        print("✅ 分析器初始化成功")
        
        # 测试获取趋势数据
        keywords = ['minecraft']
        print(f"🔍 测试关键词: {keywords}")
        
        result = analyzer.get_trends_data_with_retry(
            keywords,
            timeframe='today 1-m',
            max_retries=1
        )
        
        if result is not None and not result.empty:
            print(f"✅ 增强分析器获取数据成功: {result.shape}")
            print(f"📊 数据预览:\n{result.head()}")
            return True
        else:
            print("❌ 增强分析器获取数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 增强分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 ========== Google Trends数据抓取专项测试 ==========\n")
    
    results = {
        'basic': False,
        'config': False, 
        'timeframe': False,
        'keywords': False,
        'regional': False,
        'network': False,
        'enhanced': False
    }
    
    # 运行所有测试
    print("开始运行测试套件...\n")
    
    try:
        results['basic'] = test_basic_pytrends()
        time.sleep(10)
        
        results['config'] = test_different_configurations()
        time.sleep(10)
        
        results['timeframe'] = test_different_timeframes()
        time.sleep(10)
        
        results['keywords'], working_keywords = test_different_keywords()
        time.sleep(10)
        
        results['regional'] = test_regional_settings()
        time.sleep(10)
        
        results['network'] = test_network_and_proxy()
        time.sleep(10)
        
        test_rate_limiting()
        time.sleep(10)
        
        results['enhanced'] = test_enhanced_analyzer_integration()
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        
    # 生成测试报告
    print("\n" + "="*60)
    print("📋 ========== 测试结果报告 ==========\n")
    
    success_count = sum(results.values())
    total_tests = len(results)
    
    print(f"✅ 成功测试: {success_count}/{total_tests}")
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if success_count == 0:
        print("\n🚨 所有测试都失败了！可能的原因:")
        print("   1. 网络连接问题")
        print("   2. Google Trends API地区限制")
        print("   3. IP被Google封禁")
        print("   4. pytrends版本兼容性问题")
        print("   5. 系统时间不正确")
        
        print("\n🔧 建议解决方案:")
        print("   1. 检查网络连接和DNS设置")
        print("   2. 尝试使用VPN或代理")
        print("   3. 更新pytrends版本: pip install --upgrade pytrends")
        print("   4. 检查系统时间是否正确")
        print("   5. 尝试从不同网络环境访问")
        
    elif success_count < total_tests:
        print(f"\n⚠️  部分测试失败，但基本功能可用")
        
    else:
        print(f"\n🎉 所有测试通过！Google Trends数据抓取功能正常")
    
    return results

if __name__ == "__main__":
    results = main()