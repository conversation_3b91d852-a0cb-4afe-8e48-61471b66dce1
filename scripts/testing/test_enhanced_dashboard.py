#!/usr/bin/env python3
"""
测试增强的可视化仪表板功能
"""

import requests
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dashboard_apis():
    """测试仪表板API端点"""
    base_url = "http://localhost:8088"
    
    print("🚀 测试增强仪表板API")
    print("=" * 50)
    
    # 测试API端点
    endpoints = [
        ("/api/stats", "统计数据"),
        ("/api/trending-games?limit=12", "趋势游戏"),
        ("/api/surge-alerts?limit=5", "爆发警报"),
        ("/api/proxy-status", "代理状态")
    ]
    
    for endpoint, description in endpoints:
        try:
            print(f"\n📊 测试 {description} ({endpoint})")
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功 - 返回数据: {len(str(data))} 字符")
                
                # 显示关键数据
                if 'trending_games' in data:
                    games = data['trending_games']
                    print(f"   📈 趋势游戏数量: {len(games)}")
                    if games:
                        top_game = games[0]
                        print(f"   🏆 顶级游戏: {top_game.get('name', 'Unknown')}")
                        print(f"   📊 增长率: {top_game.get('growth_rate', 0):.1f}%")
                        print(f"   🌡️  热度级别: {top_game.get('heat_level', 'unknown')}")
                
                elif 'alerts' in data:
                    alerts = data['alerts']
                    print(f"   ⚠️  警报数量: {len(alerts)}")
                    if alerts:
                        print(f"   🚨 最新警报: {alerts[0].get('alert_type', 'unknown')}")
                
                elif 'total_games' in data:
                    print(f"   🎮 总游戏数: {data['total_games']}")
                    print(f"   📅 今日新增: {data['today_games']}")
                    print(f"   📈 趋势中: {data['trending_count']}")
                
            else:
                print(f"❌ 失败 - 状态码: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 连接失败: {e}")
        except Exception as e:
            print(f"❌ 处理失败: {e}")

def test_trending_analysis():
    """测试趋势分析功能"""
    print("\n🔥 测试趋势分析")
    print("=" * 30)
    
    base_url = "http://localhost:8088"
    
    try:
        # 获取趋势游戏
        response = requests.get(f"{base_url}/api/trending-games?limit=5")
        
        if response.status_code == 200:
            data = response.json()
            games = data.get('trending_games', [])
            
            print(f"📊 分析 {len(games)} 个趋势游戏:")
            
            for i, game in enumerate(games, 1):
                name = game.get('name', 'Unknown')[:20]
                growth = game.get('growth_rate', 0)
                heat = game.get('heat_level', 'unknown')
                volume = game.get('volume_level', 'unknown')
                surge = game.get('is_surge', False)
                
                surge_icon = "🚀" if surge else "📈"
                
                print(f"  {i:2d}. {surge_icon} {name:<20} | "
                      f"增长: {growth:6.1f}% | "
                      f"热度: {heat:<12} | "
                      f"量级: {volume}")
                
                # 显示详细趋势数据
                if game.get('chart_data'):
                    chart = game['chart_data']
                    values = chart.get('values', [])
                    if values:
                        trend_direction = "📈" if values[-1] > values[0] else "📉"
                        print(f"      {trend_direction} 趋势数据: {len(values)}个点, "
                              f"范围: {min(values):.1f}-{max(values):.1f}")
        else:
            print(f"❌ 获取趋势数据失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 趋势分析失败: {e}")

def test_filters():
    """测试过滤器功能"""
    print("\n🔍 测试过滤器")
    print("=" * 20)
    
    base_url = "http://localhost:8088"
    
    filters = [
        ("explosive", "爆发增长 (200%+)"),
        ("viral", "病毒式传播 (100-200%)"),
        ("trending", "强势增长 (50-100%)"),
        ("moderate", "温和增长 (0-50%)")
    ]
    
    try:
        # 获取所有趋势游戏
        response = requests.get(f"{base_url}/api/trending-games?limit=50")
        
        if response.status_code == 200:
            data = response.json()
            all_games = data.get('trending_games', [])
            
            print(f"📊 从 {len(all_games)} 个游戏中筛选:")
            
            for filter_type, description in filters:
                filtered_count = 0
                
                for game in all_games:
                    growth = game.get('growth_rate', 0)
                    
                    if filter_type == "explosive" and growth > 200:
                        filtered_count += 1
                    elif filter_type == "viral" and 100 < growth <= 200:
                        filtered_count += 1
                    elif filter_type == "trending" and 50 < growth <= 100:
                        filtered_count += 1
                    elif filter_type == "moderate" and 0 < growth <= 50:
                        filtered_count += 1
                
                percentage = (filtered_count / len(all_games) * 100) if all_games else 0
                print(f"  🎯 {description:<20}: {filtered_count:3d} 个游戏 ({percentage:4.1f}%)")
        
    except Exception as e:
        print(f"❌ 过滤器测试失败: {e}")

def generate_mock_data():
    """生成模拟数据用于测试"""
    print("\n🎲 生成模拟测试数据")
    print("=" * 25)
    
    # 这里可以添加创建测试数据的代码
    print("💡 提示: 可以运行以下命令生成测试数据:")
    print("   python enhanced_trends_analyzer.py")
    print("   或者直接访问: http://localhost:8088/api/analyze-trends")

def test_page_accessibility():
    """测试页面可访问性"""
    print("\n🌐 测试页面访问")
    print("=" * 20)
    
    base_url = "http://localhost:8088"
    pages = [
        ("/", "增强首页"),
        ("/classic", "经典首页"),
        ("/games", "游戏列表"),
        ("/trends", "趋势分析")
    ]
    
    for path, description in pages:
        try:
            response = requests.get(f"{base_url}{path}", timeout=5)
            status = "✅ 可访问" if response.status_code == 200 else f"❌ 错误 {response.status_code}"
            print(f"  {description:<10}: {status}")
        except Exception as e:
            print(f"  {description:<10}: ❌ 连接失败")

if __name__ == "__main__":
    print("🧪 增强仪表板完整测试")
    print("=" * 60)
    
    # 测试页面访问
    test_page_accessibility()
    
    # 测试API端点
    test_dashboard_apis()
    
    # 测试趋势分析
    test_trending_analysis()
    
    # 测试过滤器
    test_filters()
    
    # 模拟数据建议
    generate_mock_data()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成!")
    print("\n💡 使用说明:")
    print("1. 访问 http://localhost:8088 查看增强仪表板")
    print("2. 使用过滤器筛选不同类型的游戏")
    print("3. 点击游戏卡片查看详细趋势图")
    print("4. 观察实时爆发警报和增长指标")
    print("5. 访问 /classic 查看经典版本对比")