#!/usr/bin/env python3
"""
测试监控系统功能
"""

import sys
import os
import time

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from scheduler_monitor import SchedulerMonitor
from src.notifications import FeishuBot

def test_monitor_status():
    """测试监控器状态检查"""
    print("=== 测试监控器状态检查 ===")
    
    monitor = SchedulerMonitor()
    
    # 检查所有调度器状态
    status = monitor.check_all_schedulers()
    
    print("\n调度器状态:")
    for name, info in status.items():
        print(f"\n{name}:")
        print(f"  运行中: {info['running']}")
        print(f"  PID: {info['pid']}")
        print(f"  CPU: {info['cpu_percent']}%")
        print(f"  内存: {info['memory_mb']:.1f}MB")
        print(f"  日志统计: {info['log_stats']}")

def test_monitor_report():
    """测试生成监控报告"""
    print("\n=== 测试生成监控报告 ===")
    
    monitor = SchedulerMonitor()
    
    # 生成测试报告
    print("生成监控报告...")
    monitor.generate_daily_report()
    print("报告生成完成")

def test_feishu_notification():
    """测试飞书通知"""
    print("\n=== 测试飞书通知 ===")
    
    bot = FeishuBot()
    
    # 测试文本消息
    text_msg = """📊 监控系统测试通知

这是一条测试消息，用于验证监控系统的通知功能。

当前时间: {}
系统状态: 正常
""".format(time.strftime('%Y-%m-%d %H:%M:%S'))
    
    result = bot.send_text_message(text_msg)
    print(f"文本消息发送: {'成功' if result else '失败'}")
    
    # 测试监控报告
    report_content = """**一、调度器运行状态**
✅ **爬虫调度器**
  • PID: 12345
  • CPU: 2.5%
  • 内存: 156.3MB
  • 最后执行: 2024-01-01 10:00:00
  • 执行次数: 24

✅ **Reddit分析**
  • PID: 12346
  • CPU: 1.8%
  • 内存: 203.7MB
  • 最后执行: 2024-01-01 10:30:00
  • 执行次数: 12

**二、业务数据统计**
• 总游戏数: 1,234
• 24小时新增: 56

**三、系统健康度**
• 健康指数: 100%
• 运行中: 5/5
"""
    
    result = bot.send_monitor_report(report_content)
    print(f"监控报告发送: {'成功' if result else '失败'}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试监控系统')
    parser.add_argument('--status', action='store_true', help='测试状态检查')
    parser.add_argument('--report', action='store_true', help='测试报告生成')
    parser.add_argument('--notify', action='store_true', help='测试飞书通知')
    parser.add_argument('--all', action='store_true', help='运行所有测试')
    
    args = parser.parse_args()
    
    if args.all or (not args.status and not args.report and not args.notify):
        test_monitor_status()
        test_feishu_notification()
        test_monitor_report()
    else:
        if args.status:
            test_monitor_status()
        if args.notify:
            test_feishu_notification()
        if args.report:
            test_monitor_report()

if __name__ == "__main__":
    main()