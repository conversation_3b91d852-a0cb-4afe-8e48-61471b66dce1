"""
调试趋势页面数据问题
"""

from app import create_app
from models import db, Game, RedditMetric, Trend
from datetime import datetime, timedelta
import json

def debug_trends_data():
    """调试趋势数据"""
    app = create_app()
    
    with app.app_context():
        print("=== 数据库状态 ===")
        
        # 统计
        total_games = Game.query.filter_by(is_active=True).count()
        total_reddit = RedditMetric.query.count()
        total_trends = Trend.query.count()
        
        print(f"活跃游戏数: {total_games}")
        print(f"Reddit记录数: {total_reddit}")
        print(f"Trends记录数: {total_trends}")
        
        # 检查最新的Reddit数据
        print("\n=== 最新Reddit数据 (前5条) ===")
        recent_reddit = db.session.query(
            Game.name,
            RedditMetric.heat_score,
            RedditMetric.post_count_24h,
            RedditMetric.created_at
        ).join(
            Game, RedditMetric.game_id == Game.id
        ).order_by(
            RedditMetric.created_at.desc()
        ).limit(5).all()
        
        for game_name, heat, posts, created in recent_reddit:
            print(f"{game_name}: 热度={heat}, 帖子数={posts}, 时间={created}")
        
        # 检查有数据的游戏
        print("\n=== 有Reddit数据的游戏 ===")
        games_with_data = db.session.query(
            Game.id,
            Game.name,
            db.func.max(RedditMetric.heat_score).label('max_heat'),
            db.func.count(RedditMetric.id).label('record_count')
        ).join(
            RedditMetric, Game.id == RedditMetric.game_id
        ).group_by(
            Game.id, Game.name
        ).having(
            db.func.max(RedditMetric.heat_score) > 0
        ).all()
        
        for game_id, name, max_heat, count in games_with_data:
            print(f"ID={game_id}, {name}: 最高热度={max_heat}, 记录数={count}")
        
        # 测试API端点数据
        print("\n=== 测试API端点逻辑 ===")
        
        # 模拟API查询
        test_games = Game.query.filter_by(is_active=True).limit(5).all()
        
        for game in test_games:
            # 获取最新Reddit数据
            reddit_metric = RedditMetric.query.filter_by(
                game_id=game.id
            ).order_by(
                RedditMetric.created_at.desc()
            ).first()
            
            # 获取最新Trend数据
            trend = Trend.query.filter_by(
                game_id=game.id
            ).order_by(
                Trend.created_at.desc()
            ).first()
            
            print(f"\n游戏: {game.name} (ID={game.id})")
            if reddit_metric:
                print(f"  Reddit: 热度={reddit_metric.heat_score}, 状态={reddit_metric.status}")
            else:
                print(f"  Reddit: 无数据")
                
            if trend:
                print(f"  Trend: 当前值={trend.current_value}, 增长率={trend.growth_rate}")
            else:
                print(f"  Trend: 无数据")


if __name__ == "__main__":
    debug_trends_data()