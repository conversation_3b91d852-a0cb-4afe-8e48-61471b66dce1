#!/usr/bin/env python3
"""
快速环境测试脚本
验证所有必要的包都已正确安装
"""

import sys
import os

def test_imports():
    """测试所有必要的导入"""
    print("🧪 测试 Python 包导入...")
    print(f"Python 版本: {sys.version}")
    print(f"虚拟环境: {os.environ.get('VIRTUAL_ENV', '未激活')}\n")
    
    packages = [
        ('flask', 'Flask Web框架'),
        ('praw', 'Reddit API'),
        ('pytrends', 'Google Trends API'),
        ('pymysql', 'MySQL连接'),
        ('redis', 'Redis缓存'),
        ('requests', 'HTTP请求'),
        ('pandas', '数据分析'),
        ('sqlalchemy', 'ORM框架')
    ]
    
    success = True
    
    for package, description in packages:
        try:
            __import__(package)
            print(f"✅ {package:<15} - {description}")
        except ImportError as e:
            print(f"❌ {package:<15} - {description} (错误: {e})")
            success = False
    
    return success


def test_env_vars():
    """测试环境变量"""
    print("\n🔐 检查环境变量...")
    
    # 加载 .env 文件
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ 成功加载 .env 文件")
    except ImportError:
        print("⚠️  python-dotenv 未安装，跳过 .env 加载")
    
    env_vars = [
        'REDDIT_CLIENT_ID',
        'REDDIT_CLIENT_SECRET',
        'MYSQL_HOST',
        'MYSQL_DATABASE',
        'FEISHU_WEBHOOK_URL'
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            if 'SECRET' in var or 'PASSWORD' in var:
                print(f"✅ {var:<25} = ***已设置***")
            else:
                print(f"✅ {var:<25} = {value[:20]}...")
        else:
            print(f"❌ {var:<25} = 未设置")


def test_reddit_connection():
    """测试 Reddit 连接"""
    print("\n🌐 测试 Reddit API 连接...")
    
    try:
        import praw
        
        client_id = os.getenv('REDDIT_CLIENT_ID')
        client_secret = os.getenv('REDDIT_CLIENT_SECRET')
        
        if not client_id or not client_secret:
            print("❌ Reddit API 凭据未设置")
            print("请在 .env 文件中设置:")
            print("  REDDIT_CLIENT_ID=your_client_id")
            print("  REDDIT_CLIENT_SECRET=your_client_secret")
            return False
        
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent='GameMonitor/1.0 Test'
        )
        
        # 测试访问公共子版块
        subreddit = reddit.subreddit('gaming')
        print(f"✅ 成功连接到 Reddit")
        print(f"   子版块: r/{subreddit.display_name}")
        print(f"   订阅者: {subreddit.subscribers:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ Reddit 连接失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 50)
    print("🚀 游戏监控系统 - 环境测试")
    print("=" * 50)
    
    # 测试导入
    imports_ok = test_imports()
    
    # 测试环境变量
    test_env_vars()
    
    # 测试 Reddit
    test_reddit_connection()
    
    print("\n" + "=" * 50)
    if imports_ok:
        print("✅ 环境配置基本正常")
        print("\n下一步:")
        print("1. 确保 .env 文件中的 Reddit API 凭据正确")
        print("2. 运行完整测试: python test_reddit_integration.py")
        print("3. 启动应用: python app.py")
    else:
        print("❌ 环境配置有问题，请安装缺失的包:")
        print("   pip install -r requirements.txt")


if __name__ == "__main__":
    main()