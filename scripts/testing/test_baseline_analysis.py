#!/usr/bin/env python3
"""
测试多基准线趋势分析功能
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.enhanced_trends_analyzer import EnhancedTrendsAnalyzer
from models import Game
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_baseline_analysis():
    """测试基准线分析功能"""
    print("🚀 测试多基准线趋势分析")
    print("=" * 50)
    
    try:
        # 初始化分析器
        analyzer = EnhancedTrendsAnalyzer()
        
        # 测试基准词汇池
        print(f"📊 基准词汇池 ({len(analyzer.baseline_keywords)}个):")
        for i, keyword in enumerate(analyzer.baseline_keywords, 1):
            volume_level = analyzer._get_keyword_volume_level(keyword)
            print(f"   {i:2d}. {keyword:<20} (级别: {volume_level})")
        
        print("\n" + "=" * 50)
        
        # 获取基准数据
        print("📈 获取基准数据...")
        baseline_data = analyzer.get_baseline_data()
        
        if baseline_data:
            print(f"✅ 成功获取 {len(baseline_data)} 个基准词汇数据")
            print("\n基准数据预览:")
            for keyword, metrics in list(baseline_data.items())[:3]:
                print(f"  {keyword}:")
                print(f"    当前平均值: {metrics['current_avg']:.2f}")
                print(f"    整体平均值: {metrics['overall_avg']:.2f}")
                print(f"    最大值: {metrics['max_value']:.2f}")
                print(f"    稳定性: {metrics['stability']:.2f}")
        else:
            print("❌ 获取基准数据失败")
            return False
        
        print("\n" + "=" * 50)
        
        # 模拟游戏关键词分析
        test_games = [
            "subway surfers",
            "pokemon go", 
            "candy crush",
            "among us",
            "wordle"
        ]
        
        print("🎮 测试游戏关键词分析:")
        for game_name in test_games:
            try:
                print(f"\n分析 '{game_name}'...")
                
                # 获取趋势数据
                trends_df = analyzer.get_trends_data_with_retry([game_name], max_retries=1)
                
                if trends_df is not None and not trends_df.empty and game_name in trends_df.columns:
                    # 计算指标
                    metrics = analyzer.calculate_advanced_growth_metrics(trends_df, game_name)
                    
                    if metrics:
                        print(f"  📈 自身增长率: {metrics.get('self_growth_rate', 0):.1f}%")
                        print(f"  🔥 综合增长率: {metrics.get('growth_rate', 0):.1f}%")
                        print(f"  🌡️  热度级别: {metrics.get('heat_level', 'unknown')}")
                        print(f"  📊 搜索量级别: {metrics.get('volume_level', 'unknown')}")
                        print(f"  ⚡ 相对热度: {metrics.get('relative_heat', 1.0):.2f}")
                        print(f"  🎯 趋势强度: {metrics.get('trend_strength', 'unknown')}")
                        
                        # 显示基准对比详情
                        comparisons = metrics.get('benchmark_comparison', {})
                        if comparisons:
                            print("  📋 基准对比 (前3个):")
                            for i, (base_kw, comp) in enumerate(list(comparisons.items())[:3], 1):
                                ratio = comp['ratio']
                                status = "高于" if ratio > 1.0 else "低于"
                                print(f"    {i}. vs {base_kw}: {ratio:.2f}x ({status})")
                    else:
                        print("  ❌ 指标计算失败")
                else:
                    print("  ❌ 趋势数据获取失败")
                    
            except Exception as e:
                print(f"  ❌ 分析失败: {e}")
                continue
        
        print("\n" + "=" * 50)
        print("✅ 多基准线分析测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_volume_level_detection():
    """测试搜索量级别检测"""
    print("\n🎯 测试搜索量级别检测")
    print("=" * 30)
    
    analyzer = EnhancedTrendsAnalyzer()
    
    test_cases = [
        ("good morning images", "ultra_high"),
        ("happy birthday image", "high"), 
        ("baby shower", "medium"),
        ("image to text converter", "low"),
        ("gpts", "very_low"),
        ("unknown game", "unknown")
    ]
    
    for keyword, expected_level in test_cases:
        detected_level = analyzer._get_keyword_volume_level(keyword)
        status = "✅" if detected_level == expected_level else "❌"
        print(f"{status} {keyword:<25} -> {detected_level:<10} (期望: {expected_level})")

def test_heat_level_calculation():
    """测试热度级别计算"""
    print("\n🌡️  测试热度级别计算")
    print("=" * 30)
    
    analyzer = EnhancedTrendsAnalyzer()
    
    test_cases = [
        (15.0, "ultra_high", "extremely_hot"),
        (3.0, "high", "very_hot"),
        (1.5, "medium", "hot"),
        (0.8, "low", "cool"),
        (0.3, "very_low", "cold")
    ]
    
    for heat_score, volume_level, expected_heat in test_cases:
        calculated_heat = analyzer._determine_heat_level(heat_score, volume_level)
        status = "✅" if calculated_heat == expected_heat else "❌"
        print(f"{status} 分数:{heat_score:4.1f}, 级别:{volume_level:<10} -> {calculated_heat:<12} (期望: {expected_heat})")

if __name__ == "__main__":
    print("🧪 多基准线趋势分析测试")
    print("=" * 60)
    
    # 测试搜索量级别检测
    test_volume_level_detection()
    
    # 测试热度级别计算
    test_heat_level_calculation()
    
    # 测试完整分析流程
    success = test_baseline_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接")