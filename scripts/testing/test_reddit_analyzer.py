"""
Reddit分析器单元测试
测试Reddit游戏热度分析的各项功能
"""

import unittest
import asyncio
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from reddit_analyzer import RedditAnalyzer, RedditMetrics
import os


class TestRedditAnalyzer(unittest.TestCase):
    """Reddit分析器测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 使用测试凭据
        self.analyzer = RedditAnalyzer(
            client_id='test_client_id',
            client_secret='test_client_secret',
            user_agent='TestBot/1.0'
        )
        
        # Mock Reddit客户端
        self.mock_reddit = Mock()
        self.analyzer.reddit = self.mock_reddit
    
    def test_init_without_credentials(self):
        """测试无凭据初始化"""
        # 清除环境变量
        env_backup = {
            'REDDIT_CLIENT_ID': os.environ.get('REDDIT_CLIENT_ID'),
            'REDDIT_CLIENT_SECRET': os.environ.get('REDDIT_CLIENT_SECRET')
        }
        
        try:
            if 'REDDIT_CLIENT_ID' in os.environ:
                del os.environ['REDDIT_CLIENT_ID']
            if 'REDDIT_CLIENT_SECRET' in os.environ:
                del os.environ['REDDIT_CLIENT_SECRET']
            
            # 应该抛出错误
            with self.assertRaises(ValueError) as context:
                RedditAnalyzer()
            
            self.assertIn('Reddit API credentials not provided', str(context.exception))
        finally:
            # 恢复环境变量
            for key, value in env_backup.items():
                if value is not None:
                    os.environ[key] = value
    
    def test_calculate_heat_score(self):
        """测试热度分数计算"""
        # 测试用例1：低活跃度
        metrics_low = {
            'post_count': 2,
            'total_upvotes': 50,
            'total_comments': 10,
            'unique_authors': 2
        }
        score_low = self.analyzer._calculate_heat_score(metrics_low)
        self.assertLessEqual(score_low, 20)
        
        # 测试用例2：中等活跃度
        metrics_medium = {
            'post_count': 15,
            'total_upvotes': 1500,
            'total_comments': 300,
            'unique_authors': 10
        }
        score_medium = self.analyzer._calculate_heat_score(metrics_medium)
        self.assertGreater(score_medium, 30)
        self.assertLessEqual(score_medium, 70)
        
        # 测试用例3：高活跃度
        metrics_high = {
            'post_count': 50,
            'total_upvotes': 8000,
            'total_comments': 2000,
            'unique_authors': 40
        }
        score_high = self.analyzer._calculate_heat_score(metrics_high)
        self.assertGreater(score_high, 80)
        self.assertLessEqual(score_high, 100)
    
    def test_analyze_sentiment(self):
        """测试情感分析"""
        # 正面情感测试
        positive_posts = [
            {'title': 'This game is amazing and fun!', 'score': 100, 'selftext': 'I love it'},
            {'title': 'Best game ever, absolutely fantastic', 'score': 200, 'selftext': ''},
            {'title': 'Awesome gameplay', 'score': 50, 'selftext': 'Great experience'}
        ]
        sentiment_pos = self.analyzer._analyze_sentiment(positive_posts)
        self.assertEqual(sentiment_pos, 'positive')
        
        # 负面情感测试
        negative_posts = [
            {'title': 'This game is terrible and boring', 'score': 80, 'selftext': 'Hate it'},
            {'title': 'Worst game, completely broken', 'score': 150, 'selftext': 'Awful'},
            {'title': 'Disappointing and buggy', 'score': 30, 'selftext': ''}
        ]
        sentiment_neg = self.analyzer._analyze_sentiment(negative_posts)
        self.assertEqual(sentiment_neg, 'negative')
        
        # 中性情感测试
        neutral_posts = [
            {'title': 'New game released', 'score': 50, 'selftext': 'Available now'},
            {'title': 'Game update 1.2', 'score': 30, 'selftext': 'Patch notes'},
            {'title': 'Looking for players', 'score': 20, 'selftext': ''}
        ]
        sentiment_neu = self.analyzer._analyze_sentiment(neutral_posts)
        self.assertEqual(sentiment_neu, 'neutral')
    
    def test_calculate_metrics(self):
        """测试指标计算"""
        # 测试空列表
        empty_metrics = self.analyzer._calculate_metrics([])
        self.assertEqual(empty_metrics['total_upvotes'], 0)
        self.assertEqual(empty_metrics['total_comments'], 0)
        self.assertEqual(empty_metrics['avg_comments'], 0)
        self.assertEqual(empty_metrics['unique_authors'], 0)
        
        # 测试正常数据
        posts = [
            {'score': 100, 'num_comments': 20, 'author': 'user1'},
            {'score': 200, 'num_comments': 30, 'author': 'user2'},
            {'score': 50, 'num_comments': 10, 'author': 'user1'},
            {'score': 150, 'num_comments': 40, 'author': 'user3'},
            {'score': 0, 'num_comments': 0, 'author': '[deleted]'}
        ]
        
        metrics = self.analyzer._calculate_metrics(posts)
        self.assertEqual(metrics['total_upvotes'], 500)
        self.assertEqual(metrics['total_comments'], 100)
        self.assertEqual(metrics['avg_comments'], 20)
        self.assertEqual(metrics['unique_authors'], 3)  # 不包括[deleted]
    
    @patch('reddit_analyzer.praw.Reddit')
    async def test_analyze_game_success(self, mock_praw):
        """测试成功分析游戏"""
        # 设置Mock数据
        mock_submission = Mock()
        mock_submission.id = 'test123'
        mock_submission.title = 'Amazing game!'
        mock_submission.score = 500
        mock_submission.num_comments = 50
        mock_submission.author = Mock()
        mock_submission.author.__str__ = Mock(return_value='testuser')
        mock_submission.permalink = '/r/gaming/test123'
        mock_submission.created_utc = datetime.now().timestamp()
        mock_submission.selftext = 'This is a great game'
        
        # 配置Mock Reddit搜索
        mock_subreddit = Mock()
        mock_subreddit.search.return_value = [mock_submission]
        self.mock_reddit.subreddit.return_value = mock_subreddit
        
        # 执行分析
        result = await self.analyzer.analyze_game('Test Game')
        
        # 验证结果
        self.assertIsInstance(result, RedditMetrics)
        self.assertEqual(result.game_name, 'Test Game')
        self.assertEqual(result.status, 'success')
        self.assertGreater(result.heat_score, 0)
        self.assertEqual(result.post_count_24h, 1)
        self.assertEqual(result.total_upvotes_24h, 500)
        self.assertEqual(result.top_post_title, 'Amazing game!')
    
    async def test_analyze_game_no_data(self):
        """测试无数据情况"""
        # 配置Mock返回空结果
        mock_subreddit = Mock()
        mock_subreddit.search.return_value = []
        self.mock_reddit.subreddit.return_value = mock_subreddit
        
        # 执行分析
        result = await self.analyzer.analyze_game('Unknown Game')
        
        # 验证结果
        self.assertEqual(result.status, 'no_data')
        self.assertEqual(result.heat_score, 0)
        self.assertEqual(result.post_count_24h, 0)
        self.assertIn('No discussions found', result.error_message)
    
    async def test_analyze_game_with_error(self):
        """测试错误处理"""
        # 配置Mock抛出异常
        self.mock_reddit.subreddit.side_effect = Exception('API Error')
        
        # 执行分析
        result = await self.analyzer.analyze_game('Error Game')
        
        # 验证结果
        self.assertEqual(result.status, 'failed')
        self.assertEqual(result.heat_score, 0)
        self.assertIn('API Error', result.error_message)
    
    def test_subreddit_distribution(self):
        """测试子版块分布分析"""
        posts = [
            {'subreddit': 'gaming'},
            {'subreddit': 'gaming'},
            {'subreddit': 'IndieGaming'},
            {'subreddit': 'WebGames'},
            {'subreddit': 'gaming'}
        ]
        
        distribution = self.analyzer._analyze_subreddit_distribution(posts)
        
        self.assertEqual(distribution['gaming'], 3)
        self.assertEqual(distribution['IndieGaming'], 1)
        self.assertEqual(distribution['WebGames'], 1)
    
    async def test_growth_rate_calculation(self):
        """测试增长率计算"""
        # 初始化历史数据
        game_name = 'Test Game'
        
        # 添加历史数据点
        for i in range(5):
            historical_metrics = {'total_upvotes': 100 + i * 10}
            self.analyzer._history_cache[game_name].append({
                'timestamp': datetime.now() - timedelta(days=5-i),
                'metrics': historical_metrics
            })
        
        # 当前指标（显著增长）
        current_metrics = {'total_upvotes': 300}
        
        # 计算增长率
        growth_rate = await self.analyzer._calculate_growth_rate(game_name, current_metrics)
        
        # 验证增长率为正
        self.assertGreater(growth_rate, 100)  # 预期超过100%增长
    
    def test_validate_subreddit(self):
        """测试子版块验证"""
        # Mock成功情况
        mock_subreddit = Mock()
        mock_subreddit.id = 'valid_id'
        self.mock_reddit.subreddit.return_value = mock_subreddit
        
        # 测试有效子版块
        is_valid = self.analyzer.validate_subreddit('gaming')
        self.assertTrue(is_valid)
        
        # Mock失败情况
        self.mock_reddit.subreddit.side_effect = Exception('Not found')
        
        # 测试无效子版块
        is_valid = self.analyzer.validate_subreddit('invalid_sub')
        self.assertFalse(is_valid)
    
    def test_reddit_metrics_to_dict(self):
        """测试RedditMetrics转字典"""
        metrics = RedditMetrics(
            game_name='Test Game',
            heat_score=75,
            growth_rate=120.5,
            post_count_24h=10,
            total_upvotes_24h=1000,
            total_comments_24h=200,
            top_post_title='Great game!',
            top_post_score=500,
            top_post_url='https://reddit.com/r/gaming/123',
            avg_comments=20.0,
            unique_authors=8,
            sentiment='positive',
            subreddit_distribution={'gaming': 5, 'IndieGaming': 3},
            last_updated=datetime.now()
        )
        
        dict_data = metrics.to_dict()
        
        # 验证所有字段
        self.assertEqual(dict_data['game_name'], 'Test Game')
        self.assertEqual(dict_data['heat_score'], 75)
        self.assertEqual(dict_data['growth_rate'], 120.5)
        self.assertEqual(dict_data['sentiment'], 'positive')
        self.assertIsInstance(dict_data['last_updated'], str)  # ISO格式字符串


def run_async_test(coro):
    """运行异步测试的辅助函数"""
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)


# 为异步测试方法添加装饰器
for attr_name in dir(TestRedditAnalyzer):
    attr = getattr(TestRedditAnalyzer, attr_name)
    if callable(attr) and attr_name.startswith('test_') and asyncio.iscoroutinefunction(attr):
        setattr(TestRedditAnalyzer, attr_name, 
                lambda self, coro=attr: run_async_test(coro(self)))


if __name__ == '__main__':
    # 设置测试环境变量
    os.environ['REDDIT_CLIENT_ID'] = 'test_id'
    os.environ['REDDIT_CLIENT_SECRET'] = 'test_secret'
    
    # 运行测试
    unittest.main(verbosity=2)