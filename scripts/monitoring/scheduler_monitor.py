#!/usr/bin/env python3
"""
统一的调度器监控程序
监控所有调度器的运行状态并定期发送报告
"""

import os
import sys
import json
import psutil
import schedule
import time
import logging
from datetime import datetime, timedelta
from collections import defaultdict
import re

# 添加项目路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
sys.path.insert(0, project_root)

from config import Config
from src.notifications import FeishuBot
from app import create_app
from models import db, Game, Trend, RedditMetric, Alert

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 调度器配置
SCHEDULERS = {
    'scraper': {
        'name': '爬虫调度器',
        'log_file': 'scheduler_scraper.log',
        'key_metrics': ['新游戏', '更新', '错误']
    },
    'reddit': {
        'name': 'Reddit分析',
        'log_file': 'scheduler_reddit.log',
        'key_metrics': ['成功', '失败', '高热度游戏']
    },
    'trends': {
        'name': '趋势分析',
        'log_file': 'scheduler_trends.log',
        'key_metrics': ['分析游戏数', '爆发游戏数']
    },
    'reports': {
        'name': '报告通知',
        'log_file': 'scheduler_reports.log',
        'key_metrics': ['报告发送', '通知发送']
    }
}

PID_FILE_PREFIX = '/tmp/game_monitor_scheduler_'


class SchedulerMonitor:
    """调度器监控器"""
    
    def __init__(self):
        self.running = False
        self.app = create_app()
        self.bot = FeishuBot()
        self.stats = defaultdict(lambda: defaultdict(int))
        self.last_check = {}
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        
        # 监控数据存储文件
        self.stats_file = 'data/monitor_stats.json'
        os.makedirs('data', exist_ok=True)
        self.load_stats()
    
    def load_stats(self):
        """加载历史统计数据"""
        try:
            if os.path.exists(self.stats_file):
                with open(self.stats_file, 'r') as f:
                    saved_stats = json.load(f)
                    for scheduler, data in saved_stats.items():
                        self.stats[scheduler].update(data)
        except Exception as e:
            logger.error(f"加载统计数据失败: {e}")
    
    def save_stats(self):
        """保存统计数据"""
        try:
            with open(self.stats_file, 'w') as f:
                json.dump(dict(self.stats), f, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存统计数据失败: {e}")
    
    def get_pid_file(self, scheduler_name):
        """获取PID文件路径"""
        return f"{PID_FILE_PREFIX}{scheduler_name}.pid"
    
    def check_scheduler_status(self, scheduler_name):
        """检查单个调度器状态"""
        pid_file = self.get_pid_file(scheduler_name)
        
        # 检查进程是否运行
        is_running = False
        pid = None
        cpu_percent = 0
        memory_mb = 0
        
        if os.path.exists(pid_file):
            try:
                with open(pid_file, 'r') as f:
                    pid = int(f.read().strip())
                
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    if 'python' in process.name().lower():
                        is_running = True
                        cpu_percent = process.cpu_percent(interval=0.1)
                        memory_mb = process.memory_info().rss / 1024 / 1024
            except:
                pass
        
        # 解析日志获取执行统计
        log_stats = self.parse_log_stats(scheduler_name)
        
        return {
            'running': is_running,
            'pid': pid,
            'cpu_percent': cpu_percent,
            'memory_mb': memory_mb,
            'log_stats': log_stats,
            'last_update': datetime.now()
        }
    
    def parse_log_stats(self, scheduler_name):
        """解析日志文件获取统计信息"""
        stats = {
            'executions': 0,
            'errors': 0,
            'last_execution': None,
            'metrics': {}
        }
        
        # 优先检查管理器日志，因为实际日志输出在那里
        manager_log = f"logs/{scheduler_name}_manager.log"
        scheduler_log = f"logs/{SCHEDULERS[scheduler_name]['log_file']}"
        
        log_file = manager_log if os.path.exists(manager_log) else scheduler_log
        if not os.path.exists(log_file):
            return stats
        
        try:
            # 读取最近的日志行
            with open(log_file, 'r') as f:
                lines = f.readlines()[-1000:]  # 最近1000行
            
            for line in lines:
                # 统计执行次数
                if '开始执行' in line or '任务开始' in line:
                    stats['executions'] += 1
                    # 提取时间
                    time_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if time_match:
                        stats['last_execution'] = time_match.group(1)
                
                # 统计错误
                if 'ERROR' in line or '失败' in line:
                    stats['errors'] += 1
                
                # 提取特定指标
                if scheduler_name == 'scraper':
                    if match := re.search(r'新游戏=(\d+)', line):
                        stats['metrics']['new_games'] = int(match.group(1))
                    if match := re.search(r'更新=(\d+)', line):
                        stats['metrics']['updated'] = int(match.group(1))
                
                elif scheduler_name == 'reddit':
                    if match := re.search(r'成功=(\d+)', line):
                        stats['metrics']['success'] = int(match.group(1))
                    if match := re.search(r'高热度游戏.*?(\d+)', line):
                        stats['metrics']['high_heat'] = int(match.group(1))
                
                elif scheduler_name == 'trends':
                    if match := re.search(r'分析游戏数=(\d+)', line):
                        stats['metrics']['analyzed'] = int(match.group(1))
                    if match := re.search(r'爆发游戏数=(\d+)', line):
                        stats['metrics']['surge'] = int(match.group(1))
                
                elif scheduler_name == 'reports':
                    if '报告发送成功' in line:
                        stats['metrics']['reports_sent'] = stats['metrics'].get('reports_sent', 0) + 1
        
        except Exception as e:
            logger.error(f"解析日志失败 {scheduler_name}: {e}")
        
        return stats
    
    def check_all_schedulers(self):
        """检查所有调度器状态"""
        logger.info("开始检查所有调度器状态...")
        
        all_status = {}
        alerts = []
        
        for scheduler_name in SCHEDULERS:
            status = self.check_scheduler_status(scheduler_name)
            all_status[scheduler_name] = status
            
            # 更新统计
            self.stats[scheduler_name]['total_checks'] += 1
            if status['running']:
                self.stats[scheduler_name]['running_checks'] += 1
            else:
                # 检查是否需要告警
                if self.last_check.get(scheduler_name, {}).get('running', True):
                    alerts.append(f"{SCHEDULERS[scheduler_name]['name']}已停止运行！")
            
            self.last_check[scheduler_name] = status
        
        # 发送告警
        if alerts:
            self.send_alert_notification(alerts)
        
        # 保存统计
        self.save_stats()
        
        return all_status
    
    def send_alert_notification(self, alerts):
        """发送告警通知"""
        try:
            message = "⚠️ **调度器异常告警**\n\n"
            for alert in alerts:
                message += f"• {alert}\n"
            
            message += f"\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            self.bot.send_text_message(message)
            logger.warning(f"发送告警: {alerts}")
        except Exception as e:
            logger.error(f"发送告警失败: {e}")
    
    def generate_daily_report(self):
        """生成每日监控报告"""
        logger.info("生成每日监控报告...")
        
        with self.app.app_context():
            try:
                # 获取当前状态
                current_status = self.check_all_schedulers()
                
                # 获取业务数据统计
                today = datetime.utcnow().date()
                yesterday = today - timedelta(days=1)
                
                # 游戏数据
                total_games = Game.query.filter_by(is_active=True).count()
                new_games_24h = Game.query.filter(
                    Game.created_at >= yesterday
                ).count()
                
                # Reddit热度TOP5
                hot_reddit = db.session.query(
                    Game.name,
                    RedditMetric.heat_score
                ).join(RedditMetric).filter(
                    RedditMetric.created_at >= yesterday,
                    RedditMetric.heat_score > 50
                ).order_by(
                    RedditMetric.heat_score.desc()
                ).limit(5).all()
                
                # 趋势爆发游戏
                surge_games = db.session.query(
                    Game.name,
                    Trend.growth_rate
                ).join(Trend).filter(
                    Trend.created_at >= yesterday,
                    Trend.growth_rate > Config.SURGE_THRESHOLD
                ).order_by(
                    Trend.growth_rate.desc()
                ).limit(5).all()
                
                # 生成报告
                report = self._format_monitor_report(
                    current_status, 
                    {
                        'total_games': total_games,
                        'new_games_24h': new_games_24h,
                        'hot_reddit': hot_reddit,
                        'surge_games': surge_games
                    }
                )
                
                # 发送报告
                self.bot.send_monitor_report(report)
                logger.info("监控报告发送成功")
                
            except Exception as e:
                logger.error(f"生成监控报告失败: {e}", exc_info=True)
    
    def _format_monitor_report(self, status, business_data):
        """格式化监控报告"""
        lines = [
            "📊 **游戏监控系统日报**",
            f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            "",
            "**一、调度器运行状态**"
        ]
        
        # 调度器状态
        for name, info in status.items():
            status_icon = "✅" if info['running'] else "❌"
            scheduler_info = SCHEDULERS[name]['name']
            
            lines.append(f"{status_icon} **{scheduler_info}**")
            
            if info['running']:
                lines.append(f"  • PID: {info['pid']}")
                lines.append(f"  • CPU: {info['cpu_percent']:.1f}%")
                lines.append(f"  • 内存: {info['memory_mb']:.1f}MB")
            
            log_stats = info['log_stats']
            if log_stats['last_execution']:
                lines.append(f"  • 最后执行: {log_stats['last_execution']}")
            lines.append(f"  • 执行次数: {log_stats['executions']}")
            if log_stats['errors'] > 0:
                lines.append(f"  • ⚠️ 错误数: {log_stats['errors']}")
            
            # 显示关键指标
            if log_stats['metrics']:
                for key, value in log_stats['metrics'].items():
                    lines.append(f"  • {key}: {value}")
            lines.append("")
        
        # 业务数据
        lines.extend([
            "**二、业务数据统计**",
            f"• 总游戏数: {business_data['total_games']}",
            f"• 24小时新增: {business_data['new_games_24h']}",
            ""
        ])
        
        # Reddit热门
        if business_data['hot_reddit']:
            lines.append("**🔥 Reddit热度TOP5**")
            for i, (name, score) in enumerate(business_data['hot_reddit'], 1):
                lines.append(f"{i}. {name} (热度: {score})")
            lines.append("")
        
        # 趋势爆发
        if business_data['surge_games']:
            lines.append("**📈 趋势爆发游戏**")
            for i, (name, growth) in enumerate(business_data['surge_games'], 1):
                lines.append(f"{i}. {name} (增长: {growth:.1f}%)")
            lines.append("")
        
        # 系统健康度
        running_count = sum(1 for s in status.values() if s['running'])
        health_score = (running_count / len(status)) * 100
        
        lines.extend([
            "**三、系统健康度**",
            f"• 健康指数: {health_score:.0f}%",
            f"• 运行中: {running_count}/{len(status)}",
            ""
        ])
        
        # 运行时间统计
        lines.append("**四、24小时运行统计**")
        for name, stats in self.stats.items():
            if stats['total_checks'] > 0:
                uptime = (stats['running_checks'] / stats['total_checks']) * 100
                lines.append(f"• {SCHEDULERS[name]['name']}: {uptime:.1f}% 在线")
        
        return "\n".join(lines)
    
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置监控定时任务...")
        
        # 定期检查状态（每30分钟）
        schedule.every(30).minutes.do(self.check_all_schedulers)
        
        # 每日报告
        report_time = getattr(Config, 'MONITOR_REPORT_TIME', '09:00')
        schedule.every().day.at(report_time).do(self.generate_daily_report)
        
        # 每小时快速检查
        schedule.every().hour.do(self.quick_health_check)
        
        logger.info(f"监控任务设置完成")
        logger.info(f"状态检查: 每30分钟")
        logger.info(f"每日报告: {report_time}")
    
    def quick_health_check(self):
        """快速健康检查"""
        not_running = []
        for scheduler_name in SCHEDULERS:
            pid_file = self.get_pid_file(scheduler_name)
            if os.path.exists(pid_file):
                try:
                    with open(pid_file, 'r') as f:
                        pid = int(f.read().strip())
                    if not psutil.pid_exists(pid):
                        not_running.append(SCHEDULERS[scheduler_name]['name'])
                except:
                    not_running.append(SCHEDULERS[scheduler_name]['name'])
            else:
                not_running.append(SCHEDULERS[scheduler_name]['name'])
        
        if not_running:
            self.send_alert_notification([f"{name}未运行" for name in not_running])
    
    def start(self):
        """启动监控器"""
        logger.info("启动调度器监控器...")
        
        try:
            self.setup_schedule()
            
            # 启动时执行一次检查
            self.check_all_schedulers()
            
            self.running = True
            logger.info("监控器启动成功")
            
            # 主循环
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("监控器被用户停止")
            self.stop()
        except Exception as e:
            logger.error(f"监控器错误: {e}")
            self.stop()
    
    def stop(self):
        """停止监控器"""
        logger.info("停止监控器...")
        self.running = False
        self.save_stats()
        schedule.clear()
        logger.info("监控器已停止")


def main():
    """主函数"""
    monitor = SchedulerMonitor()
    monitor.start()


if __name__ == "__main__":
    main()