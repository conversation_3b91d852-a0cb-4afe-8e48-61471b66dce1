#!/usr/bin/env python3
"""
调度器管理工具
用于启动、停止和监控独立的调度器进程
"""

import os
import sys
import signal
import subprocess
import psutil
import argparse
from datetime import datetime
import json

# 调度器配置
SCHEDULERS = {
    'scraper': {
        'script': 'src/scheduler_scraper.py',
        'name': '爬虫调度器',
        'description': '负责定期爬取游戏网站数据'
    },
    'reddit': {
        'script': 'src/scheduler_reddit.py',
        'name': 'Reddit分析调度器',
        'description': '负责分析Reddit游戏讨论热度'
    },
    'trends': {
        'script': 'src/scheduler_trends.py',
        'name': '趋势分析调度器',
        'description': '负责Google趋势分析'
    },
    'reports': {
        'script': 'src/scheduler_reports.py',
        'name': '报告调度器',
        'description': '负责发送日报和通知'
    },
    'monitor': {
        'script': 'scripts/monitoring/scheduler_monitor.py',
        'name': '监控调度器',
        'description': '负责监控所有调度器状态'
    }
}

PID_FILE_PREFIX = '/tmp/game_monitor_scheduler_'


class SchedulerManager:
    """调度器管理器"""
    
    def __init__(self):
        # 获取项目根目录（从scripts/monitoring/向上两级）
        self.base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    def get_pid_file(self, scheduler_name):
        """获取PID文件路径"""
        return f"{PID_FILE_PREFIX}{scheduler_name}.pid"
    
    def is_running(self, scheduler_name):
        """检查调度器是否运行中"""
        pid_file = self.get_pid_file(scheduler_name)
        if not os.path.exists(pid_file):
            return False
        
        try:
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否存在
            if psutil.pid_exists(pid):
                process = psutil.Process(pid)
                # 验证是否是我们的Python进程
                return 'python' in process.name().lower()
            else:
                # 进程不存在，清理PID文件
                os.remove(pid_file)
                return False
        except:
            return False
    
    def start_scheduler(self, scheduler_name):
        """启动指定的调度器"""
        if scheduler_name not in SCHEDULERS:
            print(f"❌ 未知的调度器: {scheduler_name}")
            return False
        
        if self.is_running(scheduler_name):
            print(f"⚠️  {SCHEDULERS[scheduler_name]['name']}已在运行中")
            return True
        
        scheduler_info = SCHEDULERS[scheduler_name]
        script_path = os.path.join(self.base_dir, scheduler_info['script'])
        
        if not os.path.exists(script_path):
            print(f"❌ 找不到脚本文件: {script_path}")
            return False
        
        # 确保日志目录存在
        log_dir = os.path.join(self.base_dir, 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 启动进程
        log_file = os.path.join(log_dir, f'{scheduler_name}_manager.log')
        
        print(f"🚀 正在启动{scheduler_info['name']}...")
        
        # 激活虚拟环境并运行
        venv_python = os.path.join(self.base_dir, 'venv', 'bin', 'python')
        if os.path.exists(venv_python):
            cmd = [venv_python, script_path]
        else:
            cmd = [sys.executable, script_path]
        
        with open(log_file, 'a') as log:
            process = subprocess.Popen(
                cmd,
                stdout=log,
                stderr=subprocess.STDOUT,
                start_new_session=True
            )
        
        # 保存PID
        pid_file = self.get_pid_file(scheduler_name)
        with open(pid_file, 'w') as f:
            f.write(str(process.pid))
        
        # 等待一下确认启动成功
        import time
        time.sleep(2)
        
        if self.is_running(scheduler_name):
            print(f"✅ {scheduler_info['name']}启动成功 (PID: {process.pid})")
            return True
        else:
            print(f"❌ {scheduler_info['name']}启动失败")
            return False
    
    def stop_scheduler(self, scheduler_name):
        """停止指定的调度器"""
        if scheduler_name not in SCHEDULERS:
            print(f"❌ 未知的调度器: {scheduler_name}")
            return False
        
        if not self.is_running(scheduler_name):
            print(f"⚠️  {SCHEDULERS[scheduler_name]['name']}未在运行")
            return True
        
        pid_file = self.get_pid_file(scheduler_name)
        try:
            with open(pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            print(f"🛑 正在停止{SCHEDULERS[scheduler_name]['name']} (PID: {pid})...")
            
            # 发送终止信号
            os.kill(pid, signal.SIGTERM)
            
            # 等待进程结束
            import time
            for i in range(10):
                if not psutil.pid_exists(pid):
                    break
                time.sleep(0.5)
            
            # 如果还在运行，强制杀死
            if psutil.pid_exists(pid):
                os.kill(pid, signal.SIGKILL)
            
            # 清理PID文件
            os.remove(pid_file)
            
            print(f"✅ {SCHEDULERS[scheduler_name]['name']}已停止")
            return True
            
        except Exception as e:
            print(f"❌ 停止失败: {e}")
            return False
    
    def restart_scheduler(self, scheduler_name):
        """重启指定的调度器"""
        print(f"🔄 正在重启{SCHEDULERS[scheduler_name]['name']}...")
        self.stop_scheduler(scheduler_name)
        import time
        time.sleep(1)
        return self.start_scheduler(scheduler_name)
    
    def status(self):
        """显示所有调度器状态"""
        print("\n📊 调度器状态:")
        print("-" * 60)
        print(f"{'调度器':<15} {'状态':<10} {'PID':<10} {'描述'}")
        print("-" * 60)
        
        for name, info in SCHEDULERS.items():
            if self.is_running(name):
                pid_file = self.get_pid_file(name)
                with open(pid_file, 'r') as f:
                    pid = f.read().strip()
                status = "✅ 运行中"
            else:
                pid = "-"
                status = "❌ 已停止"
            
            print(f"{name:<15} {status:<10} {pid:<10} {info['description']}")
        
        print("-" * 60)
    
    def logs(self, scheduler_name, lines=50):
        """查看调度器日志"""
        if scheduler_name not in SCHEDULERS:
            print(f"❌ 未知的调度器: {scheduler_name}")
            return
        
        log_file = os.path.join(self.base_dir, 'logs', f'scheduler_{scheduler_name}.log')
        
        if not os.path.exists(log_file):
            print(f"⚠️  日志文件不存在: {log_file}")
            return
        
        print(f"\n📄 {SCHEDULERS[scheduler_name]['name']}日志 (最后{lines}行):")
        print("-" * 80)
        
        # 使用tail命令显示日志
        subprocess.run(['tail', '-n', str(lines), log_file])
    
    def start_all(self):
        """启动所有调度器"""
        print("\n🚀 启动所有调度器...")
        success = True
        for scheduler_name in SCHEDULERS:
            if not self.start_scheduler(scheduler_name):
                success = False
        return success
    
    def stop_all(self):
        """停止所有调度器"""
        print("\n🛑 停止所有调度器...")
        success = True
        for scheduler_name in SCHEDULERS:
            if not self.stop_scheduler(scheduler_name):
                success = False
        return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='游戏监控系统调度器管理工具')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status', 'logs'],
                        help='要执行的操作')
    parser.add_argument('scheduler', nargs='?', default='all',
                        help='调度器名称 (scraper/reddit/trends/reports/monitor/all)')
    parser.add_argument('-n', '--lines', type=int, default=50,
                        help='查看日志时显示的行数')
    
    args = parser.parse_args()
    
    manager = SchedulerManager()
    
    if args.action == 'status':
        manager.status()
    
    elif args.action == 'logs':
        if args.scheduler == 'all':
            for scheduler_name in SCHEDULERS:
                manager.logs(scheduler_name, args.lines)
                print("\n")
        else:
            manager.logs(args.scheduler, args.lines)
    
    elif args.action == 'start':
        if args.scheduler == 'all':
            manager.start_all()
        else:
            manager.start_scheduler(args.scheduler)
    
    elif args.action == 'stop':
        if args.scheduler == 'all':
            manager.stop_all()
        else:
            manager.stop_scheduler(args.scheduler)
    
    elif args.action == 'restart':
        if args.scheduler == 'all':
            for scheduler_name in SCHEDULERS:
                manager.restart_scheduler(scheduler_name)
        else:
            manager.restart_scheduler(args.scheduler)


if __name__ == "__main__":
    main()