#!/bin/bash
# Game Monitor 管理脚本

VENV_PATH="./venv"
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

case "$1" in
    start)
        echo -e "${GREEN}启动 Game Monitor...${NC}"
        source $VENV_PATH/bin/activate
        
        # 设置 PYTHONPATH 为当前目录，确保模块能被找到
        export PYTHONPATH="$PWD:$PYTHONPATH"
        
        nohup python app.py > logs/app.log 2>&1 &
        echo $! > app.pid
        
        # 启动5个调度器
        nohup python -m src.scheduler_scraper > logs/scraper.log 2>&1 &
        echo $! > scheduler_scraper.pid
        nohup python -m src.scheduler_trends > logs/trends.log 2>&1 &
        echo $! > scheduler_trends.pid
        nohup python -m src.scheduler_reddit > logs/reddit.log 2>&1 &
        echo $! > scheduler_reddit.pid
        nohup python -m src.scheduler_reports > logs/reports.log 2>&1 &
        echo $! > scheduler_reports.pid
        # monitor 调度器需要特殊处理，因为它在子目录
        nohup python scripts/monitoring/scheduler_monitor.py > logs/monitor.log 2>&1 &
        echo $! > scheduler_monitor.pid
        echo -e "${GREEN}启动成功！${NC}"
        ;;
    stop)
        echo -e "${RED}停止 Game Monitor...${NC}"
        if [ -f app.pid ]; then
            kill $(cat app.pid)
            rm app.pid
        fi
        # 停止5个调度器
        for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports scheduler_monitor; do
            if [ -f ${scheduler}.pid ]; then
                kill $(cat ${scheduler}.pid)
                rm ${scheduler}.pid
            fi
        done
        echo -e "${GREEN}停止成功！${NC}"
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        echo "检查服务状态..."
        if [ -f app.pid ] && ps -p $(cat app.pid) > /dev/null; then
            echo -e "${GREEN}Flask 应用: 运行中${NC}"
        else
            echo -e "${RED}Flask 应用: 已停止${NC}"
        fi
        # 检查5个调度器状态
        for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports scheduler_monitor; do
            if [ -f ${scheduler}.pid ] && ps -p $(cat ${scheduler}.pid) > /dev/null; then
                echo -e "${GREEN}${scheduler}: 运行中${NC}"
            else
                echo -e "${RED}${scheduler}: 已停止${NC}"
            fi
        done
        ;;
    logs)
        tail -f logs/app.log logs/scraper.log logs/trends.log logs/reddit.log logs/reports.log logs/monitor.log
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
