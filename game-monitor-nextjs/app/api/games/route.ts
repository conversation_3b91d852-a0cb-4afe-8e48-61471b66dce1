import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const page = parseInt(searchParams.get('page') || '1')
    const perPage = parseInt(searchParams.get('per_page') || '20')
    const search = searchParams.get('search') || ''
    const source = searchParams.get('source') || ''
    const limit = searchParams.get('limit')
    
    // 如果指定了 limit，直接返回指定数量的最新游戏
    if (limit) {
      const games = await prisma.game.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: parseInt(limit),
      })
      
      return NextResponse.json({
        games,
        total: games.length,
      })
    }
    
    // 构建查询条件
    const where: any = {
      isActive: true,
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { tags: { contains: search } },
      ]
    }
    
    if (source) {
      where.sourceSite = source
    }
    
    // 获取总数
    const total = await prisma.game.count({ where })
    
    // 获取分页数据
    const games = await prisma.game.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      skip: (page - 1) * perPage,
      take: perPage,
    })
    
    return NextResponse.json({
      games,
      total,
      pages: Math.ceil(total / perPage),
      current_page: page,
      per_page: perPage,
    })
  } catch (error) {
    console.error('Error fetching games:', error)
    return NextResponse.json(
      { error: 'Failed to fetch games' },
      { status: 500 }
    )
  }
}