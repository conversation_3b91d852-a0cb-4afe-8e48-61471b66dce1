import { NextRequest, NextResponse } from 'next/server'
import { setAuthCookies } from '@/lib/auth'
import crypto from 'crypto'
import jwt from 'jsonwebtoken'

// 从环境变量读取配置
const AUTH_USERNAME = process.env.AUTH_USERNAME || 'admin'
const AUTH_PASSWORD_HASH = process.env.AUTH_PASSWORD_HASH || ''
const AUTH_SECRET_KEY = process.env.AUTH_SECRET_KEY || 'default-secret-key'
const AUTH_TOKEN_EXPIRY = parseInt(process.env.AUTH_TOKEN_EXPIRY || '86400', 10)

function verifyPassword(password: string): boolean {
  if (!AUTH_PASSWORD_HASH) {
    console.error('AUTH_PASSWORD_HASH not configured')
    return false
  }
  
  const hash = crypto.createHash('sha256').update(password).digest('hex')
  return hash === AUTH_PASSWORD_HASH
}

function generateToken(username: string): string {
  return jwt.sign(
    {
      username,
      exp: Math.floor(Date.now() / 1000) + AUTH_TOKEN_EXPIRY,
    },
    AUTH_SECRET_KEY
  )
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { username, password } = body
    
    // 验证用户名
    if (username !== AUTH_USERNAME) {
      return NextResponse.json(
        {
          success: false,
          message: '用户名或密码错误',
        },
        { status: 401 }
      )
    }
    
    // 验证密码
    if (!verifyPassword(password)) {
      return NextResponse.json(
        {
          success: false,
          message: '用户名或密码错误',
        },
        { status: 401 }
      )
    }
    
    // 生成 token
    const token = generateToken(username)
    
    // 设置 httpOnly cookies
    await setAuthCookies(token, username)
    
    return NextResponse.json({
      success: true,
      message: '登录成功',
    })
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      {
        success: false,
        message: '服务器错误',
      },
      { status: 500 }
    )
  }
}