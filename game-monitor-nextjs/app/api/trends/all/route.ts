import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '50')
    const minGrowth = parseFloat(searchParams.get('min_growth') || '0')
    
    // 获取趋势数据
    const trends = await prisma.trend.findMany({
      where: {
        growthRate: {
          gte: minGrowth,
        },
      },
      orderBy: {
        growthRate: 'desc',
      },
      take: limit,
    })
    
    return NextResponse.json({
      trends,
    })
  } catch (error) {
    console.error('Error fetching all trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch trends' },
      { status: 500 }
    )
  }
}