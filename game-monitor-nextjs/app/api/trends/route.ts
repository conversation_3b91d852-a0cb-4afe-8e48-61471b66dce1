import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const gameId = searchParams.get('game_id')
    const keyword = searchParams.get('keyword')
    const limit = parseInt(searchParams.get('limit') || '50')
    
    // 构建查询条件
    const where: any = {}
    
    if (gameId) {
      where.gameId = parseInt(gameId)
    }
    
    if (keyword) {
      where.keyword = {
        contains: keyword,
      }
    }
    
    // 获取趋势数据
    const trends = await prisma.trend.findMany({
      where,
      include: {
        game: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    })
    
    return NextResponse.json({
      trends,
      total: trends.length,
    })
  } catch (error) {
    console.error('Error fetching trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch trends' },
      { status: 500 }
    )
  }
}