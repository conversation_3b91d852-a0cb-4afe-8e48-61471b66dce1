import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '24')
    const days = parseInt(searchParams.get('days') || '7')
    
    // 计算日期范围
    const since = new Date()
    since.setDate(since.getDate() - days)
    
    // 获取带有趋势数据的游戏
    const trendingGames = await prisma.trend.findMany({
      where: {
        createdAt: {
          gte: since,
        },
        game: {
          isActive: true,
        },
      },
      include: {
        game: true,
      },
      orderBy: {
        growthRate: 'desc',
      },
      take: limit,
    })
    
    // 格式化返回数据
    const formattedGames = trendingGames.map(trend => {
      const game = trend.game!
      const trendData = trend.trendData as any || {}
      const metrics = trendData.metrics || {}
      const surgePatterns = trendData.surge_patterns || {}
      
      return {
        ...game,
        growth_rate: Number(trend.growthRate) || 0,
        trend_date: trend.createdAt?.toISOString(),
        heat_level: metrics.heat_level || 'moderate',
        volume_level: metrics.volume_level || 'unknown',
        trend_strength: metrics.trend_strength || 'stable',
        relative_heat: metrics.relative_heat || 1.0,
        day_over_day: metrics.day_over_day || 0,
        volatility: metrics.volatility || 0,
        trend_data: metrics.trend_data || [],
        chart_data: trendData.chart_data || {},
        is_at_peak: metrics.is_at_peak || false,
        is_surge: surgePatterns.is_surge || false,
        surge_type: surgePatterns.surge_type || 'none',
        surge_confidence: surgePatterns.surge_confidence || 0,
        alert_level: surgePatterns.alert_level || 'normal',
      }
    })
    
    return NextResponse.json({
      trending_games: formattedGames,
      total: formattedGames.length,
      timestamp: new Date().toISOString(),
    })
  } catch (error) {
    console.error('Error fetching trending games:', error)
    return NextResponse.json(
      { error: 'Failed to fetch trending games' },
      { status: 500 }
    )
  }
}