import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const limit = parseInt(searchParams.get('limit') || '20')
    const hours = parseInt(searchParams.get('hours') || '24')
    
    // 计算时间范围
    const since = new Date()
    since.setHours(since.getHours() - hours)
    
    // 获取暴涨警报
    const alerts = await prisma.alert.findMany({
      where: {
        createdAt: {
          gte: since,
        },
        alertType: {
          in: ['surge', 'viral_explosion', 'strong_surge', 'moderate_surge'],
        },
      },
      include: {
        game: true,
      },
      orderBy: {
        currentValue: 'desc',
      },
      take: limit,
    })
    
    return NextResponse.json({
      alerts,
      total: alerts.length,
    })
  } catch (error) {
    console.error('Error fetching surge alerts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch surge alerts' },
      { status: 500 }
    )
  }
}