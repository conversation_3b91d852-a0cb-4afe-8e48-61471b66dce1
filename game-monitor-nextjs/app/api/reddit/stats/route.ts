import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    // 基础统计
    const totalGames = await prisma.game.count({
      where: {
        isActive: true,
      },
    })
    
    // Reddit统计 - 24小时内
    const since24h = new Date()
    since24h.setHours(since24h.getHours() - 24)
    
    // 聚合Reddit指标
    const redditAggregates = await prisma.redditMetric.aggregate({
      where: {
        createdAt: {
          gte: since24h,
        },
      },
      _count: {
        id: true,
      },
      _avg: {
        heatScore: true,
      },
      _max: {
        heatScore: true,
      },
    })
    
    // 热门游戏（基于Reddit）- 获取最新的热度数据
    const hotGamesData = await prisma.redditMetric.findMany({
      where: {
        createdAt: {
          gte: since24h,
        },
        heatScore: {
          gt: 0,
        },
      },
      include: {
        game: true,
      },
      orderBy: {
        heatScore: 'desc',
      },
      distinct: ['gameId'],
      take: 5,
    })
    
    // 格式化热门游戏数据
    const hotGames = hotGamesData.map(metric => ({
      name: metric.game.name,
      heat_score: metric.heatScore || 0,
      growth_rate: metric.growthRate || 0,
    }))
    
    return NextResponse.json({
      success: true,
      stats: {
        total_games: totalGames,
        reddit: {
          total_analyses_24h: redditAggregates._count.id || 0,
          avg_heat_score: Math.round(redditAggregates._avg.heatScore || 0),
          max_heat_score: redditAggregates._max.heatScore || 0,
        },
        hot_games: hotGames,
      },
      timestamp: new Date().toISOString(),
    })
    
  } catch (error) {
    console.error('Error fetching Reddit stats:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch stats' 
      },
      { status: 500 }
    )
  }
}