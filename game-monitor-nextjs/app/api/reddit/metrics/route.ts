import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams
    const gameId = searchParams.get('game_id')
    const days = searchParams.get('days') || '7'
    
    // 如果指定了游戏ID，获取该游戏的Reddit历史数据
    if (gameId) {
      const since = new Date()
      since.setDate(since.getDate() - parseInt(days))
      
      const metrics = await prisma.redditMetric.findMany({
        where: {
          gameId: parseInt(gameId),
          createdAt: {
            gte: since,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      })
      
      return NextResponse.json({
        success: true,
        game_id: parseInt(gameId),
        data: metrics.map(m => ({
          ...m,
          id: Number(m.id),
          gameId: Number(m.gameId),
          postCount24h: Number(m.postCount24h || 0),
          totalUpvotes24h: Number(m.totalUpvotes24h || 0),
          totalComments24h: Number(m.totalComments24h || 0),
          topPostScore: Number(m.topPostScore || 0),
        })),
        days: parseInt(days),
        count: metrics.length,
      })
    }
    
    // 否则获取统一的趋势数据（包含Reddit和Google数据）
    const limit = parseInt(searchParams.get('limit') || '50')
    const minHeat = parseInt(searchParams.get('min_heat') || '0')
    const sortBy = searchParams.get('sort_by') || 'composite_score'
    const source = searchParams.get('source') || 'all'
    
    // 获取有Reddit数据的游戏
    const latestRedditMetrics = await prisma.redditMetric.findMany({
      where: {
        ...(source === 'reddit' ? { heatScore: { gt: 0 } } : {}),
        ...(minHeat > 0 ? { heatScore: { gte: minHeat } } : {}),
      },
      include: {
        game: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      distinct: ['gameId'], // 每个游戏只取最新的一条
      take: limit * 2, // 多取一些，后面再过滤
    })
    
    // 获取Google Trends数据
    const trends = source !== 'reddit' ? await prisma.trend.findMany({
      where: {
        game: {
          isActive: true,
        },
      },
      include: {
        game: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      distinct: ['gameId'],
      take: limit * 2,
    }) : []
    
    // 合并数据
    const gameMap = new Map()
    
    // 处理Reddit数据
    latestRedditMetrics.forEach(metric => {
      if (!gameMap.has(metric.gameId)) {
        gameMap.set(metric.gameId, {
          game: metric.game,
          reddit: {
            heat_score: metric.heatScore || 0,
            growth_rate: metric.growthRate || 0,
            post_count: metric.postCount24h || 0,
            upvotes: metric.totalUpvotes24h || 0,
            comments: metric.totalComments24h || 0,
            top_post: {
              title: metric.topPostTitle || '',
              score: metric.topPostScore || 0,
              url: metric.topPostUrl || '',
            },
            sentiment: metric.sentiment || 'neutral',
            status: metric.status || 'no_data',
          },
          google: null,
        })
      }
    })
    
    // 处理Google Trends数据
    trends.forEach(trend => {
      const existing = gameMap.get(trend.gameId)
      if (existing) {
        existing.google = {
          value: trend.trendValue || 0,
          growth_rate: Number(trend.growthRate) || 0,
          status: trend.trendValue ? 'success' : 'no_data',
          updated_at: trend.createdAt?.toISOString() || '',
        }
      } else if (source !== 'reddit') {
        gameMap.set(trend.gameId, {
          game: trend.game,
          reddit: null,
          google: {
            value: trend.trendValue || 0,
            growth_rate: Number(trend.growthRate) || 0,
            status: trend.trendValue ? 'success' : 'no_data',
            updated_at: trend.createdAt?.toISOString() || '',
          },
        })
      }
    })
    
    // 转换为数组并计算综合分数
    let results = Array.from(gameMap.values()).map(item => {
      const redditScore = item.reddit?.heat_score || 0
      const googleScore = item.google?.value || 0
      const compositeScore = (redditScore * 0.6 + googleScore * 0.4)
      
      return {
        game: {
          id: Number(item.game.id),
          name: item.game.name,
          title: item.game.title,
          url: item.game.gameUrl,
          thumbnail: item.game.thumbnailUrl,
          source_site: item.game.sourceSite,
          created_at: item.game.createdAt?.toISOString(),
        },
        reddit: item.reddit,
        google: item.google,
        composite_score: Math.round(compositeScore * 100) / 100,
        last_updated: new Date().toISOString(),
      }
    })
    
    // 根据source过滤
    if (source === 'reddit') {
      results = results.filter(r => r.reddit && r.reddit.heat_score > 0)
    } else if (source === 'google') {
      results = results.filter(r => r.google && r.google.value > 0)
    }
    
    // 排序
    results.sort((a, b) => {
      if (sortBy === 'reddit_heat') {
        return (b.reddit?.heat_score || 0) - (a.reddit?.heat_score || 0)
      } else if (sortBy === 'google_trend') {
        return (b.google?.value || 0) - (a.google?.value || 0)
      } else {
        return b.composite_score - a.composite_score
      }
    })
    
    // 限制数量
    results = results.slice(0, limit)
    
    return NextResponse.json({
      success: true,
      data: results,
      count: results.length,
      timestamp: new Date().toISOString(),
    })
    
  } catch (error) {
    console.error('Error fetching Reddit metrics:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to fetch Reddit metrics' 
      },
      { status: 500 }
    )
  }
}