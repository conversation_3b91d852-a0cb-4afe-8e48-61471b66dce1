import { NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export const dynamic = 'force-dynamic'

export async function GET() {
  try {
    // 获取游戏总数
    const totalGames = await prisma.game.count({
      where: {
        isActive: true,
      },
    })
    
    // 获取今日新增游戏数
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todayGames = await prisma.game.count({
      where: {
        createdAt: {
          gte: today,
        },
        isActive: true,
      },
    })
    
    // 获取趋势数量（增长率大于阈值）
    const trendingCount = await prisma.trend.count({
      where: {
        growthRate: {
          gt: 50, // SURGE_THRESHOLD
        },
      },
    })
    
    // 获取活跃网站数
    const activeSites = await prisma.siteConfig.count({
      where: {
        isActive: true,
      },
    })
    
    return NextResponse.json({
      total_games: totalGames,
      today_games: todayGames,
      trending_count: trendingCount,
      active_sites: activeSites,
    })
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch statistics' },
      { status: 500 }
    )
  }
}