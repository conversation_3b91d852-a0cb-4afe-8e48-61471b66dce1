'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Search, Filter, ExternalLink, Calendar, User, Tag } from 'lucide-react'
import Image from 'next/image'

interface Game {
  id: number
  name: string
  title?: string
  description?: string
  thumbnailUrl?: string
  gameUrl?: string
  sourceSite?: string
  genre?: string
  tags?: string
  author?: string
  playerCount?: string
  rating?: string
  createdAt: string
}

interface GamesResponse {
  games: Game[]
  total: number
  pages: number
  current_page: number
  per_page: number
}

function GamesContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [games, setGames] = useState<Game[]>([])
  const [loading, setLoading] = useState(true)
  const [total, setTotal] = useState(0)
  const [pages, setPages] = useState(1)
  const [currentPage, setCurrentPage] = useState(1)
  const [search, setSearch] = useState(searchParams.get('search') || '')
  const [source, setSource] = useState(searchParams.get('source') || '')
  const [sources, setSources] = useState<string[]>([])
  
  // 获取所有来源网站
  useEffect(() => {
    fetch('/api/games?per_page=1000')
      .then(res => res.json())
      .then(data => {
        const uniqueSources = [...new Set(data.games.map((g: Game) => g.sourceSite).filter(Boolean))]
        setSources(uniqueSources as string[])
      })
  }, [])
  
  // 获取游戏列表
  useEffect(() => {
    const fetchGames = async () => {
      setLoading(true)
      const params = new URLSearchParams()
      params.set('page', currentPage.toString())
      params.set('per_page', '20')
      if (search) params.set('search', search)
      if (source) params.set('source', source)
      
      try {
        const res = await fetch(`/api/games?${params.toString()}`)
        const data: GamesResponse = await res.json()
        setGames(data.games)
        setTotal(data.total)
        setPages(data.pages)
      } catch (error) {
        console.error('Failed to fetch games:', error)
      } finally {
        setLoading(false)
      }
    }
    
    fetchGames()
  }, [currentPage, search, source])
  
  // 更新 URL 参数
  const updateParams = () => {
    const params = new URLSearchParams()
    if (search) params.set('search', search)
    if (source) params.set('source', source)
    params.set('page', '1')
    router.push(`/games?${params.toString()}`)
    setCurrentPage(1)
  }
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateParams()
  }
  
  const handleSourceChange = (value: string) => {
    setSource(value)
    const params = new URLSearchParams()
    if (search) params.set('search', search)
    if (value) params.set('source', value)
    params.set('page', '1')
    router.push(`/games?${params.toString()}`)
    setCurrentPage(1)
  }
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    })
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">游戏中心</h1>
          <p className="mt-2 text-gray-600">
            共收录 {total} 款游戏，来自 {sources.length} 个网站
          </p>
        </div>
        
        {/* 搜索和筛选 */}
        <div className="bg-white rounded-lg shadow mb-6 p-4">
          <form onSubmit={handleSearch} className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="搜索游戏名称、描述或标签..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={source}
              onChange={(e) => handleSourceChange(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">所有来源</option>
              {sources.map(s => (
                <option key={s} value={s}>{s}</option>
              ))}
            </select>
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              搜索
            </button>
          </form>
        </div>
        
        {/* 游戏列表 */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {games.map(game => (
                <div key={game.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  {game.thumbnailUrl && (
                    <div className="aspect-video relative bg-gray-100">
                      <img
                        src={game.thumbnailUrl}
                        alt={game.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder-game.png'
                        }}
                      />
                    </div>
                  )}
                  <div className="p-4">
                    <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-1">
                      {game.name}
                    </h3>
                    {game.description && (
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {game.description}
                      </p>
                    )}
                    <div className="space-y-2 text-sm text-gray-500">
                      {game.author && (
                        <div className="flex items-center gap-1">
                          <User className="w-4 h-4" />
                          <span className="truncate">{game.author}</span>
                        </div>
                      )}
                      {game.sourceSite && (
                        <div className="flex items-center gap-1">
                          <ExternalLink className="w-4 h-4" />
                          <span>{game.sourceSite}</span>
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(game.createdAt)}</span>
                      </div>
                      {game.tags && (
                        <div className="flex items-center gap-1">
                          <Tag className="w-4 h-4" />
                          <span className="truncate">{game.tags}</span>
                        </div>
                      )}
                    </div>
                    {game.gameUrl && (
                      <a
                        href={game.gameUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="mt-4 block w-full text-center py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                      >
                        查看游戏
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            {/* 分页 */}
            {pages > 1 && (
              <div className="mt-8 flex justify-center">
                <div className="flex gap-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    上一页
                  </button>
                  <span className="px-4 py-2 text-gray-700">
                    第 {currentPage} / {pages} 页
                  </span>
                  <button
                    onClick={() => setCurrentPage(Math.min(pages, currentPage + 1))}
                    disabled={currentPage === pages}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    下一页
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}

export default function GamesPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <GamesContent />
    </Suspense>
  )
}