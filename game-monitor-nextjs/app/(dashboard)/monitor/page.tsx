'use client'

import { useState, useEffect } from 'react'
import { Activity, CheckCircle, XCircle, AlertCircle, Clock, RefreshCw, Server, Database } from 'lucide-react'

interface SchedulerInfo {
  name: string
  log_file: string
  status: string
  last_run: string | null
  last_success: string | null
  last_error: string | null
  success_count: number
  error_count: number
  success_rate?: number
  last_modified?: string
  last_error_msg?: string
  error?: string
}

export default function MonitorPage() {
  const [schedulers, setSchedulers] = useState<Record<string, SchedulerInfo>>({})
  const [loading, setLoading] = useState(true)
  const [lastRefresh, setLastRefresh] = useState(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)
  
  const fetchSchedulerLogs = async () => {
    try {
      const res = await fetch('/api/scheduler/logs')
      const data = await res.json()
      setSchedulers(data)
      setLastRefresh(new Date())
    } catch (error) {
      console.error('Failed to fetch scheduler logs:', error)
    } finally {
      setLoading(false)
    }
  }
  
  useEffect(() => {
    fetchSchedulerLogs()
    
    if (autoRefresh) {
      const interval = setInterval(fetchSchedulerLogs, 30000) // 每30秒刷新
      return () => clearInterval(interval)
    }
  }, [autoRefresh])
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running':
        return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />
      case 'not_found':
        return <AlertCircle className="w-5 h-5 text-gray-400" />
      default:
        return <Clock className="w-5 h-5 text-yellow-500" />
    }
  }
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'running':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'not_found':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }
  
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '未知'
    const date = new Date(dateString.replace(' ', 'T'))
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    if (diff < 60000) return '刚刚'
    if (diff < 3600000) return `${Math.floor(diff / 60000)} 分钟前`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)} 小时前`
    return `${Math.floor(diff / 86400000)} 天前`
  }
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">监控中心</h1>
              <p className="mt-2 text-gray-600">
                系统运行状态和调度器监控
              </p>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-500">
                最后更新: {lastRefresh.toLocaleTimeString('zh-CN')}
              </div>
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  autoRefresh
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {autoRefresh ? '自动刷新: 开' : '自动刷新: 关'}
              </button>
              <button
                onClick={fetchSchedulerLogs}
                className="p-2 rounded-lg bg-white shadow hover:shadow-md transition-shadow"
              >
                <RefreshCw className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
        
        {/* 系统概览 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Server className="w-6 h-6 text-blue-600" />
              </div>
              <span className="text-sm text-gray-500">系统状态</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">运行中</div>
            <p className="text-sm text-gray-500 mt-1">所有服务正常</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <Activity className="w-6 h-6 text-green-600" />
              </div>
              <span className="text-sm text-gray-500">活跃调度器</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {Object.values(schedulers).filter(s => s.status === 'healthy' || s.status === 'running').length}
            </div>
            <p className="text-sm text-gray-500 mt-1">共 {Object.keys(schedulers).length} 个</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <AlertCircle className="w-6 h-6 text-yellow-600" />
              </div>
              <span className="text-sm text-gray-500">异常数量</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {Object.values(schedulers).filter(s => s.status === 'error').length}
            </div>
            <p className="text-sm text-gray-500 mt-1">需要关注</p>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Database className="w-6 h-6 text-purple-600" />
              </div>
              <span className="text-sm text-gray-500">平均成功率</span>
            </div>
            <div className="text-2xl font-bold text-gray-900">
              {(
                Object.values(schedulers)
                  .filter(s => s.success_rate !== undefined)
                  .reduce((acc, s) => acc + (s.success_rate || 0), 0) /
                Object.values(schedulers).filter(s => s.success_rate !== undefined).length || 0
              ).toFixed(1)}%
            </div>
            <p className="text-sm text-gray-500 mt-1">整体表现</p>
          </div>
        </div>
        
        {/* 调度器详情 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">调度器状态</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {Object.entries(schedulers).map(([key, scheduler]) => (
              <div key={key} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-4">
                    {getStatusIcon(scheduler.status)}
                    <div>
                      <h3 className="font-semibold text-gray-900">{scheduler.name}</h3>
                      <p className="text-sm text-gray-500 mt-1">{scheduler.log_file}</p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(scheduler.status)}`}>
                    {scheduler.status === 'healthy' ? '健康' :
                     scheduler.status === 'running' ? '运行中' :
                     scheduler.status === 'error' ? '异常' :
                     scheduler.status === 'not_found' ? '未找到' : '未知'}
                  </span>
                </div>
                
                <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">最后运行：</span>
                    <p className="font-medium">{formatDate(scheduler.last_run)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">最后成功：</span>
                    <p className="font-medium text-green-600">{formatDate(scheduler.last_success)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">最后错误：</span>
                    <p className="font-medium text-red-600">{formatDate(scheduler.last_error)}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">成功率：</span>
                    <p className="font-medium">
                      {scheduler.success_rate !== undefined ? `${scheduler.success_rate}%` : '暂无数据'}
                    </p>
                  </div>
                </div>
                
                {scheduler.last_error_msg && (
                  <div className="mt-4 p-3 bg-red-50 rounded-lg">
                    <p className="text-sm text-red-800">
                      <span className="font-medium">最后错误信息：</span>
                      {scheduler.last_error_msg}
                    </p>
                  </div>
                )}
                
                <div className="mt-4 flex items-center gap-6 text-sm text-gray-500">
                  <span>成功次数: {scheduler.success_count}</span>
                  <span>错误次数: {scheduler.error_count}</span>
                  {scheduler.last_modified && (
                    <span>日志更新: {formatDate(scheduler.last_modified)}</span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}