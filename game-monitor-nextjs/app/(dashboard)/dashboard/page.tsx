'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { clearAuthCookies } from '@/lib/auth-client'
import { Package, TrendingUp, Zap, Globe, BarChart3, PieChart, Loader2 } from 'lucide-react'

interface Stats {
  total_games: number
  today_games: number
  trending_count: number
  active_sites: number
}

interface RecentGame {
  id: number
  name: string
  sourceSite?: string
  createdAt: string
}

export default function DashboardPage() {
  const router = useRouter()
  const [stats, setStats] = useState<Stats | null>(null)
  const [recentGames, setRecentGames] = useState<RecentGame[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        // 获取统计数据
        const statsRes = await fetch('/api/stats')
        if (!statsRes.ok) throw new Error('Failed to fetch stats')
        const statsData = await statsRes.json()
        setStats(statsData)
        
        // 获取最近的游戏
        const gamesRes = await fetch('/api/games?limit=10')
        if (!gamesRes.ok) throw new Error('Failed to fetch games')
        const gamesData = await gamesRes.json()
        setRecentGames(gamesData.games)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
    // 每分钟刷新一次数据
    const interval = setInterval(fetchData, 60000)
    return () => clearInterval(interval)
  }, [])
  
  const handleLogout = async () => {
    await clearAuthCookies()
    router.push('/login')
  }
  
  const calculateGrowthRate = () => {
    if (!stats || stats.total_games === 0) return 0
    return ((stats.today_games / stats.total_games) * 100).toFixed(1)
  }
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    )
  }
  
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">加载失败: {error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <h1 className="text-2xl font-semibold text-gray-900">游戏监控系统</h1>
            <button
              onClick={handleLogout}
              className="px-4 py-2 text-sm text-gray-700 hover:text-gray-900"
            >
              退出登录
            </button>
          </div>
        </div>
      </div>
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">仪表板</h2>
          <p className="text-gray-600">最后更新: {new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <span className="text-sm text-gray-500">游戏总数</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {stats?.total_games.toLocaleString() || 0}
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <span className="text-sm text-gray-500">今日新增</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {stats?.today_games || 0}
            </p>
            <p className="text-sm text-green-600 mt-1">
              ↑ {calculateGrowthRate()}%
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-red-100 rounded-lg">
                <Zap className="h-6 w-6 text-red-600" />
              </div>
              <span className="text-sm text-gray-500">热度暴涨</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {stats?.trending_count || 0}
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-100 rounded-lg">
                <Globe className="h-6 w-6 text-purple-600" />
              </div>
              <span className="text-sm text-gray-500">监控网站</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">
              {stats?.active_sites || 0}/27
            </p>
          </div>
        </div>
        
        {/* Recent Games and Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">最新收录游戏</h3>
            </div>
            <div className="space-y-3">
              {recentGames.map(game => (
                <div key={game.id} className="flex items-center justify-between py-2 border-b last:border-0">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {game.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {game.sourceSite} · {formatDate(game.createdAt)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">快速访问</h3>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <button
                onClick={() => router.push('/games')}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow text-center"
              >
                <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-sm font-medium">游戏中心</p>
              </button>
              <button
                onClick={() => router.push('/trends')}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow text-center"
              >
                <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-sm font-medium">趋势分析</p>
              </button>
              <button
                onClick={() => router.push('/monitor')}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow text-center"
              >
                <BarChart3 className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <p className="text-sm font-medium">监控中心</p>
              </button>
              <button
                className="p-4 border rounded-lg opacity-50 cursor-not-allowed text-center"
                disabled
              >
                <PieChart className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm font-medium text-gray-400">更多功能</p>
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}