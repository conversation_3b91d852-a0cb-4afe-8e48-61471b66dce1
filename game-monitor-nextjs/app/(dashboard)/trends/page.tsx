'use client'

import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, Zap, AlertCircle, ArrowUp } from 'lucide-react'
import { UnifiedTrend, RedditMetric } from '@/types'
import { RedditMetricsCard } from '@/components/reddit/RedditMetricsCard'
import { RedditHeatmap } from '@/components/reddit/RedditHeatmap'
import { RedditTrendChart } from '@/components/reddit/RedditTrendChart'

interface Trend {
  id: number
  keyword: string
  trendValue: number
  comparisonValue: number
  growthRate: string
  timeframe: string
  region: string
  createdAt: string
  game?: Game
}

interface Game {
  id: number
  name: string
  thumbnailUrl?: string
  sourceSite?: string
}

interface TrendingGame {
  id: number
  name: string
  thumbnailUrl?: string
  sourceSite?: string
  growth_rate: number
  trend_date: string
  heat_level: string
  volume_level: string
  trend_strength: string
  is_surge: boolean
  surge_type: string
  alert_level: string
}

interface Alert {
  id: number
  keyword: string
  alertType: string
  currentValue: string
  thresholdValue: string
  createdAt: string
  game?: Game
}

export default function TrendsPage() {
  const [trends, setTrends] = useState<Trend[]>([])
  const [trendingGames, setTrendingGames] = useState<TrendingGame[]>([])
  const [surges, setSurges] = useState<Alert[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'trends' | 'games' | 'surges' | 'reddit'>('trends')
  const [unifiedTrends, setUnifiedTrends] = useState<UnifiedTrend[]>([])
  const [selectedGameId, setSelectedGameId] = useState<number | null>(null)
  const [gameRedditHistory, setGameRedditHistory] = useState<RedditMetric[]>([])
  const [redditStats, setRedditStats] = useState<{
    reddit?: {
      total_analyses_24h: number;
      avg_heat_score: number;
      max_heat_score: number;
    };
    totalPosts?: number;
    totalComments?: number;
    avgScore?: number;
    topSubreddits?: Array<{ name: string; count: number }>;
  } | null>(null)
  
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // 获取趋势数据
        const trendsRes = await fetch('/api/trends/all?limit=50&min_growth=0')
        const trendsData = await trendsRes.json()
        setTrends(trendsData.trends)
        
        // 获取趋势游戏
        const gamesRes = await fetch('/api/trending-games?limit=24')
        const gamesData = await gamesRes.json()
        setTrendingGames(gamesData.trending_games)
        
        // 获取暴涨警报
        const surgesRes = await fetch('/api/surge-alerts?limit=20')
        const surgesData = await surgesRes.json()
        setSurges(surgesData.alerts)
        
        // 获取 Reddit 数据
        if (activeTab === 'reddit') {
          const redditRes = await fetch('/api/reddit/metrics?limit=50&source=reddit')
          const redditData = await redditRes.json()
          if (redditData.success) {
            setUnifiedTrends(redditData.data)
          }
          
          // 获取 Reddit 统计
          const statsRes = await fetch('/api/reddit/stats')
          const statsData = await statsRes.json()
          if (statsData.success) {
            setRedditStats(statsData.stats)
          }
        }
      } catch (error) {
        console.error('Failed to fetch trends data:', error)
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [activeTab])
  
  const fetchGameRedditHistory = async (gameId: number) => {
    try {
      const res = await fetch(`/api/reddit/metrics?game_id=${gameId}&days=7`)
      const data = await res.json()
      if (data.success && data.data) {
        setGameRedditHistory(data.data)
        setSelectedGameId(gameId)
      }
    } catch (error) {
      console.error('Failed to fetch game Reddit history:', error)
    }
  }
  
  const getHeatLevelColor = (level: string) => {
    switch (level) {
      case 'explosive': return 'text-red-600 bg-red-100'
      case 'hot': return 'text-orange-600 bg-orange-100'
      case 'warm': return 'text-yellow-600 bg-yellow-100'
      case 'moderate': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }
  
  const getAlertTypeIcon = (type: string) => {
    switch (type) {
      case 'viral_explosion': return <Zap className="w-5 h-5 text-red-500" />
      case 'strong_surge': return <TrendingUp className="w-5 h-5 text-orange-500" />
      case 'moderate_surge': return <ArrowUp className="w-5 h-5 text-yellow-500" />
      default: return <AlertCircle className="w-5 h-5 text-blue-500" />
    }
  }
  
  const formatGrowthRate = (rate: string | number) => {
    const numRate = typeof rate === 'string' ? parseFloat(rate) : rate
    if (isNaN(numRate)) return '0%'
    return numRate > 0 ? `+${numRate.toFixed(1)}%` : `${numRate.toFixed(1)}%`
  }
  
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }
  
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">趋势分析</h1>
          <p className="mt-2 text-gray-600">
            基于 Google Trends 和多基线对比的游戏热度分析
          </p>
        </div>
        
        {/* 标签切换 */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('trends')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'trends'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                热门趋势
              </button>
              <button
                onClick={() => setActiveTab('games')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'games'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                趋势游戏
              </button>
              <button
                onClick={() => setActiveTab('surges')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'surges'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                暴涨警报
              </button>
              <button
                onClick={() => setActiveTab('reddit')}
                className={`py-4 px-6 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'reddit'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Reddit 热度
              </button>
            </nav>
          </div>
        </div>
        
        {/* 内容区域 */}
        <div className="bg-white rounded-lg shadow">
          {activeTab === 'trends' && (
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">热门关键词趋势</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        关键词
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        增长率
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        趋势值
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        时间范围
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        更新时间
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {trends.map(trend => (
                      <tr key={trend.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {trend.keyword}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center gap-1 ${
                            parseFloat(trend.growthRate) > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {parseFloat(trend.growthRate) > 0 ? (
                              <TrendingUp className="w-4 h-4" />
                            ) : (
                              <TrendingDown className="w-4 h-4" />
                            )}
                            {formatGrowthRate(trend.growthRate)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {trend.trendValue}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {trend.timeframe}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(trend.createdAt)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
          
          {activeTab === 'games' && (
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">趋势游戏</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {trendingGames.map(game => (
                  <div key={game.id} className="border rounded-lg p-4 hover:shadow-lg transition-shadow">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-semibold text-gray-900 line-clamp-1">
                        {game.name}
                      </h3>
                      {game.is_surge && (
                        <Zap className="w-5 h-5 text-red-500 flex-shrink-0 ml-2" />
                      )}
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-500">增长率</span>
                        <span className={`font-semibold ${
                          game.growth_rate > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {formatGrowthRate(game.growth_rate)}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-gray-500">热度等级</span>
                        <span className={`px-2 py-1 rounded text-xs ${getHeatLevelColor(game.heat_level)}`}>
                          {game.heat_level}
                        </span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-gray-500">趋势强度</span>
                        <span className="text-gray-700">{game.trend_strength}</span>
                      </div>
                      
                      {game.alert_level !== 'normal' && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-500">警报级别</span>
                          <span className="text-orange-600 font-semibold">
                            {game.alert_level}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <div className="mt-4 pt-4 border-t">
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{game.sourceSite}</span>
                        <span>{formatDate(game.trend_date)}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {activeTab === 'surges' && (
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">暴涨警报</h2>
              <div className="space-y-4">
                {surges.map(alert => (
                  <div key={alert.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="flex-shrink-0">
                        {getAlertTypeIcon(alert.alertType)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="font-semibold text-gray-900">
                              {alert.keyword}
                            </h3>
                            {alert.game && (
                              <p className="text-sm text-gray-600 mt-1">
                                游戏：{alert.game.name}
                              </p>
                            )}
                          </div>
                          <span className="text-lg font-bold text-red-600">
                            +{parseFloat(alert.currentValue).toFixed(1)}%
                          </span>
                        </div>
                        <div className="mt-2 flex items-center gap-4 text-sm text-gray-500">
                          <span>阈值：{parseFloat(alert.thresholdValue).toFixed(1)}%</span>
                          <span>•</span>
                          <span>{formatDate(alert.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
          
          {activeTab === 'reddit' && (
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4">Reddit 社区热度</h2>
              
              {/* Reddit 统计概览 */}
              {redditStats && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div className="text-sm text-orange-600 mb-1">24小时分析</div>
                    <div className="text-2xl font-bold text-orange-700">
                      {redditStats.reddit?.total_analyses_24h || 0}
                    </div>
                  </div>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="text-sm text-blue-600 mb-1">平均热度</div>
                    <div className="text-2xl font-bold text-blue-700">
                      {redditStats.reddit?.avg_heat_score || 0}
                    </div>
                  </div>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="text-sm text-red-600 mb-1">最高热度</div>
                    <div className="text-2xl font-bold text-red-700">
                      {redditStats.reddit?.max_heat_score || 0}
                    </div>
                  </div>
                </div>
              )}
              
              {/* Reddit 热度地图 */}
              {unifiedTrends.length > 0 && (
                <div className="mb-6">
                  <RedditHeatmap 
                    metrics={unifiedTrends
                      .filter(t => t.reddit && t.reddit.heat_score > 0)
                      .map(t => ({
                        id: t.game.id,
                        game_id: t.game.id,
                        game_name: t.game.name,
                        heat_score: t.reddit!.heat_score,
                        growth_rate: t.reddit!.growth_rate,
                        post_count_24h: t.reddit!.post_count,
                        total_upvotes_24h: t.reddit!.upvotes,
                        total_comments_24h: t.reddit!.comments,
                        top_post_title: t.reddit!.top_post.title,
                        top_post_score: t.reddit!.top_post.score,
                        top_post_url: t.reddit!.top_post.url,
                        avg_comments: 0,
                        unique_authors: 0,
                        sentiment: t.reddit!.sentiment as 'positive' | 'neutral' | 'negative',
                        subreddit_distribution: {},
                        created_at: t.last_updated,
                        status: 'success' as const,
                      }))}
                    onGameClick={fetchGameRedditHistory}
                  />
                </div>
              )}
              
              {/* 选中游戏的趋势图表 */}
              {selectedGameId && gameRedditHistory.length > 0 && (
                <div className="mb-6">
                  <RedditTrendChart 
                    metrics={gameRedditHistory}
                    gameName={gameRedditHistory[0]?.game_name || ''}
                  />
                </div>
              )}
              
              {/* Reddit 指标卡片列表 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {unifiedTrends
                  .filter(t => t.reddit && t.reddit.heat_score > 0)
                  .sort((a, b) => (b.reddit?.heat_score || 0) - (a.reddit?.heat_score || 0))
                  .map(trend => (
                    <RedditMetricsCard
                      key={trend.game.id}
                      metric={{
                        id: trend.game.id,
                        game_id: trend.game.id,
                        game_name: trend.game.name,
                        heat_score: trend.reddit!.heat_score,
                        growth_rate: trend.reddit!.growth_rate,
                        post_count_24h: trend.reddit!.post_count,
                        total_upvotes_24h: trend.reddit!.upvotes,
                        total_comments_24h: trend.reddit!.comments,
                        top_post_title: trend.reddit!.top_post.title,
                        top_post_score: trend.reddit!.top_post.score,
                        top_post_url: trend.reddit!.top_post.url,
                        avg_comments: 0,
                        unique_authors: 0,
                        sentiment: trend.reddit!.sentiment as 'positive' | 'neutral' | 'negative',
                        subreddit_distribution: {},
                        created_at: trend.last_updated,
                        status: 'success' as const,
                      }}
                    />
                  ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}