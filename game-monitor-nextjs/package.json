{"name": "game-monitor-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next node_modules package-lock.json", "fresh-install": "npm run clean && npm install", "deploy": "npm run fresh-install && npm run build", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@radix-ui/react-slot": "^1.2.3", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.536.0", "mysql2": "^3.14.3", "next": "14.2.31", "prisma": "^6.13.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.62.0", "recharts": "^3.1.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@playwright/test": "^1.54.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.31", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}