#!/bin/bash

# Next.js 项目部署脚本
# 适用于 Linux 服务器 Node.js 20 环境

echo "🚀 开始部署 Next.js 项目..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node --version
npm --version

# 清理旧的构建文件
echo "🧹 清理旧文件..."
rm -rf node_modules package-lock.json .next

# 安装依赖
echo "📦 安装依赖..."
npm install

# 检查关键文件是否存在
echo "🔍 检查关键文件..."
if [ ! -f "lib/utils.ts" ]; then
    echo "❌ lib/utils.ts 文件缺失"
    exit 1
fi

if [ ! -f "lib/auth-client.ts" ]; then
    echo "❌ lib/auth-client.ts 文件缺失"
    exit 1
fi

if [ ! -f "lib/auth.ts" ]; then
    echo "❌ lib/auth.ts 文件缺失"
    exit 1
fi

if [ ! -f "lib/prisma.ts" ]; then
    echo "❌ lib/prisma.ts 文件缺失"
    exit 1
fi

echo "✅ 所有关键文件存在"

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "⚠️  .env.local 文件不存在，从示例文件创建..."
    cp .env.example .env.local
    echo "📝 请编辑 .env.local 文件配置正确的数据库连接信息"
fi

# 构建项目
echo "🔨 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo "🎉 部署完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 配置 .env.local 文件中的数据库连接"
    echo "2. 运行 'npm run dev' 启动开发服务器"
    echo "3. 或运行 'npm start' 启动生产服务器"
else
    echo "❌ 构建失败！"
    exit 1
fi
