#!/bin/bash

# Next.js 项目部署脚本
# 适用于 Linux 服务器 Node.js 20 环境

echo "🚀 开始部署 Next.js 项目..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node --version
npm --version

# 清理旧的构建文件
echo "🧹 清理旧文件..."
rm -rf node_modules package-lock.json .next

# 安装依赖
echo "📦 安装依赖..."
npm install

# 创建 lib 目录和关键文件
echo "🔍 检查并创建关键文件..."
mkdir -p lib

# 创建 lib/utils.ts
if [ ! -f "lib/utils.ts" ]; then
    echo "📝 创建 lib/utils.ts..."
    cat > lib/utils.ts << 'EOF'
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
EOF
fi

# 创建 lib/auth-client.ts
if [ ! -f "lib/auth-client.ts" ]; then
    echo "📝 创建 lib/auth-client.ts..."
    cat > lib/auth-client.ts << 'EOF'
'use client'

import Cookies from 'js-cookie'

export interface User {
  id: number
  username: string
  role: string
}

export function getAuthToken(): string | null {
  return Cookies.get('auth_token') || null
}

export function setAuthToken(token: string): void {
  Cookies.set('auth_token', token, { expires: 7 }) // 7 days
}

export function removeAuthToken(): void {
  Cookies.remove('auth_token')
}

export function getCurrentUser(): User | null {
  const userStr = Cookies.get('user_info')
  if (!userStr) return null

  try {
    return JSON.parse(userStr)
  } catch {
    return null
  }
}

export function setCurrentUser(user: User): void {
  Cookies.set('user_info', JSON.stringify(user), { expires: 7 })
}

export function removeCurrentUser(): void {
  Cookies.remove('user_info')
}

export function isAuthenticated(): boolean {
  return !!getAuthToken()
}

export function logout(): void {
  removeAuthToken()
  removeCurrentUser()
  window.location.href = '/login'
}

export function clearAuthCookies(): void {
  removeAuthToken()
  removeCurrentUser()
}
EOF
fi

# 创建 lib/auth.ts
if [ ! -f "lib/auth.ts" ]; then
    echo "📝 创建 lib/auth.ts..."
    cat > lib/auth.ts << 'EOF'
import jwt from 'jsonwebtoken'
import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'

export interface User {
  id: number
  username: string
  role: string
}

export function generateToken(user: User): string {
  return jwt.sign(
    {
      id: user.id,
      username: user.username,
      role: user.role,
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  )
}

export function verifyToken(token: string): User | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as jwt.JwtPayload & User
    return {
      id: decoded.id,
      username: decoded.username,
      role: decoded.role,
    }
  } catch {
    return null
  }
}

export function getCurrentUser(): User | null {
  try {
    const cookieStore = cookies()
    const token = cookieStore.get('auth_token')?.value

    if (!token) return null

    return verifyToken(token)
  } catch {
    return null
  }
}

export function getUserFromRequest(request: NextRequest): User | null {
  try {
    const token = request.cookies.get('auth_token')?.value

    if (!token) return null

    return verifyToken(token)
  } catch {
    return null
  }
}

export function requireAuth(): User {
  const user = getCurrentUser()
  if (!user) {
    throw new Error('Unauthorized')
  }
  return user
}

export function setAuthCookies(user: User): string {
  const token = generateToken(user)
  // Note: In Next.js API routes, you should set cookies using the response object
  // This function returns the token for manual cookie setting
  return token
}
EOF
fi

# 创建 lib/prisma.ts
if [ ! -f "lib/prisma.ts" ]; then
    echo "📝 创建 lib/prisma.ts..."
    cat > lib/prisma.ts << 'EOF'
import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma
EOF
fi

echo "✅ 所有关键文件已创建"

# 检查环境变量文件
if [ ! -f ".env.local" ]; then
    echo "⚠️  .env.local 文件不存在，从示例文件创建..."
    cp .env.example .env.local
    echo "📝 请编辑 .env.local 文件配置正确的数据库连接信息"
fi

# 构建项目
echo "🔨 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    echo "🎉 部署完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 配置 .env.local 文件中的数据库连接"
    echo "2. 运行 'npm run dev' 启动开发服务器"
    echo "3. 或运行 'npm start' 启动生产服务器"
else
    echo "❌ 构建失败！"
    exit 1
fi
