# Next.js 前端设置说明

## 认证配置

后端使用基于 SHA256 的密码认证系统。需要在后端的 `.env` 文件中配置：

```env
# 认证配置
AUTH_USERNAME=Zacharyhu
AUTH_PASSWORD_HASH=你的密码SHA256值
AUTH_SECRET_KEY=你的JWT密钥
```

### 生成密码 Hash

使用以下命令生成密码的 SHA256 hash：

```bash
python3 -c "import hashlib; print(hashlib.sha256('你的密码'.encode()).hexdigest())"
```

例如，如果使用密码 `test123456`：
```bash
python3 -c "import hashlib; print(hashlib.sha256('test123456'.encode()).hexdigest())"
# 输出: 85777f270ad7cf2a790981bbae3c4e484a1dc55e24a77390d692fbf1cffa12fa
```

### 完整的 .env 配置示例

在后端目录创建 `.env` 文件：

```env
# 认证配置
AUTH_USERNAME=Zacharyhu
AUTH_PASSWORD_HASH=85777f270ad7cf2a790981bbae3c4e484a1dc55e24a77390d692fbf1cffa12fa
AUTH_SECRET_KEY=your-secret-key-here-change-in-production
AUTH_TOKEN_EXPIRY=86400

# 其他必要配置...
```

### 测试登录

配置完成后，使用以下凭证登录：
- 用户名: `Zacharyhu`
- 密码: `test123456`

## 常见问题

### 1. 登录返回 HTML 而不是 JSON

确保：
- 后端在 8088 端口运行
- `.env` 文件配置正确
- 密码 hash 正确生成

### 2. 401 未授权错误

检查：
- 用户名是否正确（默认 `Zacharyhu`）
- 密码 hash 是否与输入的密码匹配
- JWT 密钥是否配置

### 3. 测试 API

```bash
# 测试登录
curl -X POST http://localhost:8088/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"Zacharyhu","password":"test123456"}'
```