import { test, expect } from '@playwright/test'

test.describe('认证系统', () => {
  test('未登录用户访问受保护页面应重定向到登录页', async ({ page }) => {
    await page.goto('/dashboard')
    await expect(page).toHaveURL('/login?from=%2Fdashboard')
  })
  
  test('登录页面应显示正确的UI元素', async ({ page }) => {
    await page.goto('/login')
    
    // 检查标题
    await expect(page.locator('h2')).toContainText('游戏监控系统')
    
    // 检查表单元素
    await expect(page.locator('input[id="username"]')).toBeVisible()
    await expect(page.locator('input[id="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toContainText('登录')
  })
  
  test('空表单提交应显示验证错误', async ({ page }) => {
    await page.goto('/login')
    
    // 直接点击登录按钮
    await page.click('button[type="submit"]')
    
    // 应该看到错误消息
    await expect(page.locator('text=请输入用户名')).toBeVisible()
    await expect(page.locator('text=请输入密码')).toBeVisible()
  })
  
  test('错误的凭证应显示错误消息', async ({ page }) => {
    await page.goto('/login')
    
    // 填写错误的凭证
    await page.fill('input[id="username"]', 'wronguser')
    await page.fill('input[id="password"]', 'wrongpass')
    await page.click('button[type="submit"]')
    
    // 应该看到错误消息
    await expect(page.locator('text=登录失败')).toBeVisible()
  })
  
  // 注意：以下测试需要正确的用户凭证
  test.skip('成功登录应重定向到仪表板', async ({ page }) => {
    await page.goto('/login')
    
    // 填写正确的凭证（需要根据实际情况修改）
    await page.fill('input[id="username"]', 'testuser')
    await page.fill('input[id="password"]', 'testpass')
    await page.click('button[type="submit"]')
    
    // 应该重定向到仪表板
    await expect(page).toHaveURL('/dashboard')
    await expect(page.locator('h1')).toContainText('仪表板')
  })
})