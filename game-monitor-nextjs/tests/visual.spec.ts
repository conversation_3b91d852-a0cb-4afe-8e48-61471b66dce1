import { test, expect } from '@playwright/test'

test.describe('视觉测试', () => {
  test('登录页面截图', async ({ page }) => {
    await page.goto('/login')
    await page.waitForLoadState('networkidle')
    
    // 截图
    await expect(page).toHaveScreenshot('login-page.png', {
      fullPage: true,
      animations: 'disabled',
    })
  })
  
  test('登录页面响应式布局', async ({ page }) => {
    await page.goto('/login')
    
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 })
    await expect(page).toHaveScreenshot('login-page-mobile.png')
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 })
    await expect(page).toHaveScreenshot('login-page-tablet.png')
    
    // 测试桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 })
    await expect(page).toHaveScreenshot('login-page-desktop.png')
  })
  
  // 需要登录后的页面测试
  test.skip('仪表板页面截图', async ({ page, context }) => {
    // 模拟登录状态（设置cookie）
    await context.addCookies([
      {
        name: 'game-monitor-token',
        value: 'test-token',
        domain: 'localhost',
        path: '/',
      },
      {
        name: 'game-monitor-user',
        value: 'testuser',
        domain: 'localhost',
        path: '/',
      },
    ])
    
    await page.goto('/dashboard')
    await page.waitForLoadState('networkidle')
    
    await expect(page).toHaveScreenshot('dashboard-page.png', {
      fullPage: true,
      animations: 'disabled',
    })
  })
})