# 部署指南

## 目录
- [开发环境部署](#开发环境部署)
- [生产环境部署](#生产环境部署)
- [Docker 部署](#docker-部署)
- [Vercel 部署](#vercel-部署)
- [常见问题](#常见问题)

## 开发环境部署

### 前置要求
- Node.js 18+ 
- MySQL 5.7+
- npm 或 yarn

### 步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd game-monitor-nextjs
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
```

编辑 `.env.local`:
```env
# 认证配置
AUTH_USERNAME=你的用户名
AUTH_PASSWORD_HASH=你的密码hash
AUTH_SECRET_KEY=你的JWT密钥
AUTH_TOKEN_EXPIRY=86400

# 数据库配置
DATABASE_URL="mysql://用户名:密码@localhost:3306/数据库名"
```

4. **生成 Prisma Client**
```bash
npx prisma generate
```

5. **启动开发服务器**
```bash
npm run dev
```

## 生产环境部署

### 使用 PM2

1. **构建应用**
```bash
npm run build
```

2. **安装 PM2**
```bash
npm install -g pm2
```

3. **创建 PM2 配置文件** `ecosystem.config.js`:
```javascript
module.exports = {
  apps: [{
    name: 'game-monitor',
    script: 'npm',
    args: 'start',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G'
  }]
}
```

4. **启动应用**
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 使用 Nginx 反向代理

1. **安装 Nginx**
```bash
sudo apt update
sudo apt install nginx
```

2. **配置 Nginx** `/etc/nginx/sites-available/game-monitor`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

3. **启用配置**
```bash
sudo ln -s /etc/nginx/sites-available/game-monitor /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Docker 部署

1. **创建 Dockerfile**
```dockerfile
FROM node:18-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package*.json ./
RUN npm ci

FROM node:18-alpine AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npx prisma generate
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app
ENV NODE_ENV production
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder /app/prisma ./prisma

USER nextjs
EXPOSE 3000
ENV PORT 3000

CMD ["node", "server.js"]
```

2. **创建 docker-compose.yml**
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=mysql://root:password@db:3306/game_monitor
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: game_monitor
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped

volumes:
  mysql_data:
```

3. **构建并运行**
```bash
docker-compose up -d
```

## Vercel 部署

1. **安装 Vercel CLI**
```bash
npm i -g vercel
```

2. **配置项目**
在 `next.config.mjs` 中添加:
```javascript
const nextConfig = {
  output: 'standalone',
}
```

3. **部署**
```bash
vercel
```

4. **配置环境变量**
在 Vercel 控制台中添加所有必要的环境变量

5. **配置数据库**
- 使用 Vercel Postgres 或
- 使用外部 MySQL 服务（如 PlanetScale）

## 环境变量说明

### 必需的环境变量
```env
# 认证相关
AUTH_USERNAME=            # 登录用户名
AUTH_PASSWORD_HASH=       # 密码的 SHA256 hash
AUTH_SECRET_KEY=          # JWT 签名密钥
AUTH_TOKEN_EXPIRY=        # Token 过期时间（秒）

# 数据库
DATABASE_URL=             # MySQL 连接字符串
```

### 可选的环境变量
```env
# 应用配置
PORT=3000                 # 应用端口
NODE_ENV=production       # 环境标识
```

## 性能优化

### 1. 启用缓存
```javascript
// next.config.mjs
const nextConfig = {
  experimental: {
    incrementalCacheHandlerPath: require.resolve('./cache-handler.js'),
  },
}
```

### 2. 图片优化
```javascript
// next.config.mjs
const nextConfig = {
  images: {
    domains: ['your-image-domains.com'],
    formats: ['image/avif', 'image/webp'],
  },
}
```

### 3. 数据库连接池
```javascript
// prisma/schema.prisma
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  connectionLimit = 10
}
```

## 监控和日志

### 使用 Sentry
1. 安装 Sentry
```bash
npm install @sentry/nextjs
```

2. 配置 Sentry
```javascript
// sentry.client.config.js
import * as Sentry from "@sentry/nextjs";

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  tracesSampleRate: 1.0,
});
```

### 日志管理
使用 Winston 或 Pino 进行日志管理：
```bash
npm install winston
```

## 备份策略

### 数据库备份
```bash
# 备份
mysqldump -u username -p database_name > backup.sql

# 恢复
mysql -u username -p database_name < backup.sql
```

### 自动备份脚本
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u root -p$MYSQL_PASSWORD game_monitor > /backups/db_$DATE.sql
find /backups -name "db_*.sql" -mtime +7 -delete
```

## 常见问题

### Q: 端口被占用怎么办？
A: 修改 `.env.local` 中的 `PORT` 变量或使用 `PORT=3001 npm start`

### Q: 数据库连接失败？
A: 检查 `DATABASE_URL` 格式是否正确，确保 MySQL 服务正在运行

### Q: Prisma Client 错误？
A: 运行 `npx prisma generate` 重新生成客户端

### Q: 生产环境性能问题？
A: 
- 启用 Next.js 生产优化
- 使用 CDN 加速静态资源
- 配置数据库索引
- 考虑使用 Redis 缓存

### Q: 如何升级？
A:
1. 备份数据库
2. 拉取最新代码
3. 运行 `npm install`
4. 运行 `npx prisma generate`
5. 运行 `npm run build`
6. 重启应用

## 安全建议

1. **使用 HTTPS**
   - 配置 SSL 证书
   - 强制 HTTPS 重定向

2. **环境变量安全**
   - 不要提交 `.env` 文件
   - 使用强密码和密钥
   - 定期轮换密钥

3. **数据库安全**
   - 限制数据库访问 IP
   - 使用最小权限原则
   - 定期更新数据库

4. **应用安全**
   - 保持依赖更新
   - 配置 CSP 头
   - 启用速率限制