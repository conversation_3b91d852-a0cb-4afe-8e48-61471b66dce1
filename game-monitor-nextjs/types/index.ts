export interface User {
  id: string
  username: string
  token?: string
}

export interface Game {
  id: number
  name: string
  description?: string
  source_site: string
  game_url?: string
  thumbnail_url?: string
  tags?: string[]
  created_at: string
  trend_status?: 'explosive' | 'rising' | 'stable' | 'none'
  growth_rate?: number
  heat_score?: number
}

export interface Stats {
  total_games: number
  today_games: number
  trending_count: number
  explosive_growth: number
  active_sites: number
}

export interface Trend {
  id: number
  game_id: number
  keyword: string
  trend_value: number
  growth_rate: number
  date: string
}

export interface RedditMetric {
  id: number
  game_id: number
  game_name: string
  heat_score: number
  growth_rate: number
  post_count_24h: number
  total_upvotes_24h: number
  total_comments_24h: number
  top_post_title: string
  top_post_score: number
  top_post_url: string
  avg_comments: number
  unique_authors: number
  sentiment: 'positive' | 'neutral' | 'negative'
  subreddit_distribution: Record<string, number>
  created_at: string
  status: 'success' | 'failed' | 'no_data'
  error_message?: string
}

export interface RedditTrends {
  game_id: number
  game_name: string
  metrics: RedditMetric[]
  latest: RedditMetric | null
}

export interface UnifiedTrend {
  game: Game
  reddit: {
    heat_score: number
    growth_rate: number
    post_count: number
    upvotes: number
    comments: number
    top_post: {
      title: string
      score: number
      url: string
    }
    sentiment: string
    status: string
  } | null
  google: {
    value: number
    growth_rate: number
    status: string
    updated_at: string
  } | null
  composite_score: number
  last_updated: string
}

export interface SchedulerStatus {
  name: string
  status: 'running' | 'stopped' | 'error'
  last_run?: string
  next_run?: string
  stats?: Record<string, any>
}