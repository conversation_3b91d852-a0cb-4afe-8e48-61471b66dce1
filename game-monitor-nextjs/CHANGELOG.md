# 更新日志

## [1.0.1] - 2025-08-04

### 🔧 优化改进

#### 代码清理
- 🗑️ 移除未使用的 UI 组件（badge、button、card、checkbox、dialog、dropdown-menu、input、label、select、switch、textarea、tooltip）
- 🗑️ 删除未使用的 api.ts 文件
- 🔄 优化 app/page.tsx 直接重定向到登录页
- 📦 依赖进一步优化到 408 个包（从 472 减少到 408，优化 14%）

#### 文档更新
- 📝 更新 README.md 反映最新的项目状态
- 📝 添加代码清理和优化的说明
- 📝 更新项目结构说明，移除已删除的组件

### 技术债务清理
- ✅ 所有未使用的代码已移除
- ✅ 项目结构更加精简清晰
- ✅ 提高了代码可维护性

## [1.0.0] - 2025-08-04

### 🎉 首次发布

#### 新增功能
- ✨ 完整的 Next.js 14 应用，替代原 Flask + Create React App 架构
- 🔐 JWT + httpOnly Cookies 安全认证系统
- 📊 仪表板页面，展示实时统计数据
- 🎮 游戏中心，支持搜索、筛选和分页
- 📈 趋势分析，包括热门趋势、趋势游戏和暴涨警报
- 🔍 监控中心，实时查看调度器状态
- 📱 响应式设计，支持移动端访问
- 🗄️ Prisma ORM 直连 MySQL 数据库

#### 技术改进
- 📦 依赖包从 1400+ 减少到 472（减少 66%）
- ⚡ 服务端渲染（SSR）提升首屏加载速度
- 🔧 零配置开发环境，开箱即用
- 🧪 集成 Playwright E2E 测试框架
- 🎨 Tailwind CSS 替代 Ant Design

#### 架构优化
- 🏗️ 统一的 TypeScript 全栈开发
- 🚀 直连数据库，减少 API 转发层
- 📁 清晰的项目结构和代码组织
- 🔒 中间件自动路由保护
- 🎯 模块化组件设计

### 迁移说明
从旧版本迁移：
1. 确保 MySQL 数据库正在运行
2. 配置 `.env.local` 文件
3. 运行 `npm install && npx prisma generate`
4. 启动 `npm run dev`

### 已知问题
- 图表组件（Recharts）尚未实现
- WebSocket 实时更新功能待开发
- 暗色模式支持计划中

### 贡献者
- @zachryhu - 项目发起人
- Claude - AI 架构师