generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Game {
  id            Int            @id @default(autoincrement())
  name          String         @db.VarChar(255)
  title         String?        @db.VarChar(255)
  description   String?        @db.Text
  thumbnailUrl  String?        @map("thumbnail_url") @db.VarChar(500)
  gameUrl       String?        @map("game_url") @db.VarChar(500)
  embedUrl      String?        @map("embed_url") @db.VarChar(500)
  sourceSite    String?        @map("source_site") @db.VarChar(255)
  sourceUrl     String?        @map("source_url") @db.VarChar(500)
  genre         String?        @db.VarChar(100)
  tags          String?        @db.Text
  author        String?        @db.VarChar(255)
  playerCount   String?        @map("player_count") @db.VarChar(50)
  rating        Decimal?       @db.Decimal(3, 2)
  scrapeMethod  String?        @map("scrape_method") @db.<PERSON>ar<PERSON>har(20)
  createdAt     DateTime?      @map("created_at") @db.DateTime(0)
  updatedAt     DateTime?      @updatedAt @map("updated_at") @db.DateTime(0)
  hashId        String?        @unique(map: "hash_id") @map("hash_id") @db.VarChar(64)
  isActive      Boolean?       @map("is_active")
  alerts        Alert[]
  keywords      Keyword[]
  redditMetrics RedditMetric[]
  trends        Trend[]

  @@index([sourceSite], map: "idx_source")
  @@index([createdAt], map: "idx_created")
  @@index([hashId], map: "idx_hash")
  @@index([sourceSite, createdAt, isActive], map: "idx_games_compound")
  @@index([name], map: "idx_name")
  @@map("games")
}

model Trend {
  id              Int       @id @default(autoincrement())
  keyword         String    @db.VarChar(255)
  gameId          Int?      @map("game_id")
  trendValue      Int?      @map("trend_value")
  comparisonValue Int?      @map("comparison_value")
  growthRate      Decimal?  @map("growth_rate") @db.Decimal(8, 2)
  trendData       Json?     @map("trend_data")
  date            DateTime? @db.Date
  timeframe       String?   @db.VarChar(20)
  region          String?   @db.VarChar(10)
  createdAt       DateTime? @map("created_at") @db.DateTime(0)
  game            Game?     @relation(fields: [gameId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "trends_ibfk_1")

  @@index([growthRate], map: "idx_growth")
  @@index([gameId, date], map: "idx_game_date")
  @@index([keyword, date, growthRate], map: "idx_trends_compound")
  @@index([date], map: "idx_date")
  @@index([keyword], map: "idx_keyword")
  @@map("trends")
}

model Keyword {
  id                Int       @id @default(autoincrement())
  gameId            Int?      @map("game_id")
  keyword           String    @db.VarChar(255)
  relatedKeywords   Json?     @map("related_keywords")
  searchSuggestions Json?     @map("search_suggestions")
  searchVolume      Int?      @map("search_volume")
  competitionLevel  String?   @map("competition_level") @db.VarChar(20)
  avgCpc            Decimal?  @map("avg_cpc") @db.Decimal(6, 2)
  createdAt         DateTime? @map("created_at") @db.DateTime(0)
  updatedAt         DateTime? @updatedAt @map("updated_at") @db.DateTime(0)
  game              Game?     @relation(fields: [gameId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "keywords_ibfk_1")

  @@unique([gameId, keyword], map: "unique_game_keyword")
  @@index([gameId], map: "idx_game_id")
  @@index([searchVolume], map: "idx_volume")
  @@index([keyword], map: "idx_keyword")
  @@map("keywords")
}

model Alert {
  id             Int       @id @default(autoincrement())
  gameId         Int?      @map("game_id")
  keyword        String?   @db.VarChar(255)
  alertType      String?   @map("alert_type") @db.VarChar(50)
  thresholdValue Decimal?  @map("threshold_value") @db.Decimal(8, 2)
  currentValue   Decimal?  @map("current_value") @db.Decimal(8, 2)
  message        String?   @db.Text
  isSent         Boolean?  @map("is_sent")
  sentAt         DateTime? @map("sent_at") @db.DateTime(0)
  createdAt      DateTime? @map("created_at") @db.DateTime(0)
  game           Game?     @relation(fields: [gameId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "alerts_ibfk_1")

  @@index([isSent], map: "idx_sent")
  @@index([alertType], map: "idx_type")
  @@index([alertType, isSent, createdAt], map: "idx_alerts_compound")
  @@index([createdAt], map: "idx_created")
  @@index([gameId], map: "idx_game_id")
  @@map("alerts")
}

model SiteConfig {
  id          Int       @id @default(autoincrement())
  siteName    String    @map("site_name") @db.VarChar(255)
  siteUrl     String    @unique(map: "site_url") @map("site_url") @db.VarChar(500)
  selectors   Json?
  isActive    Boolean?  @map("is_active")
  lastScraped DateTime? @map("last_scraped") @db.DateTime(0)
  scrapeCount Int?      @map("scrape_count")
  errorCount  Int?      @map("error_count")
  createdAt   DateTime? @map("created_at") @db.DateTime(0)
  updatedAt   DateTime? @updatedAt @map("updated_at") @db.DateTime(0)

  @@index([isActive], map: "idx_active")
  @@index([siteName], map: "idx_site_name")
  @@map("site_configs")
}

model RedditMetric {
  id                    BigInt    @id @default(autoincrement())
  gameId                Int       @map("game_id")
  heatScore             Int?      @default(0) @map("heat_score")
  growthRate            Float?    @default(0) @map("growth_rate") @db.Float
  postCount24h          Int?      @default(0) @map("post_count_24h")
  totalUpvotes24h       Int?      @default(0) @map("total_upvotes_24h")
  totalComments24h      Int?      @default(0) @map("total_comments_24h")
  topPostTitle          String?   @map("top_post_title") @db.Text
  topPostScore          Int?      @default(0) @map("top_post_score")
  topPostUrl            String?   @map("top_post_url") @db.VarChar(500)
  avgComments           Float?    @default(0) @map("avg_comments") @db.Float
  uniqueAuthors         Int?      @default(0) @map("unique_authors")
  sentiment             String?   @default("neutral") @db.VarChar(20)
  subredditDistribution Json?     @map("subreddit_distribution")
  rawData               Json?     @map("raw_data")
  status                String?   @default("success") @db.VarChar(20)
  errorMessage          String?   @map("error_message") @db.Text
  createdAt             DateTime? @default(now()) @map("created_at") @db.Timestamp(0)
  game                  Game      @relation(fields: [gameId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "reddit_metrics_ibfk_1")

  @@index([gameId, createdAt], map: "idx_reddit_game_created")
  @@index([heatScore], map: "idx_reddit_heat")
  @@index([growthRate], map: "idx_reddit_growth")
  @@map("reddit_metrics")
}
