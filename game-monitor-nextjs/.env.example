# Next.js 环境变量配置示例
# 复制此文件为 .env.local 并根据您的环境修改

# ========== 认证配置 ==========
# 登录用户名
AUTH_USERNAME=Zacharyhu

# 密码的 SHA256 hash 值
# 默认密码: test123456
# 生成方法: python3 -c "import hashlib; print(hashlib.sha256('你的密码'.encode()).hexdigest())"
AUTH_PASSWORD_HASH=85777f270ad7cf2a790981bbae3c4e484a1dc55e24a77390d692fbf1cffa12fa

# JWT 密钥（生产环境请使用强密码）
AUTH_SECRET_KEY=your-jwt-secret-key-change-in-production

# Token 过期时间（秒）
AUTH_TOKEN_EXPIRY=86400  # 24小时

# ========== 数据库配置 ==========
# MySQL 连接字符串
# 格式: mysql://用户名:密码@主机:端口/数据库名
DATABASE_URL="mysql://game-monitor:game-monitor@localhost:3306/game-monitor"

# ========== API 配置（保留用于兼容） ==========
# 这些配置已不再使用，因为系统直接连接数据库
# NEXT_PUBLIC_API_URL=http://localhost:8087/api
# BACKEND_API_URL=http://localhost:8087/api