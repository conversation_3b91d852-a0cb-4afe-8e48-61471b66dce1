# 游戏监控系统架构文档

## 项目概述

游戏监控系统是一个现代化的 Web 应用，用于监控和分析来自 27 个游戏网站的数据，提供趋势分析、热度监控和系统状态管理功能。

### 核心特性
- 🎮 **游戏数据管理**: 收录并展示来自 27 个网站的游戏信息
- 📊 **趋势分析**: 基于 Google Trends 的热度分析和暴涨警报
- 🔍 **Reddit 监控**: 游戏社区讨论热度追踪
- 📈 **系统监控**: 调度器状态和系统健康状态实时监控
- 🔐 **安全认证**: JWT + httpOnly Cookies 的安全认证机制

## 技术架构

### 技术栈
```
Frontend:
├── Next.js 14 (App Router) - React 框架
├── TypeScript - 类型安全
├── Tailwind CSS - 样式系统
├── Lucide Icons - 图标库
└── Playwright - E2E 测试

Backend:
├── Next.js API Routes - API 服务
├── Prisma ORM - 数据库 ORM
├── MySQL - 数据存储
└── JWT - 认证机制
```

### 架构优势
1. **全栈 TypeScript**: 前后端类型统一，提高开发效率
2. **直连数据库**: 相比原 Flask 架构，减少了一层 API 转发
3. **服务端渲染**: 提升首屏加载速度和 SEO
4. **现代化工具链**: 开箱即用，无需复杂配置

## 项目结构

```
game-monitor-nextjs/
├── app/                        # Next.js App Router
│   ├── (auth)/                # 公开路由组
│   │   └── login/             # 登录页面
│   ├── (dashboard)/           # 受保护路由组
│   │   ├── dashboard/         # 仪表板
│   │   ├── games/             # 游戏中心
│   │   ├── trends/            # 趋势分析
│   │   └── monitor/           # 监控中心
│   └── api/                   # API 路由
│       ├── auth/              # 认证相关
│       ├── games/             # 游戏数据
│       ├── trends/            # 趋势分析
│       └── scheduler/         # 调度器日志
├── components/                # React 组件
│   ├── AppLayout.tsx         # 应用布局
│   └── ui/                   # UI 基础组件
├── lib/                      # 工具函数
│   ├── prisma.ts            # Prisma 客户端
│   ├── auth.ts              # 服务端认证
│   └── auth-client.ts       # 客户端认证
├── prisma/                   # 数据库配置
│   └── schema.prisma        # 数据模型定义
└── middleware.ts            # 路由中间件
```

## 数据模型

### 核心数据表
1. **games** - 游戏信息表
   - 存储游戏基本信息、来源、标签等
   - 通过 hash_id 去重

2. **trends** - 趋势数据表
   - Google Trends 数据
   - 增长率计算和趋势分析

3. **reddit_metrics** - Reddit 指标表
   - 社区讨论热度
   - 帖子和评论统计

4. **alerts** - 警报表
   - 暴涨警报记录
   - 通知状态管理

5. **site_configs** - 网站配置表
   - 爬虫配置
   - 网站状态管理

## 功能模块

### 1. 认证系统
- **技术方案**: JWT + httpOnly Cookies
- **安全特性**:
  - 密码 SHA256 加密
  - Token 存储在 httpOnly Cookie
  - 中间件自动路由保护

### 2. 仪表板
- **实时数据展示**: 游戏总数、今日新增、热度暴涨
- **快速导航**: 各功能模块入口
- **自动刷新**: 每分钟更新数据

### 3. 游戏中心
- **功能特性**:
  - 分页浏览（每页 20 条）
  - 搜索功能（名称、描述、标签）
  - 来源筛选（27 个网站）
  - 响应式卡片布局

### 4. 趋势分析
- **三大模块**:
  - 热门趋势: 关键词增长率排行
  - 趋势游戏: 热度等级和警报级别
  - 暴涨警报: 超过阈值的游戏警报

### 5. 监控中心
- **系统监控**:
  - 5 个调度器状态实时展示
  - 成功率统计
  - 错误日志查看
  - 30 秒自动刷新

## API 设计

### RESTful API 端点
```
认证相关:
POST   /api/auth/login    - 用户登录
POST   /api/auth/logout   - 用户登出

数据查询:
GET    /api/games         - 游戏列表
GET    /api/stats         - 统计数据
GET    /api/trends        - 趋势数据
GET    /api/trends/all    - 所有趋势
GET    /api/trending-games - 趋势游戏
GET    /api/surge-alerts  - 暴涨警报
GET    /api/scheduler/logs - 调度器日志
```

### 数据库查询优化
- 使用 Prisma 的关系查询减少 N+1 问题
- 合理使用索引提升查询性能
- 分页查询避免大数据量传输

## 部署方案

### 开发环境
```bash
# 1. 安装依赖
npm install

# 2. 配置环境变量
cp .env.example .env.local

# 3. 生成 Prisma Client
npx prisma generate

# 4. 启动开发服务器
npm run dev
```

### 生产部署
```bash
# 1. 构建应用
npm run build

# 2. 启动生产服务器
npm start
```

### 部署选项
1. **Vercel** (推荐)
   - 零配置部署
   - 自动 CI/CD
   - 边缘网络加速

2. **Docker**
   - 容器化部署
   - 易于扩展
   - 环境一致性

3. **传统服务器**
   - PM2 进程管理
   - Nginx 反向代理
   - 手动部署流程

## 可扩展性设计

### 1. 模块化架构
- 组件解耦，易于维护
- API 路由独立，便于扩展
- 数据模型清晰，方便迭代

### 2. 性能优化空间
- [ ] 添加 Redis 缓存层
- [ ] 实现 WebSocket 实时更新
- [ ] 图片 CDN 加速
- [ ] 数据库读写分离

### 3. 功能扩展建议
- [ ] 图表可视化（Recharts）
- [ ] 数据导出功能
- [ ] 多语言支持
- [ ] 暗色模式
- [ ] 移动端 App

## 安全考虑

1. **认证安全**
   - JWT 签名验证
   - httpOnly Cookie 防 XSS
   - CSRF 保护

2. **数据安全**
   - SQL 注入防护（Prisma ORM）
   - 输入验证
   - 错误信息脱敏

3. **部署安全**
   - HTTPS 加密传输
   - 环境变量管理
   - 定期安全更新

## 监控和维护

### 日志管理
- 应用日志: `/logs/app.log`
- 调度器日志: `/logs/*_manager.log`
- 错误追踪: 集成 Sentry（可选）

### 性能监控
- 页面加载时间
- API 响应时间
- 数据库查询性能

### 备份策略
- 数据库定期备份
- 代码版本管理
- 配置文件备份

## 总结

本系统通过现代化的技术栈和架构设计，成功实现了：

1. **架构简化**: 从 Flask + React 迁移到 Next.js 全栈
2. **性能提升**: SSR + 数据库直连，响应速度更快
3. **开发体验**: TypeScript + 热更新，开发效率更高
4. **功能完整**: 保留所有核心功能，界面更现代
5. **易于维护**: 代码结构清晰，文档完善

这是一个生产就绪的系统，可以直接部署使用，也为未来的功能扩展预留了充分的空间。