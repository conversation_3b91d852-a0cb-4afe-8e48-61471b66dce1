import { RedditMetric } from '@/types'
import { TrendingUp, TrendingDown, MessageCircle, ThumbsUp, Users, ExternalLink } from 'lucide-react'

interface RedditMetricsCardProps {
  metric: RedditMetric
  showGame?: boolean
}

export function RedditMetricsCard({ metric, showGame = true }: RedditMetricsCardProps) {
  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-100'
      case 'negative': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHeatColor = (score: number) => {
    if (score >= 80) return 'bg-red-500'
    if (score >= 60) return 'bg-orange-500'
    if (score >= 40) return 'bg-yellow-500'
    if (score >= 20) return 'bg-blue-500'
    return 'bg-gray-400'
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      {showGame && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{metric.game_name}</h3>
      )}
      
      {/* 热度和增长率 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <div className="text-2xl font-bold">{metric.heat_score}</div>
          <div className="text-sm text-gray-500">热度</div>
        </div>
        
        <div className={`flex items-center gap-1 text-lg font-semibold ${
          metric.growth_rate > 0 ? 'text-green-600' : 'text-red-600'
        }`}>
          {metric.growth_rate > 0 ? (
            <TrendingUp className="w-5 h-5" />
          ) : (
            <TrendingDown className="w-5 h-5" />
          )}
          {metric.growth_rate > 0 ? '+' : ''}{metric.growth_rate.toFixed(1)}%
        </div>
      </div>

      {/* 热度条 */}
      <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
        <div 
          className={`h-2 rounded-full ${getHeatColor(metric.heat_score)}`}
          style={{ width: `${metric.heat_score}%` }}
        />
      </div>

      {/* 统计数据 */}
      <div className="grid grid-cols-3 gap-4 mb-4">
        <div className="text-center">
          <div className="flex items-center justify-center text-gray-600 mb-1">
            <MessageCircle className="w-4 h-4 mr-1" />
          </div>
          <div className="text-lg font-semibold">{metric.post_count_24h}</div>
          <div className="text-xs text-gray-500">帖子</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center text-gray-600 mb-1">
            <ThumbsUp className="w-4 h-4 mr-1" />
          </div>
          <div className="text-lg font-semibold">{metric.total_upvotes_24h.toLocaleString()}</div>
          <div className="text-xs text-gray-500">点赞</div>
        </div>
        
        <div className="text-center">
          <div className="flex items-center justify-center text-gray-600 mb-1">
            <Users className="w-4 h-4 mr-1" />
          </div>
          <div className="text-lg font-semibold">{metric.unique_authors}</div>
          <div className="text-xs text-gray-500">作者</div>
        </div>
      </div>

      {/* 情感分析 */}
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm text-gray-600">社区情感</span>
        <span className={`px-2 py-1 rounded text-xs font-medium ${getSentimentColor(metric.sentiment)}`}>
          {metric.sentiment === 'positive' ? '积极' : metric.sentiment === 'negative' ? '消极' : '中性'}
        </span>
      </div>

      {/* 热门帖子 */}
      {metric.top_post_title && (
        <div className="bg-gray-50 rounded p-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900 line-clamp-2">
                {metric.top_post_title}
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {metric.top_post_score} 赞
              </div>
            </div>
            {metric.top_post_url && (
              <a 
                href={metric.top_post_url}
                target="_blank"
                rel="noopener noreferrer"
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                <ExternalLink className="w-4 h-4" />
              </a>
            )}
          </div>
        </div>
      )}

      {/* 子版块分布 */}
      {metric.subreddit_distribution && Object.keys(metric.subreddit_distribution).length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="text-sm font-medium text-gray-700 mb-2">子版块分布</div>
          <div className="flex flex-wrap gap-1">
            {Object.entries(metric.subreddit_distribution).map(([subreddit, count]) => (
              <span
                key={subreddit}
                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800"
              >
                r/{subreddit}: {count}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}