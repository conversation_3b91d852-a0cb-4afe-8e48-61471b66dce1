'use client'

import { RedditMetric } from '@/types'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'

interface RedditTrendChartProps {
  metrics: RedditMetric[]
  gameName: string
}

export function RedditTrendChart({ metrics, gameName }: RedditTrendChartProps) {
  // 按时间排序并格式化数据
  const chartData = metrics
    .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
    .map(metric => ({
      date: new Date(metric.created_at).toLocaleDateString('zh-CN', { 
        month: '2-digit', 
        day: '2-digit' 
      }),
      heat_score: metric.heat_score,
      posts: metric.post_count_24h,
      upvotes: metric.total_upvotes_24h,
      comments: metric.total_comments_24h,
    }))

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {gameName} - Reddit 趋势图表
      </h3>
      
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={chartData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis yAxisId="left" />
          <YAxis yAxisId="right" orientation="right" />
          <Tooltip />
          <Legend />
          
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="heat_score"
            stroke="#FF4500"
            strokeWidth={2}
            name="热度分数"
            dot={{ fill: '#FF4500' }}
          />
          
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="posts"
            stroke="#4285F4"
            strokeWidth={2}
            name="帖子数"
            dot={{ fill: '#4285F4' }}
          />
          
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="upvotes"
            stroke="#34A853"
            strokeWidth={2}
            name="点赞数"
            dot={{ fill: '#34A853' }}
          />
        </LineChart>
      </ResponsiveContainer>
      
      <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {metrics[metrics.length - 1]?.heat_score || 0}
          </div>
          <div className="text-gray-600">当前热度</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {metrics[metrics.length - 1]?.post_count_24h || 0}
          </div>
          <div className="text-gray-600">24小时帖子</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {(metrics[metrics.length - 1]?.total_upvotes_24h || 0).toLocaleString()}
          </div>
          <div className="text-gray-600">24小时点赞</div>
        </div>
      </div>
    </div>
  )
}