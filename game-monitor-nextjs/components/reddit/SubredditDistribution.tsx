'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Toolt<PERSON> } from 'recharts'

interface SubredditDistributionProps {
  distribution: Record<string, number>
}

const COLORS = [
  '#FF4500', // Reddit Orange
  '#4285F4', // Blue
  '#34A853', // Green
  '#FBBC04', // Yellow
  '#EA4335', // Red
  '#9333EA', // Purple
  '#F59E0B', // Amber
  '#10B981', // Emerald
]

export function SubredditDistribution({ distribution }: SubredditDistributionProps) {
  const data = Object.entries(distribution)
    .map(([name, value]) => ({ name: `r/${name}`, value }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 8) // 只显示前8个

  const total = data.reduce((sum, item) => sum + item.value, 0)

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">子版块分布</h3>
      
      <ResponsiveContainer width="100%" height={300}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>

      <div className="mt-4">
        <div className="grid grid-cols-2 gap-2 text-sm">
          {data.map((item, index) => (
            <div key={item.name} className="flex items-center gap-2">
              <div 
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: COLORS[index % COLORS.length] }}
              />
              <span className="text-gray-700">{item.name}</span>
              <span className="text-gray-500 ml-auto">
                {item.value} ({((item.value / total) * 100).toFixed(1)}%)
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}