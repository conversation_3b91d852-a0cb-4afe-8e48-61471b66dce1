import { RedditMetric } from '@/types'

interface RedditHeatmapProps {
  metrics: RedditMetric[]
  onGameClick?: (gameId: number) => void
}

export function RedditHeatmap({ metrics, onGameClick }: RedditHeatmapProps) {
  const getHeatColor = (score: number) => {
    if (score >= 80) return 'bg-red-600 hover:bg-red-700'
    if (score >= 60) return 'bg-orange-500 hover:bg-orange-600'
    if (score >= 40) return 'bg-yellow-500 hover:bg-yellow-600'
    if (score >= 20) return 'bg-blue-500 hover:bg-blue-600'
    return 'bg-gray-400 hover:bg-gray-500'
  }

  const sortedMetrics = [...metrics].sort((a, b) => b.heat_score - a.heat_score)

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Reddit 热度地图</h3>
      
      <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
        {sortedMetrics.map((metric) => (
          <div
            key={metric.id}
            onClick={() => onGameClick?.(metric.game_id)}
            className={`relative group cursor-pointer rounded-lg p-3 text-white transition-all transform hover:scale-105 ${getHeatColor(metric.heat_score)}`}
            title={`${metric.game_name}: ${metric.heat_score} 热度`}
          >
            <div className="text-center">
              <div className="text-lg font-bold">{metric.heat_score}</div>
              <div className="text-xs opacity-90 truncate">{metric.game_name}</div>
            </div>
            
            {/* 悬停时显示详细信息 */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 p-2 bg-gray-900 text-white text-xs rounded shadow-lg opacity-0 group-hover:opacity-100 pointer-events-none z-10">
              <div className="font-semibold mb-1">{metric.game_name}</div>
              <div>热度: {metric.heat_score}</div>
              <div>增长: {metric.growth_rate > 0 ? '+' : ''}{metric.growth_rate.toFixed(1)}%</div>
              <div>帖子: {metric.post_count_24h}</div>
              <div>点赞: {metric.total_upvotes_24h}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 图例 */}
      <div className="mt-6 flex items-center justify-center gap-4 text-sm">
        <span className="text-gray-600">热度等级:</span>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-gray-400 rounded"></div>
          <span>冷门</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-blue-500 rounded"></div>
          <span>普通</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-yellow-500 rounded"></div>
          <span>温热</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-orange-500 rounded"></div>
          <span>火热</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-red-600 rounded"></div>
          <span>爆款</span>
        </div>
      </div>
    </div>
  )
}