#!/bin/bash

# 游戏监控系统 Next.js 启动脚本

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}游戏监控系统 - Next.js 版本${NC}"
echo ""

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo -e "${RED}错误：请在 game-monitor-nextjs 目录下运行此脚本${NC}"
    exit 1
fi

# 检查依赖是否已安装
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}检测到依赖未安装，正在安装...${NC}"
    npm install
fi

# 检查后端是否运行
echo -e "${YELLOW}检查后端服务...${NC}"
if curl -s http://localhost:8088/api/stats > /dev/null 2>&1; then
    echo -e "${GREEN}✓ 后端服务正在运行${NC}"
else
    echo -e "${RED}✗ 后端服务未运行${NC}"
    echo -e "${YELLOW}请先启动后端服务：${NC}"
    echo "cd .. && ./manage.sh start"
    exit 1
fi

# 启动开发服务器
echo -e "${GREEN}启动 Next.js 开发服务器...${NC}"
npm run dev