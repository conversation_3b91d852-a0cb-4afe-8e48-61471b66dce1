# Docker Compose Override Example
# 复制为 docker-compose.override.yml 来自定义配置

version: '3.8'

services:
  app:
    # 如果端口8088被占用，可以修改为其他端口
    ports:
      - "8089:8088"  # 外部端口:内部端口
    
    # 如果需要更多资源，可以调整限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
  
  mysql:
    # 如果需要外部访问MySQL (不推荐)
    # ports:
    #   - "3307:3306"
    
    # 自定义MySQL配置
    command: >
      --default-authentication-plugin=mysql_native_password
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
  
  redis:
    # 如果需要外部访问Redis (不推荐)
    # ports:
    #   - "6380:6379"
    
    # Redis密码保护 (推荐)
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    
  scheduler:
    # 如果需要调整调度器资源
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'