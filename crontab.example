# Game Monitor Crontab Configuration
# Copy this to your crontab with: crontab -e

# Set environment variables for cron jobs
SHELL=/bin/bash
PATH=/usr/local/bin:/usr/bin:/bin
MAILTO=""

# Game Monitor project directory (change this to your actual path)
PROJECT_DIR=/path/to/game-monitor

# Run scraper every hour
0 * * * * $PROJECT_DIR/scripts/run_scraper.sh

# Alternative: Run scraper twice daily (6 AM and 6 PM)
# 0 6,18 * * * $PROJECT_DIR/scripts/run_scraper.sh

# Run trends analysis every 4 hours
0 */4 * * * cd $PROJECT_DIR && $PROJECT_DIR/venv/bin/python -c "from trends_analyzer import run_trends_analysis; run_trends_analysis()"

# Send daily report at 9 AM
0 9 * * * cd $PROJECT_DIR && $PROJECT_DIR/venv/bin/python -c "from notifications import send_daily_report; send_daily_report()"

# Clean up old log files weekly (Sunday at 2 AM)
0 2 * * 0 find $PROJECT_DIR/logs -name "*.log" -mtime +7 -delete

# Health check every 30 minutes (optional)
*/30 * * * * cd $PROJECT_DIR && $PROJECT_DIR/venv/bin/python -c "
import sys
sys.path.insert(0, '.')
try:
    from app import app
    with app.app_context():
        from models import db
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
except Exception as e:
    print(f'Health check failed: {e}')
    sys.exit(1)
" > /dev/null 2>&1

# Restart services daily at 3 AM (if using systemd)
# 0 3 * * * /usr/bin/systemctl restart game-monitor-scheduler

# Alternative manual scheduler start (if not using systemd)
# @reboot $PROJECT_DIR/scripts/run_scheduler.sh

# Example: Run scraper for specific sites only
# 0 */2 * * * cd $PROJECT_DIR && $PROJECT_DIR/venv/bin/python -c "
# from production_scraper import ProductionGameScraper
# import sys
# sys.path.insert(0, '.')
# scraper = ProductionGameScraper()
# # Add specific sites you want to scrape more frequently
# sites = ['https://itch.io/games/new-and-popular', 'https://poki.com/']
# for site in sites:
#     scraper.scrape_url(site)
# scraper.close()
# "