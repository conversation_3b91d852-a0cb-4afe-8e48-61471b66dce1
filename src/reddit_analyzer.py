"""
Reddit游戏热度分析模块
用于分析Reddit上游戏相关的讨论热度和趋势
"""

import os
import praw
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import re
from functools import lru_cache

logger = logging.getLogger(__name__)


@dataclass
class RedditMetrics:
    """Reddit数据指标"""
    game_name: str
    heat_score: int  # 0-100
    growth_rate: float  # 百分比
    post_count_24h: int
    total_upvotes_24h: int
    total_comments_24h: int
    top_post_title: str
    top_post_score: int
    top_post_url: str
    avg_comments: float
    unique_authors: int
    sentiment: str  # positive/neutral/negative
    subreddit_distribution: Dict[str, int]
    last_updated: datetime
    status: str = 'success'  # success/failed/no_data
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        data['last_updated'] = self.last_updated.isoformat()
        return data


class RedditAnalyzer:
    """Reddit游戏热度分析器"""
    
    # 目标子版块
    GAMING_SUBREDDITS = [
        'WebGames', 
        'incremental_games', 
        'IndieGaming',
        'html5games', 
        'browsergames', 
        'gaming',
        'iosgaming',
        'AndroidGaming'
    ]
    
    # 热度计算权重
    HEAT_WEIGHTS = {
        'post_count': 0.25,      # 帖子数量权重
        'upvotes': 0.35,         # 点赞数权重
        'comments': 0.25,        # 评论数权重
        'unique_authors': 0.15   # 独立作者数权重
    }
    
    def __init__(self, client_id: str = None, client_secret: str = None, user_agent: str = None):
        """
        初始化Reddit分析器
        
        Args:
            client_id: Reddit API客户端ID
            client_secret: Reddit API客户端密钥
            user_agent: User Agent字符串
        """
        self.client_id = client_id or os.getenv('REDDIT_CLIENT_ID')
        self.client_secret = client_secret or os.getenv('REDDIT_CLIENT_SECRET')
        self.user_agent = user_agent or os.getenv('REDDIT_USER_AGENT', 'GameMonitor/1.0')
        
        if not self.client_id or not self.client_secret:
            raise ValueError("Reddit API credentials not provided")
        
        # 初始化Reddit客户端
        self.reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=self.user_agent
        )
        
        # 缓存历史数据用于计算增长率
        self._history_cache = defaultdict(list)
    
    async def analyze_game(self, game_name: str, search_variations: List[str] = None) -> RedditMetrics:
        """
        分析单个游戏的Reddit热度
        
        Args:
            game_name: 游戏名称
            search_variations: 搜索变体列表（如不同拼写、简称等）
            
        Returns:
            RedditMetrics: Reddit指标数据
        """
        try:
            # 准备搜索关键词
            search_terms = [game_name]
            if search_variations:
                search_terms.extend(search_variations)
            
            # 搜索相关帖子
            posts = await self._search_game_posts(search_terms)
            
            if not posts:
                return self._create_empty_metrics(game_name, "No discussions found")
            
            # 计算各项指标
            metrics = self._calculate_metrics(posts)
            
            # 计算热度分数
            heat_score = self._calculate_heat_score(metrics)
            
            # 计算增长率
            growth_rate = await self._calculate_growth_rate(game_name, metrics)
            
            # 获取最热帖子信息
            top_post = max(posts, key=lambda x: x['score']) if posts else None
            
            # 分析子版块分布
            subreddit_dist = self._analyze_subreddit_distribution(posts)
            
            # 分析情感倾向
            sentiment = self._analyze_sentiment(posts)
            
            return RedditMetrics(
                game_name=game_name,
                heat_score=heat_score,
                growth_rate=growth_rate,
                post_count_24h=len(posts),
                total_upvotes_24h=metrics['total_upvotes'],
                total_comments_24h=metrics['total_comments'],
                top_post_title=top_post['title'] if top_post else "",
                top_post_score=top_post['score'] if top_post else 0,
                top_post_url=top_post['url'] if top_post else "",
                avg_comments=metrics['avg_comments'],
                unique_authors=metrics['unique_authors'],
                sentiment=sentiment,
                subreddit_distribution=subreddit_dist,
                last_updated=datetime.now(),
                status='success'
            )
            
        except Exception as e:
            logger.error(f"Error analyzing game {game_name}: {str(e)}")
            return self._create_empty_metrics(game_name, str(e))
    
    async def _search_game_posts(self, search_terms: List[str]) -> List[Dict]:
        """
        搜索游戏相关帖子
        
        Args:
            search_terms: 搜索关键词列表
            
        Returns:
            List[Dict]: 帖子列表
        """
        all_posts = []
        seen_ids = set()
        
        # 设置时间范围（24小时内）
        time_filter = datetime.now() - timedelta(hours=24)
        
        for term in search_terms:
            for subreddit_name in self.GAMING_SUBREDDITS:
                try:
                    subreddit = self.reddit.subreddit(subreddit_name)
                    
                    # 搜索帖子
                    for submission in subreddit.search(term, time_filter='day', limit=25):
                        # 避免重复
                        if submission.id in seen_ids:
                            continue
                        
                        seen_ids.add(submission.id)
                        
                        # 检查是否在时间范围内
                        created_time = datetime.fromtimestamp(submission.created_utc)
                        if created_time < time_filter:
                            continue
                        
                        # 收集帖子数据
                        post_data = {
                            'id': submission.id,
                            'title': submission.title,
                            'score': submission.score,
                            'num_comments': submission.num_comments,
                            'author': str(submission.author) if submission.author else '[deleted]',
                            'subreddit': subreddit_name,
                            'url': f"https://reddit.com{submission.permalink}",
                            'created_utc': submission.created_utc,
                            'selftext': submission.selftext[:500] if submission.selftext else ""
                        }
                        
                        all_posts.append(post_data)
                        
                except Exception as e:
                    logger.warning(f"Error searching {subreddit_name} for '{term}': {e}")
                    continue
        
        return all_posts
    
    def _calculate_metrics(self, posts: List[Dict]) -> Dict:
        """计算帖子指标"""
        if not posts:
            return {
                'total_upvotes': 0,
                'total_comments': 0,
                'avg_comments': 0,
                'unique_authors': 0
            }
        
        total_upvotes = sum(p['score'] for p in posts)
        total_comments = sum(p['num_comments'] for p in posts)
        unique_authors = len(set(p['author'] for p in posts if p['author'] != '[deleted]'))
        
        return {
            'total_upvotes': total_upvotes,
            'total_comments': total_comments,
            'avg_comments': total_comments / len(posts) if posts else 0,
            'unique_authors': unique_authors
        }
    
    def _calculate_heat_score(self, metrics: Dict) -> int:
        """
        计算热度分数（0-100）
        使用加权算法综合多个指标
        """
        # 归一化各项指标
        normalized_scores = {
            'post_count': min(metrics.get('post_count', 0) / 50, 1.0),  # 50个帖子为满分
            'upvotes': min(metrics['total_upvotes'] / 5000, 1.0),       # 5000赞为满分
            'comments': min(metrics['total_comments'] / 1000, 1.0),     # 1000评论为满分
            'unique_authors': min(metrics['unique_authors'] / 30, 1.0)  # 30个独立作者为满分
        }
        
        # 计算加权分数
        weighted_score = sum(
            normalized_scores.get(key, 0) * weight 
            for key, weight in self.HEAT_WEIGHTS.items()
        )
        
        # 转换为0-100分数
        return int(weighted_score * 100)
    
    async def _calculate_growth_rate(self, game_name: str, current_metrics: Dict) -> float:
        """
        计算增长率
        比较当前指标与历史平均值
        """
        # 获取历史数据
        history = self._history_cache.get(game_name, [])
        
        # 添加当前数据到历史
        history.append({
            'timestamp': datetime.now(),
            'metrics': current_metrics
        })
        
        # 保留最近7天的数据
        cutoff_time = datetime.now() - timedelta(days=7)
        history = [h for h in history if h['timestamp'] > cutoff_time]
        self._history_cache[game_name] = history
        
        # 如果历史数据不足，返回0
        if len(history) < 2:
            return 0.0
        
        # 计算历史平均值（排除当前）
        historical_upvotes = [h['metrics']['total_upvotes'] for h in history[:-1]]
        historical_avg = sum(historical_upvotes) / len(historical_upvotes) if historical_upvotes else 1
        
        # 计算增长率
        current_value = current_metrics['total_upvotes']
        growth_rate = ((current_value - historical_avg) / historical_avg) * 100 if historical_avg > 0 else 0
        
        return round(growth_rate, 2)
    
    def _analyze_subreddit_distribution(self, posts: List[Dict]) -> Dict[str, int]:
        """分析帖子在各子版块的分布"""
        distribution = defaultdict(int)
        for post in posts:
            distribution[post['subreddit']] += 1
        return dict(distribution)
    
    def _analyze_sentiment(self, posts: List[Dict]) -> str:
        """
        简单的情感分析
        基于标题和内容中的关键词
        """
        positive_keywords = [
            'awesome', 'amazing', 'love', 'great', 'best', 'fun', 
            'excellent', 'fantastic', 'wonderful', 'addictive', 'brilliant'
        ]
        negative_keywords = [
            'bad', 'terrible', 'hate', 'worst', 'boring', 'sucks', 
            'awful', 'disappointing', 'broken', 'buggy', 'trash'
        ]
        
        positive_count = 0
        negative_count = 0
        
        for post in posts:
            text = (post['title'] + ' ' + post.get('selftext', '')).lower()
            
            # 计算正面词汇
            for word in positive_keywords:
                if word in text:
                    positive_count += post['score']  # 使用分数加权
            
            # 计算负面词汇
            for word in negative_keywords:
                if word in text:
                    negative_count += post['score']
        
        # 判断整体情感
        if positive_count > negative_count * 1.5:
            return 'positive'
        elif negative_count > positive_count * 1.5:
            return 'negative'
        else:
            return 'neutral'
    
    def _create_empty_metrics(self, game_name: str, error_msg: str = None) -> RedditMetrics:
        """创建空的指标数据"""
        return RedditMetrics(
            game_name=game_name,
            heat_score=0,
            growth_rate=0.0,
            post_count_24h=0,
            total_upvotes_24h=0,
            total_comments_24h=0,
            top_post_title="",
            top_post_score=0,
            top_post_url="",
            avg_comments=0.0,
            unique_authors=0,
            sentiment='neutral',
            subreddit_distribution={},
            last_updated=datetime.now(),
            status='no_data' if not error_msg else 'failed',
            error_message=error_msg
        )
    
    @lru_cache(maxsize=100)
    def validate_subreddit(self, subreddit_name: str) -> bool:
        """验证子版块是否存在且可访问"""
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            # 尝试访问子版块的一个属性来验证
            _ = subreddit.id
            return True
        except Exception:
            return False