import requests
import json
import logging
import hashlib
import hmac
import time
from typing import List, Dict, Optional
from datetime import datetime, timedelta
from config import Config
from models import db, Alert, Game, Trend

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FeishuBot:
    """Feishu (Lark) Bot integration for notifications"""
    
    def __init__(self):
        self.webhook_url = Config.FEISHU_WEBHOOK_URL
        self.secret = Config.FEISHU_SECRET
        self.enabled = Config.ENABLE_FEISHU_ALERTS
    
    def _generate_sign(self, timestamp: int) -> str:
        """Generate signature for Feishu webhook"""
        if not self.secret:
            return ""
        
        string_to_sign = f"{timestamp}\n{self.secret}"
        hmac_code = hmac.new(
            string_to_sign.encode("utf-8"),
            digestmod=hashlib.sha256
        ).digest()
        sign = hmac_code.hex()
        return sign
    
    def send_message(self, title: str, content: str, message_type: str = "surge") -> bool:
        """Send message to Feishu group"""
        if not self.enabled or not self.webhook_url:
            logger.warning("Feishu notifications disabled or not configured")
            return False
        
        try:
            timestamp = int(time.time())
            sign = self._generate_sign(timestamp)
            
            # Prepare message payload
            payload = {
                "timestamp": str(timestamp),
                "sign": sign,
                "msg_type": "interactive",
                "card": self._create_card(title, content, message_type)
            }
            
            # Send request
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('StatusCode') == 0:
                    logger.info(f"Successfully sent Feishu message: {title}")
                    return True
                else:
                    logger.error(f"Feishu API error: {result}")
                    return False
            else:
                logger.error(f"HTTP error sending to Feishu: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending Feishu message: {e}")
            return False
    
    def _create_card(self, title: str, content: str, message_type: str) -> Dict:
        """Create interactive card for Feishu message"""
        # Color scheme based on message type
        colors = {
            'surge': 'red',
            'daily_report': 'blue',
            'warning': 'orange',
            'info': 'green'
        }
        
        color = colors.get(message_type, 'blue')
        
        card = {
            "config": {
                "wide_screen_mode": True
            },
            "header": {
                "title": {
                    "tag": "plain_text",
                    "content": title
                },
                "template": color
            },
            "elements": [
                {
                    "tag": "div",
                    "text": {
                        "tag": "lark_md",
                        "content": content
                    }
                },
                {
                    "tag": "hr"
                },
                {
                    "tag": "div",
                    "text": {
                        "tag": "plain_text",
                        "content": f"🤖 Generated by Game Monitor System | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    }
                }
            ]
        }
        
        return card
    
    def send_surge_alert(self, surge_data: List[Dict]) -> bool:
        """Send surge alert message"""
        if not surge_data:
            return False
        
        title = f"🚀 {len(surge_data)} Game Keywords Surging!"
        
        content_parts = ["**Trending Game Keywords Detected:**\n"]
        
        for item in surge_data[:10]:  # Limit to top 10
            game_name = item.get('game', {}).get('name', 'Unknown Game')
            keyword = item.get('keyword', 'Unknown')
            growth_rate = item.get('growth_rate', 0)
            current_value = item.get('current_value', 0)
            game_url = item.get('game', {}).get('game_url', '#')
            
            content_parts.append(
                f"🎮 **{game_name}**\n"
                f"📈 Growth Rate: **{growth_rate:.1f}%**\n"
                f"🔥 Current Trend Value: **{current_value}**\n"
                f"🔗 [Play Game]({game_url})\n"
            )
        
        if len(surge_data) > 10:
            content_parts.append(f"\n... and {len(surge_data) - 10} more trending games!")
        
        content_parts.extend([
            "\n**💡 Action Items:**",
            "• Monitor these games for potential opportunities",
            "• Consider featuring trending games",
            "• Analyze competitor strategies",
            f"• Threshold: {Config.SURGE_THRESHOLD}% growth rate"
        ])
        
        content = "\n".join(content_parts)
        
        return self.send_message(title, content, "surge")
    
    def send_daily_report(self, stats: Dict) -> bool:
        """Send daily summary report"""
        title = "📊 Daily Game Monitor Report"
        
        content_parts = [
            f"**📅 Report Date: {datetime.now().strftime('%Y-%m-%d')}**\n",
            "**📈 Today's Statistics:**",
            f"• New Games Discovered: **{stats.get('new_games', 0)}**",
            f"• Total Games in Database: **{stats.get('total_games', 0)}**",
            f"• Trending Keywords: **{stats.get('trending_keywords', 0)}**",
            f"• Active Monitoring Sites: **{stats.get('active_sites', 0)}**",
            f"• Successful Scrapes: **{stats.get('successful_scrapes', 0)}**"
        ]
        
        # Add top trending games if available
        if stats.get('top_trending'):
            content_parts.extend([
                "\n**🔥 Top Trending Games:**"
            ])
            
            for game in stats['top_trending'][:5]:
                content_parts.append(
                    f"• **{game['name']}** ({game['growth_rate']:.1f}% growth)"
                )
        
        # Add system status
        content_parts.extend([
            "\n**⚙️ System Status:**",
            f"• Scraping Success Rate: **{stats.get('scrape_success_rate', 0):.1f}%**",
            f"• API Response Time: **{stats.get('avg_response_time', 0):.0f}ms**",
            f"• Database Health: **{stats.get('db_status', 'Unknown')}**"
        ])
        
        content = "\n".join(content_parts)
        
        return self.send_message(title, content, "daily_report")
    
    def send_error_alert(self, error_message: str, component: str = "System") -> bool:
        """Send error alert message"""
        title = f"⚠️ {component} Error Alert"
        
        content = f"""
**Error Detected:**
```
{error_message}
```

**Component:** {component}
**Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

**Recommended Actions:**
• Check system logs for detailed information
• Verify service connectivity
• Monitor system resources
• Contact administrator if issue persists
        """
        
        return self.send_message(title, content, "warning")
    
    def send_text_message(self, text: str) -> bool:
        """Send plain text message to Feishu"""
        if not self.enabled or not self.webhook_url:
            logger.warning("Feishu notifications disabled or not configured")
            return False
        
        try:
            timestamp = int(time.time())
            sign = self._generate_sign(timestamp)
            
            payload = {
                "timestamp": str(timestamp),
                "sign": sign,
                "msg_type": "text",
                "content": {
                    "text": text
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    logger.info("Text message sent successfully")
                    return True
                else:
                    logger.error(f"Feishu API error: {result}")
            else:
                logger.error(f"HTTP error {response.status_code}: {response.text}")
            
        except Exception as e:
            logger.error(f"Failed to send text message: {e}")
        
        return False
    
    def send_monitor_report(self, report_content: str) -> bool:
        """Send formatted monitor report"""
        if not self.enabled or not self.webhook_url:
            logger.warning("Feishu notifications disabled or not configured")
            return False
        
        try:
            timestamp = int(time.time())
            sign = self._generate_sign(timestamp)
            
            # 构建卡片消息
            card = {
                "config": {
                    "wide_screen_mode": True,
                    "enable_forward": True
                },
                "header": {
                    "title": {
                        "tag": "plain_text",
                        "content": "📊 游戏监控系统日报"
                    },
                    "template": "blue"
                },
                "elements": [
                    {
                        "tag": "markdown",
                        "content": report_content
                    },
                    {
                        "tag": "hr"
                    },
                    {
                        "tag": "note",
                        "elements": [
                            {
                                "tag": "plain_text",
                                "content": f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            }
                        ]
                    }
                ]
            }
            
            payload = {
                "timestamp": str(timestamp),
                "sign": sign,
                "msg_type": "interactive",
                "card": card
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    logger.info("Monitor report sent successfully")
                    return True
                else:
                    logger.error(f"Feishu API error: {result}")
            else:
                logger.error(f"HTTP error {response.status_code}: {response.text}")
                
        except Exception as e:
            logger.error(f"Failed to send monitor report: {e}")
        
        return False

class NotificationManager:
    """Manage all types of notifications"""
    
    def __init__(self):
        self.feishu = FeishuBot()
    
    def process_pending_alerts(self) -> int:
        """Process and send pending alerts"""
        sent_count = 0
        
        from app import app
        with app.app_context():
            # Get unsent alerts
            pending_alerts = Alert.query.filter_by(is_sent=False)\
                .order_by(Alert.created_at.desc()).all()
            
            if not pending_alerts:
                logger.info("No pending alerts to process")
                return 0
            
            # Group alerts by type
            surge_alerts = [a for a in pending_alerts if a.alert_type == 'surge']
            
            # Process surge alerts
            if surge_alerts:
                surge_data = []
                for alert in surge_alerts:
                    surge_data.append({
                        'keyword': alert.keyword,
                        'growth_rate': float(alert.current_value),
                        'current_value': int(alert.threshold_value),
                        'game': alert.game.to_dict() if alert.game else {}
                    })
                
                if self.feishu.send_surge_alert(surge_data):
                    # Mark alerts as sent
                    for alert in surge_alerts:
                        alert.is_sent = True
                        alert.sent_at = datetime.utcnow()
                        sent_count += 1
                    
                    db.session.commit()
                    logger.info(f"Sent surge alert for {len(surge_alerts)} keywords")
        
        return sent_count
    
    def send_daily_report(self) -> bool:
        """Generate and send daily report"""
        from app import app
        with app.app_context():
            try:
                # Collect statistics
                today = datetime.utcnow().date()
                yesterday = today - timedelta(days=1)
                
                stats = {
                    'new_games': Game.query.filter(
                        Game.created_at >= yesterday,
                        Game.created_at < today,
                        Game.is_active == True
                    ).count(),
                    
                    'total_games': Game.query.filter_by(is_active=True).count(),
                    
                    'trending_keywords': Trend.query.filter(
                        Trend.growth_rate >= Config.SURGE_THRESHOLD,
                        Trend.created_at >= yesterday
                    ).count(),
                    
                    'active_sites': db.session.query(Game.source_site)\
                        .filter_by(is_active=True).distinct().count()
                }
                
                # Get top trending games
                top_trending = Trend.query\
                    .filter(Trend.created_at >= yesterday)\
                    .filter(Trend.growth_rate > 0)\
                    .order_by(Trend.growth_rate.desc())\
                    .limit(5).all()
                
                stats['top_trending'] = [
                    {
                        'name': trend.keyword,
                        'growth_rate': float(trend.growth_rate)
                    }
                    for trend in top_trending
                ]
                
                # Add system health metrics
                stats.update({
                    'scrape_success_rate': 85.0,  # This would be calculated from actual data
                    'avg_response_time': 250,
                    'db_status': 'Healthy'
                })
                
                return self.feishu.send_daily_report(stats)
                
            except Exception as e:
                logger.error(f"Error generating daily report: {e}")
                return self.feishu.send_error_alert(str(e), "Daily Report Generator")
    
    def send_system_error(self, error: str, component: str = "System") -> bool:
        """Send system error notification"""
        return self.feishu.send_error_alert(error, component)

# Convenience functions
def send_surge_notifications():
    """Send pending surge notifications"""
    manager = NotificationManager()
    return manager.process_pending_alerts()

def send_daily_report():
    """Send daily report"""
    manager = NotificationManager()
    return manager.send_daily_report()

def send_error_notification(error: str, component: str = "System"):
    """Send error notification"""
    manager = NotificationManager()
    return manager.send_system_error(error, component)

if __name__ == "__main__":
    # Test notification
    manager = NotificationManager()
    test_stats = {
        'new_games': 156,
        'total_games': 2847,
        'trending_keywords': 12,
        'active_sites': 27,
        'top_trending': [
            {'name': 'Among Us Battle', 'growth_rate': 156.3},
            {'name': 'Pixel Adventure', 'growth_rate': 89.7}
        ],
        'scrape_success_rate': 94.2,
        'avg_response_time': 234,
        'db_status': 'Healthy'
    }
    
    result = manager.feishu.send_daily_report(test_stats)
    print(f"Test notification sent: {result}")