#!/usr/bin/env python3
"""
独立的Reddit分析定时任务模块
负责定期分析游戏的Reddit热度
"""

import schedule
import time
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Game, RedditMetric
from src.reddit_analyzer_sync import RedditAnalyzerSync
from src.notifications import send_error_notification, FeishuBot

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler_reddit.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class RedditScheduler:
    """Reddit分析定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self.last_run = None
        self.app = create_app()
        
        # 初始化Reddit分析器
        try:
            self.reddit_analyzer = RedditAnalyzerSync()
            logger.info("Reddit分析器初始化成功")
        except Exception as e:
            logger.error(f"Reddit分析器初始化失败: {e}")
            self.reddit_analyzer = None
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
    
    def run_reddit_analysis(self):
        """执行Reddit分析任务"""
        if not self.reddit_analyzer:
            logger.warning("Reddit分析器未初始化，跳过分析")
            return
            
        logger.info("开始执行Reddit分析任务...")
        start_time = datetime.now()
        
        with self.app.app_context():
            try:
                # 获取活跃游戏
                games = Game.query.filter_by(is_active=True).limit(100).all()
                logger.info(f"准备分析 {len(games)} 个游戏的Reddit数据")
                
                success_count = 0
                error_count = 0
                high_heat_games = []
                
                for i, game in enumerate(games):
                    try:
                        # 分析游戏
                        metrics = self.reddit_analyzer.analyze_game(game.name)
                        
                        # 保存到数据库
                        reddit_metric = RedditMetric(
                            game_id=game.id,
                            heat_score=metrics.heat_score,
                            growth_rate=metrics.growth_rate,
                            post_count_24h=metrics.post_count_24h,
                            total_upvotes_24h=metrics.total_upvotes_24h,
                            total_comments_24h=metrics.total_comments_24h,
                            top_post_title=metrics.top_post_title,
                            top_post_score=metrics.top_post_score,
                            top_post_url=metrics.top_post_url,
                            avg_comments=metrics.avg_comments,
                            unique_authors=metrics.unique_authors,
                            sentiment=metrics.sentiment,
                            subreddit_distribution=metrics.subreddit_distribution,
                            status=metrics.status,
                            error_message=metrics.error_message
                        )
                        
                        db.session.add(reddit_metric)
                        success_count += 1
                        
                        # 记录高热度游戏
                        if metrics.heat_score > 70:
                            high_heat_games.append((game, metrics))
                            logger.info(f"发现高热度游戏: {game.name} (热度={metrics.heat_score})")
                        
                        # 每20个游戏提交一次
                        if (i + 1) % 20 == 0:
                            db.session.commit()
                            logger.info(f"进度: {i+1}/{len(games)}")
                            time.sleep(5)  # 短暂休息
                        
                    except Exception as e:
                        logger.error(f"分析游戏 {game.name} 失败: {e}")
                        error_count += 1
                        continue
                
                # 提交剩余更改
                db.session.commit()
                self.last_run = datetime.now()
                
                logger.info(f"Reddit分析完成: 成功={success_count}, 失败={error_count}")
                
                # 记录结构化统计信息
                duration = (datetime.now() - start_time).total_seconds()
                logger.info(f"[STATS] success={success_count} failed={error_count} "
                          f"high_heat={len(high_heat_games)} total_analyzed={len(games)} "
                          f"duration={duration:.2f}")
                
                # 发送高热度游戏通知
                if high_heat_games:
                    self._notify_high_heat_games(high_heat_games)
                    
            except Exception as e:
                logger.error(f"Reddit分析任务异常: {e}", exc_info=True)
                db.session.rollback()
                try:
                    send_error_notification(f"Reddit分析失败: {str(e)}", "Reddit Scheduler")
                except:
                    pass
    
    def _notify_high_heat_games(self, high_heat_games):
        """发送高热度游戏通知"""
        try:
            message = "🔥 Reddit热度爆发游戏\n\n"
            
            for game, metrics in high_heat_games[:5]:  # 最多显示5个
                message += f"**{game.name}**\n"
                message += f"- 热度: {metrics.heat_score}/100\n"
                message += f"- 24h讨论: {metrics.post_count_24h}个帖子\n"
                message += f"- 总互动: {metrics.total_upvotes_24h}赞, {metrics.total_comments_24h}评论\n"
                if metrics.top_post_title:
                    message += f"- 热门帖: {metrics.top_post_title[:50]}...\n"
                message += "\n"
            
            bot = FeishuBot()
            bot.send_text_message(message)
            logger.info("高热度游戏通知已发送")
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
    
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置Reddit分析定时任务...")
        
        # 每2小时执行一次
        schedule.every(2).hours.do(self.run_reddit_analysis)
        
        # 也可以在特定时间执行
        # schedule.every().day.at("10:00").do(self.run_reddit_analysis)
        # schedule.every().day.at("16:00").do(self.run_reddit_analysis)
        # schedule.every().day.at("22:00").do(self.run_reddit_analysis)
        
        logger.info("Reddit分析定时任务设置完成")
        logger.info("执行频率: 每2小时")
    
    def start(self):
        """启动调度器"""
        logger.info("启动Reddit分析调度器...")
        
        try:
            self.setup_schedule()
            
            # 启动时立即执行一次
            logger.info("启动时执行一次Reddit分析...")
            self.run_reddit_analysis()
            
            self.running = True
            logger.info("Reddit分析调度器启动成功")
            
            # 主循环
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("Reddit分析调度器被用户停止")
            self.stop()
        except Exception as e:
            logger.error(f"Reddit分析调度器错误: {e}")
            self.stop()
    
    def stop(self):
        """停止调度器"""
        logger.info("停止Reddit分析调度器...")
        self.running = False
        schedule.clear()
        logger.info("Reddit分析调度器已停止")


def main():
    """主函数"""
    scheduler = RedditScheduler()
    scheduler.start()


if __name__ == "__main__":
    main()