#!/usr/bin/env python3
"""
独立的爬虫定时任务模块
负责定期爬取游戏网站数据
"""

import schedule
import time
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from src.production_scraper import run_production_scrape
from src.notifications import send_error_notification

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ScraperScheduler:
    """爬虫定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self.last_run = None
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
        os.makedirs('data', exist_ok=True)
    
    def run_scraping_job(self):
        """执行爬虫任务"""
        logger.info("开始执行网站爬虫任务...")
        
        try:
            # 运行爬虫
            result = run_production_scrape()
            self.last_run = datetime.now()
            
            if result.get('success', False):
                stats_msg = (f"爬虫任务成功完成: 新游戏={result.get('new_games', 0)}, "
                           f"更新={result.get('updated_games', 0)}, "
                           f"错误={result.get('error_count', 0)}")
                logger.info(stats_msg)
                
                # 记录结构化统计信息
                logger.info(f"[STATS] new_games={result.get('new_games', 0)} "
                          f"updated={result.get('updated_games', 0)} "
                          f"errors={result.get('error_count', 0)} "
                          f"total_sites={result.get('total_sites', 0)} "
                          f"duration={result.get('duration_seconds', 0)}")
            else:
                logger.error(f"爬虫任务失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"爬虫任务异常: {e}", exc_info=True)
            try:
                send_error_notification(f"爬虫任务失败: {str(e)}", "Scraper Scheduler")
            except:
                pass
    
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置爬虫定时任务...")
        
        # 每小时执行一次爬虫
        schedule.every().hour.do(self.run_scraping_job)
        
        # 也可以在特定时间执行
        # schedule.every().day.at("02:00").do(self.run_scraping_job)
        
        logger.info("爬虫定时任务设置完成")
        logger.info("执行频率: 每小时")
    
    def start(self):
        """启动调度器"""
        logger.info("启动爬虫调度器...")
        
        try:
            self.setup_schedule()
            
            # 启动时立即执行一次
            logger.info("启动时执行一次爬虫任务...")
            self.run_scraping_job()
            
            self.running = True
            logger.info("爬虫调度器启动成功")
            
            # 主循环
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("爬虫调度器被用户停止")
            self.stop()
        except Exception as e:
            logger.error(f"爬虫调度器错误: {e}")
            self.stop()
    
    def stop(self):
        """停止调度器"""
        logger.info("停止爬虫调度器...")
        self.running = False
        schedule.clear()
        logger.info("爬虫调度器已停止")


def main():
    """主函数"""
    scheduler = ScraperScheduler()
    scheduler.start()


if __name__ == "__main__":
    main()