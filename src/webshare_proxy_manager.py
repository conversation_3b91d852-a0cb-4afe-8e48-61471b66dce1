#!/usr/bin/env python3
"""
Webshare API代理管理器
支持动态获取和管理代理IP池
"""

import requests
import time
import random
import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import json
from config import Config
from src.notifications import send_error_notification

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebshareProxyManager:
    """Webshare API代理管理器"""
    
    def __init__(self):
        self.api_token = Config.WEBSHARE_API_TOKEN
        self.base_url = "https://proxy.webshare.io/api/v2"
        self.proxy_pool = []
        self.current_proxy_index = 0
        self.last_refresh = None
        self.refresh_interval = 3600  # 1小时刷新一次
        self.failed_proxies = set()
        self.max_failures = 3
        
        # 请求计数和限流
        self.request_count = 0
        self.request_reset_time = time.time() + 60
        self.max_requests_per_minute = 150  # 留些余量
        
        if not self.api_token:
            logger.error("Webshare API token not configured")
            send_error_notification("Webshare API token not configured", "Webshare Proxy Manager")
    
    def _wait_for_rate_limit(self):
        """处理API速率限制"""
        current_time = time.time()
        
        # 重置计数器
        if current_time >= self.request_reset_time:
            self.request_count = 0
            self.request_reset_time = current_time + 60
        
        # 检查是否超出限制
        if self.request_count >= self.max_requests_per_minute:
            wait_time = self.request_reset_time - current_time
            if wait_time > 0:
                logger.warning(f"Rate limit reached, waiting {wait_time:.1f} seconds")
                time.sleep(wait_time)
                self.request_count = 0
                self.request_reset_time = time.time() + 60
        
        self.request_count += 1
    
    def _make_api_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """发送API请求"""
        if not self.api_token:
            return None
        
        try:
            self._wait_for_rate_limit()
            
            url = f"{self.base_url}/{endpoint.lstrip('/')}"
            headers = {
                'Authorization': f'Token {self.api_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 429:
                logger.warning("API rate limit exceeded")
                time.sleep(60)  # 等待1分钟
                return None
            elif response.status_code == 401:
                error_msg = "Webshare API authentication failed - invalid token"
                logger.error(error_msg)
                send_error_notification(error_msg, "Webshare API")
                return None
            else:
                error_msg = f"Webshare API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                send_error_notification(error_msg, "Webshare API")
                return None
                
        except requests.exceptions.Timeout:
            error_msg = "Webshare API request timeout"
            logger.error(error_msg)
            send_error_notification(error_msg, "Webshare API")
            return None
        except Exception as e:
            error_msg = f"Webshare API request failed: {e}"
            logger.error(error_msg)
            send_error_notification(error_msg, "Webshare API")
            return None
    
    def fetch_proxy_list(self, page_size: int = 100) -> List[Dict]:
        """从Webshare API获取代理列表"""
        all_proxies = []
        page = 1
        
        logger.info("Fetching proxy list from Webshare API...")
        
        while True:
            try:
                params = {
                    'mode': 'direct',
                    'page': page,
                    'page_size': page_size
                }
                
                data = self._make_api_request('proxy/list/', params)
                if not data:
                    break
                
                proxies = data.get('results', [])
                if not proxies:
                    break
                
                all_proxies.extend(proxies)
                logger.info(f"Fetched {len(proxies)} proxies from page {page}")
                
                # 检查是否有下一页
                if not data.get('next'):
                    break
                
                page += 1
                
                # 添加延迟避免过快请求
                time.sleep(0.5)
                
            except Exception as e:
                error_msg = f"Error fetching proxy page {page}: {e}"
                logger.error(error_msg)
                send_error_notification(error_msg, "Webshare Proxy Fetch")
                break
        
        logger.info(f"Total proxies fetched: {len(all_proxies)}")
        return all_proxies
    
    def format_proxy_for_requests(self, proxy_data: Dict) -> Dict:
        """将Webshare代理数据格式化为requests可用格式"""
        try:
            username = proxy_data.get('username')
            password = proxy_data.get('password')
            endpoint = proxy_data.get('proxy_address')
            port = proxy_data.get('port')
            
            if not all([username, password, endpoint, port]):
                return None
            
            proxy_url = f"http://{username}:{password}@{endpoint}:{port}"
            
            return {
                'http': proxy_url,
                'https': proxy_url,
                'proxy_info': {
                    'endpoint': endpoint,
                    'port': port,
                    'username': username,
                    'country': proxy_data.get('country_code', 'Unknown'),
                    'city': proxy_data.get('city', 'Unknown')
                }
            }
            
        except Exception as e:
            logger.error(f"Error formatting proxy: {e}")
            return None
    
    def test_proxy(self, proxy_dict: Dict) -> Tuple[bool, float]:
        """测试代理可用性和响应时间"""
        try:
            start_time = time.time()
            
            test_urls = [
                'https://httpbin.org/ip',
                'https://www.google.com/search?q=test'
            ]
            
            for url in test_urls:
                response = requests.get(
                    url,
                    proxies=proxy_dict,
                    timeout=15,
                    headers={'User-Agent': Config.USER_AGENT}
                )
                
                if response.status_code != 200:
                    return False, 0
            
            response_time = time.time() - start_time
            return True, response_time
            
        except Exception as e:
            logger.debug(f"Proxy test failed: {e}")
            return False, 0
    
    def refresh_proxy_pool(self) -> bool:
        """刷新代理池"""
        try:
            logger.info("Refreshing proxy pool...")
            
            # 获取新的代理列表
            raw_proxies = self.fetch_proxy_list()
            if not raw_proxies:
                error_msg = "Failed to fetch proxy list from Webshare API"
                logger.error(error_msg)
                send_error_notification(error_msg, "Proxy Pool Refresh")
                return False
            
            # 格式化代理
            formatted_proxies = []
            for proxy_data in raw_proxies:
                formatted = self.format_proxy_for_requests(proxy_data)
                if formatted:
                    formatted_proxies.append(formatted)
            
            if not formatted_proxies:
                error_msg = "No valid proxies after formatting"
                logger.error(error_msg)
                send_error_notification(error_msg, "Proxy Pool Refresh")
                return False
            
            # 测试代理可用性（抽样测试）
            working_proxies = []
            test_count = min(20, len(formatted_proxies))  # 最多测试20个
            sample_proxies = random.sample(formatted_proxies, test_count)
            
            logger.info(f"Testing {test_count} sample proxies...")
            
            for proxy in sample_proxies:
                is_working, response_time = self.test_proxy(proxy)
                if is_working:
                    proxy['response_time'] = response_time
                    working_proxies.append(proxy)
                    logger.debug(f"Working proxy: {proxy['proxy_info']['endpoint']} ({response_time:.2f}s)")
            
            if working_proxies:
                # 添加未测试的代理到池中（假设它们可用）
                untested_proxies = [p for p in formatted_proxies if p not in sample_proxies]
                working_proxies.extend(untested_proxies)
                
                # 随机打乱代理顺序
                random.shuffle(working_proxies)
                
                self.proxy_pool = working_proxies
                self.current_proxy_index = 0
                self.last_refresh = datetime.now()
                self.failed_proxies.clear()
                
                logger.info(f"Proxy pool refreshed: {len(working_proxies)} proxies available")
                logger.info(f"Tested {len(working_proxies)} proxies, {len(sample_proxies)} confirmed working")
                
                return True
            else:
                error_msg = f"No working proxies found after testing {test_count} samples"
                logger.error(error_msg)
                send_error_notification(error_msg, "Proxy Pool Refresh")
                return False
                
        except Exception as e:
            error_msg = f"Error refreshing proxy pool: {e}"
            logger.error(error_msg)
            send_error_notification(error_msg, "Proxy Pool Refresh")
            return False
    
    def get_next_proxy(self) -> Optional[Dict]:
        """获取下一个可用代理"""
        # 检查是否需要刷新代理池
        if (not self.proxy_pool or 
            not self.last_refresh or 
            datetime.now() - self.last_refresh > timedelta(seconds=self.refresh_interval)):
            
            if not self.refresh_proxy_pool():
                return None
        
        # 如果没有可用代理
        if not self.proxy_pool:
            return None
        
        # 查找下一个可用代理
        attempts = 0
        while attempts < len(self.proxy_pool):
            proxy = self.proxy_pool[self.current_proxy_index]
            proxy_id = f"{proxy['proxy_info']['endpoint']}:{proxy['proxy_info']['port']}"
            
            # 移动到下一个代理
            self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_pool)
            attempts += 1
            
            # 检查这个代理是否在失败列表中
            if proxy_id not in self.failed_proxies:
                return proxy
        
        # 如果所有代理都失败了，清除失败列表并重试
        logger.warning("All proxies marked as failed, clearing failure list")
        self.failed_proxies.clear()
        
        if self.proxy_pool:
            return self.proxy_pool[self.current_proxy_index]
        
        return None
    
    def mark_proxy_failed(self, proxy: Dict):
        """标记代理为失败"""
        try:
            proxy_id = f"{proxy['proxy_info']['endpoint']}:{proxy['proxy_info']['port']}"
            self.failed_proxies.add(proxy_id)
            
            logger.warning(f"Marked proxy as failed: {proxy_id}")
            
            # 如果失败的代理太多，尝试刷新代理池
            if len(self.failed_proxies) >= len(self.proxy_pool) * 0.5:
                logger.warning("Too many failed proxies, refreshing pool...")
                self.refresh_proxy_pool()
                
        except Exception as e:
            logger.error(f"Error marking proxy as failed: {e}")
    
    def get_proxy_stats(self) -> Dict:
        """获取代理池统计信息"""
        return {
            'total_proxies': len(self.proxy_pool),
            'failed_proxies': len(self.failed_proxies),
            'current_index': self.current_proxy_index,
            'last_refresh': self.last_refresh.isoformat() if self.last_refresh else None,
            'api_requests_used': self.request_count,
            'next_refresh_in': (
                self.refresh_interval - (datetime.now() - self.last_refresh).seconds
                if self.last_refresh else 0
            )
        }
    
    def save_proxy_pool_backup(self, filename: str = None):
        """保存代理池备份"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"proxy_backup_{timestamp}.json"
        
        try:
            backup_data = {
                'timestamp': datetime.now().isoformat(),
                'proxy_count': len(self.proxy_pool),
                'proxies': self.proxy_pool,
                'stats': self.get_proxy_stats()
            }
            
            with open(f"data/{filename}", 'w') as f:
                json.dump(backup_data, f, indent=2)
            
            logger.info(f"Proxy pool backup saved: data/{filename}")
            
        except Exception as e:
            logger.error(f"Error saving proxy backup: {e}")

# 全局代理管理器实例
webshare_manager = None

def get_webshare_proxy_manager() -> WebshareProxyManager:
    """获取全局代理管理器实例"""
    global webshare_manager
    if webshare_manager is None:
        webshare_manager = WebshareProxyManager()
    return webshare_manager

def test_webshare_integration():
    """测试Webshare集成"""
    manager = get_webshare_proxy_manager()
    
    if not manager.api_token:
        print("❌ Webshare API token not configured")
        return False
    
    print("🔧 Testing Webshare API integration...")
    
    # 刷新代理池
    if manager.refresh_proxy_pool():
        print("✅ Proxy pool refreshed successfully")
        
        # 获取统计信息
        stats = manager.get_proxy_stats()
        print(f"📊 Proxy Stats: {stats['total_proxies']} total, {stats['failed_proxies']} failed")
        
        # 测试获取代理
        proxy = manager.get_next_proxy()
        if proxy:
            print(f"✅ Sample proxy: {proxy['proxy_info']['endpoint']}:{proxy['proxy_info']['port']}")
            
            # 测试代理
            is_working, response_time = manager.test_proxy(proxy)
            if is_working:
                print(f"✅ Proxy test successful: {response_time:.2f}s")
            else:
                print("⚠️  Proxy test failed")
            
            return True
        else:
            print("❌ Failed to get proxy")
            return False
    else:
        print("❌ Failed to refresh proxy pool")
        return False

if __name__ == "__main__":
    test_webshare_integration()