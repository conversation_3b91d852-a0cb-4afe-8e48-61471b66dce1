import logging
import time
import random
from typing import List, Dict, <PERSON><PERSON>, <PERSON><PERSON>
from datetime import datetime, timedelta
import pandas as pd
from pytrends.request import TrendReq
from pytrends.exceptions import TooManyRequestsError, ResponseError
from config import Config
from models import db, Game, Trend, Keyword, Alert
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GoogleTrendsAnalyzer:
    """Google Trends analysis and surge detection"""
    
    def __init__(self):
        # 获取代理配置
        proxies = self._get_proxies() if Config.USE_PROXY else []
        
        # 初始化 pytrends，确保 proxies 参数正确
        self.pytrends = TrendReq(
            hl='en-US',
            tz=360,
            timeout=(10, 25),
            proxies=proxies if proxies else [],  # 确保传递空列表而不是 None
            retries=2,
            backoff_factor=0.1
        )
        self.request_count = 0
        self.last_request_time = 0
        self.baseline_keywords = ['games', 'online games', 'browser games']
    
    def _get_proxies(self) -> List[str]:
        """Get proxy configuration"""
        if Config.USE_PROXY and Config.PROXY_HTTP:
            # pytrends expects a list of proxy URLs
            return [Config.PROXY_HTTP]
        return []
    
    def _rate_limit(self):
        """Implement rate limiting for Google Trends API"""
        current_time = time.time()
        if current_time - self.last_request_time < 1:  # At least 1 second between requests
            time.sleep(1)
        self.last_request_time = time.time()
        self.request_count += 1
        
        # Longer pause every 10 requests
        if self.request_count % 10 == 0:
            logger.info("Rate limiting: Taking longer pause")
            time.sleep(random.uniform(10, 20))
    
    def get_trends_data(self, keywords: List[str], timeframe: str = 'today 7-d', 
                       geo: str = '') -> Optional[pd.DataFrame]:
        """Get trends data for keywords"""
        try:
            self._rate_limit()
            
            # Limit to 5 keywords per request (Google Trends limit)
            keywords = keywords[:5]
            
            logger.info(f"Fetching trends for keywords: {keywords}")
            
            self.pytrends.build_payload(
                kw_list=keywords,
                timeframe=timeframe,
                geo=geo or Config.GOOGLE_TRENDS_GEO
            )
            
            # Get interest over time
            interest_df = self.pytrends.interest_over_time()
            
            if interest_df.empty:
                logger.warning(f"No trends data found for keywords: {keywords}")
                return None
            
            # Remove 'isPartial' column if it exists
            if 'isPartial' in interest_df.columns:
                interest_df = interest_df.drop(columns=['isPartial'])
            
            return interest_df
            
        except TooManyRequestsError:
            logger.warning("Too many requests to Google Trends, waiting...")
            time.sleep(random.uniform(60, 120))
            return None
        except ResponseError as e:
            logger.error(f"Google Trends response error: {e}")
            return None
        except Exception as e:
            logger.error(f"Error fetching trends data: {e}")
            return None
    
    def get_related_queries(self, keyword: str) -> Dict:
        """Get related queries for a keyword"""
        try:
            self._rate_limit()
            
            self.pytrends.build_payload(
                kw_list=[keyword],
                timeframe=Config.GOOGLE_TRENDS_TIMEFRAME,
                geo=Config.GOOGLE_TRENDS_GEO
            )
            
            related_queries = self.pytrends.related_queries()
            
            result = {
                'top': [],
                'rising': []
            }
            
            if keyword in related_queries:
                if related_queries[keyword]['top'] is not None:
                    result['top'] = related_queries[keyword]['top']['query'].tolist()[:10]
                if related_queries[keyword]['rising'] is not None:
                    result['rising'] = related_queries[keyword]['rising']['query'].tolist()[:10]
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting related queries for {keyword}: {e}")
            return {'top': [], 'rising': []}
    
    def calculate_growth_rate(self, data: pd.DataFrame, keyword: str) -> Tuple[float, Dict]:
        """Calculate growth rate and trend statistics"""
        try:
            if keyword not in data.columns:
                return 0.0, {}
            
            values = data[keyword].values
            values = values[values > 0]  # Remove zero values
            
            if len(values) < 2:
                return 0.0, {}
            
            # Calculate growth rate
            recent_avg = float(values[-3:].mean())  # Last 3 periods
            baseline_avg = float(values[:-3].mean())  # Earlier periods
            
            if baseline_avg == 0:
                growth_rate = 0.0
            else:
                growth_rate = ((recent_avg - baseline_avg) / baseline_avg) * 100
            
            # Calculate statistics
            stats = {
                'current_value': int(values[-1]),
                'recent_avg': recent_avg,
                'baseline_avg': baseline_avg,
                'max_value': int(values.max()),
                'min_value': int(values.min()),
                'trend_data': values.tolist()
            }
            
            return growth_rate, stats
            
        except Exception as e:
            logger.error(f"Error calculating growth rate for {keyword}: {e}")
            return 0.0, {}
    
    def analyze_game_trends(self, games: List[Game]) -> List[Dict]:
        """Analyze trends for a list of games"""
        results = []
        batch_size = 5
        
        for i in range(0, len(games), batch_size):
            batch = games[i:i + batch_size]
            keywords = [game.name for game in batch]
            
            try:
                # Get trends data
                trends_df = self.get_trends_data(
                    keywords,
                    timeframe=Config.GOOGLE_TRENDS_TIMEFRAME,
                    geo=Config.GOOGLE_TRENDS_GEO
                )
                
                if trends_df is None or trends_df.empty:
                    continue
                
                # Analyze each game in the batch
                for game in batch:
                    if game.name in trends_df.columns:
                        growth_rate, stats = self.calculate_growth_rate(trends_df, game.name)
                        
                        result = {
                            'game': game,
                            'keyword': game.name,
                            'growth_rate': growth_rate,
                            'stats': stats,
                            'is_surge': growth_rate >= Config.SURGE_THRESHOLD,
                            'timestamp': datetime.utcnow()
                        }
                        
                        results.append(result)
                        logger.info(f"Analyzed {game.name}: Growth rate {growth_rate:.2f}%")
                
                # Longer pause between batches
                time.sleep(random.uniform(15, 30))
                
            except Exception as e:
                logger.error(f"Error analyzing batch starting at index {i}: {e}")
                continue
        
        return results
    
    def save_trends_to_db(self, trend_results: List[Dict]):
        """Save trend analysis results to database"""
        saved_count = 0
        surge_count = 0
        
        from app import app
        with app.app_context():
            for result in trend_results:
                try:
                    game = result['game']
                    
                    # Create trend record
                    trend = Trend(
                        keyword=result['keyword'],
                        game_id=game.id,
                        trend_value=result['stats'].get('current_value', 0),
                        comparison_value=result['stats'].get('baseline_avg', 0),
                        growth_rate=result['growth_rate'],
                        trend_data=json.dumps(result['stats']),
                        date=datetime.utcnow().date(),
                        timeframe=Config.GOOGLE_TRENDS_TIMEFRAME,
                        region=Config.GOOGLE_TRENDS_GEO or 'global'
                    )
                    
                    db.session.add(trend)
                    saved_count += 1
                    
                    # Create alert if surge detected
                    if result['is_surge']:
                        alert = Alert(
                            game_id=game.id,
                            keyword=result['keyword'],
                            alert_type='surge',
                            threshold_value=Config.SURGE_THRESHOLD,
                            current_value=result['growth_rate'],
                            message=f"Surge detected: {result['keyword']} growth rate {result['growth_rate']:.2f}%",
                            is_sent=False
                        )
                        
                        db.session.add(alert)
                        surge_count += 1
                    
                    # Update or create keyword record
                    keyword_obj = Keyword.query.filter_by(
                        game_id=game.id,
                        keyword=result['keyword']
                    ).first()
                    
                    if not keyword_obj:
                        # Get related queries
                        related = self.get_related_queries(result['keyword'])
                        
                        keyword_obj = Keyword(
                            game_id=game.id,
                            keyword=result['keyword'],
                            related_keywords=json.dumps(related.get('top', [])),
                            search_suggestions=json.dumps(related.get('rising', [])),
                            search_volume=result['stats'].get('current_value', 0)
                        )
                        
                        db.session.add(keyword_obj)
                    else:
                        keyword_obj.search_volume = result['stats'].get('current_value', 0)
                        keyword_obj.updated_at = datetime.utcnow()
                
                except Exception as e:
                    logger.error(f"Error saving trend data for {result['keyword']}: {e}")
                    db.session.rollback()
                    continue
            
            try:
                db.session.commit()
                logger.info(f"Saved {saved_count} trend records, {surge_count} surge alerts")
            except Exception as e:
                logger.error(f"Error committing trend data: {e}")
                db.session.rollback()
    
    def get_surge_keywords(self, limit: int = 20) -> List[Dict]:
        """Get keywords with recent surge activity"""
        from app import app
        with app.app_context():
            trends = Trend.query\
                .filter(Trend.growth_rate >= Config.SURGE_THRESHOLD)\
                .filter(Trend.created_at >= datetime.utcnow() - timedelta(hours=24))\
                .order_by(Trend.growth_rate.desc())\
                .limit(limit).all()
            
            results = []
            for trend in trends:
                result = trend.to_dict()
                if trend.game:
                    result['game'] = trend.game.to_dict()
                results.append(result)
            
            return results
    
    def analyze_top_games(self, limit: int = 100) -> Dict:
        """Analyze trends for top recent games"""
        from app import app
        with app.app_context():
            # Get recent games
            recent_games = Game.query\
                .filter_by(is_active=True)\
                .filter(Game.created_at >= datetime.utcnow() - timedelta(days=7))\
                .order_by(Game.created_at.desc())\
                .limit(limit).all()
            
            if not recent_games:
                logger.warning("No recent games found for trend analysis")
                return {'analyzed': 0, 'surges': 0}
            
            logger.info(f"Starting trend analysis for {len(recent_games)} recent games")
            
            # Analyze trends
            trend_results = self.analyze_game_trends(recent_games)
            
            if trend_results:
                # Save to database
                self.save_trends_to_db(trend_results)
                
                surges = [r for r in trend_results if r['is_surge']]
                
                return {
                    'analyzed': len(trend_results),
                    'surges': len(surges),
                    'results': trend_results
                }
            
            return {'analyzed': 0, 'surges': 0}

# Main analysis function
def run_trends_analysis():
    """Run comprehensive trends analysis"""
    analyzer = GoogleTrendsAnalyzer()
    
    try:
        logger.info("Starting trends analysis")
        results = analyzer.analyze_top_games(limit=50)  # Analyze top 50 recent games
        logger.info(f"Trends analysis completed: {results}")
        return results
    except Exception as e:
        logger.error(f"Trends analysis failed: {e}")
        raise

if __name__ == "__main__":
    run_trends_analysis()