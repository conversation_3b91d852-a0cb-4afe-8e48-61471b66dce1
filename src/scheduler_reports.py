#!/usr/bin/env python3
"""
独立的报告和通知定时任务模块
负责定期发送日报和系统状态报告
"""

import schedule
import time
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from src.notifications import send_daily_report, send_error_notification
from app import create_app
from models import db, Game, Trend, RedditMetric, Alert

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler_reports.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class ReportsScheduler:
    """报告和通知定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self.last_report = None
        self.app = create_app()
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
    
    def send_daily_report_job(self):
        """发送每日报告"""
        logger.info("开始生成每日报告...")
        
        with self.app.app_context():
            try:
                # 收集统计数据
                today = datetime.utcnow().date()
                yesterday = today - timedelta(days=1)
                
                # 游戏统计
                total_games = Game.query.filter_by(is_active=True).count()
                new_games_today = Game.query.filter(
                    Game.created_at >= today,
                    Game.is_active == True
                ).count()
                
                # 趋势统计
                trends_today = Trend.query.filter(
                    Trend.created_at >= today
                ).count()
                
                surge_games = Trend.query.filter(
                    Trend.growth_rate > Config.SURGE_THRESHOLD,
                    Trend.created_at >= yesterday
                ).all()
                
                # Reddit统计
                reddit_analyzed = RedditMetric.query.filter(
                    RedditMetric.created_at >= today
                ).count()
                
                hot_reddit_games = db.session.query(
                    Game.name,
                    RedditMetric.heat_score
                ).join(
                    RedditMetric
                ).filter(
                    RedditMetric.created_at >= today,
                    RedditMetric.heat_score > 50
                ).order_by(
                    RedditMetric.heat_score.desc()
                ).limit(5).all()
                
                # 系统健康状态
                health_status = self._check_system_health()
                
                # 发送报告
                send_daily_report({
                    'total_games': total_games,
                    'new_games_today': new_games_today,
                    'trends_analyzed': trends_today,
                    'surge_games': surge_games,
                    'reddit_analyzed': reddit_analyzed,
                    'hot_reddit_games': hot_reddit_games,
                    'system_health': health_status
                })
                
                self.last_report = datetime.now()
                logger.info("每日报告发送成功")
                
                # 记录结构化统计信息
                logger.info(f"[STATS] reports_sent=1 type=daily_report "
                          f"total_games={total_games} new_games={new_games_today} "
                          f"trends={trends_today} reddit={reddit_analyzed}")
                
            except Exception as e:
                logger.error(f"发送每日报告失败: {e}", exc_info=True)
                try:
                    send_error_notification(f"每日报告生成失败: {str(e)}", "Reports Scheduler")
                except:
                    pass
    
    def _check_system_health(self):
        """检查系统健康状态"""
        try:
            # 检查数据库连接
            db.session.execute('SELECT 1')
            db_status = "正常"
        except:
            db_status = "异常"
        
        # 检查最近的数据更新
        last_scrape = Game.query.order_by(Game.created_at.desc()).first()
        last_trend = Trend.query.order_by(Trend.created_at.desc()).first()
        last_reddit = RedditMetric.query.order_by(RedditMetric.created_at.desc()).first()
        
        return {
            'database': db_status,
            'last_scrape': last_scrape.created_at if last_scrape else None,
            'last_trend': last_trend.created_at if last_trend else None,
            'last_reddit': last_reddit.created_at if last_reddit else None
        }
    
    def send_weekly_summary(self):
        """发送周报（可选）"""
        logger.info("生成周报...")
        
        with self.app.app_context():
            try:
                # 获取一周的统计数据
                week_ago = datetime.now() - timedelta(days=7)
                
                # 本周新游戏
                new_games = Game.query.filter(
                    Game.created_at >= week_ago
                ).count()
                
                # 本周热门游戏
                hot_games = db.session.query(
                    Game.name,
                    db.func.max(Trend.growth_rate).label('max_growth')
                ).join(
                    Trend
                ).filter(
                    Trend.created_at >= week_ago
                ).group_by(
                    Game.id, Game.name
                ).order_by(
                    db.func.max(Trend.growth_rate).desc()
                ).limit(10).all()
                
                # 发送周报通知
                message = f"📊 游戏监控周报\n\n"
                message += f"本周新增游戏: {new_games}\n\n"
                message += "本周热门游戏TOP10:\n"
                
                for i, (name, growth) in enumerate(hot_games, 1):
                    message += f"{i}. {name} (增长率: {growth:.1f}%)\n"
                
                from notifications import FeishuBot
                bot = FeishuBot()
                bot.send_text_message(message)
                
                logger.info("周报发送成功")
                
            except Exception as e:
                logger.error(f"发送周报失败: {e}")
    
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置报告定时任务...")
        
        # 每日报告
        daily_time = Config.DAILY_REPORT_TIME
        schedule.every().day.at(daily_time).do(self.send_daily_report_job)
        
        # 周报（可选，每周一9点）
        # schedule.every().monday.at("09:00").do(self.send_weekly_summary)
        
        logger.info(f"报告定时任务设置完成")
        logger.info(f"每日报告时间: {daily_time}")
    
    def start(self):
        """启动调度器"""
        logger.info("启动报告调度器...")
        
        try:
            self.setup_schedule()
            
            self.running = True
            logger.info("报告调度器启动成功")
            
            # 主循环
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("报告调度器被用户停止")
            self.stop()
        except Exception as e:
            logger.error(f"报告调度器错误: {e}")
            self.stop()
    
    def stop(self):
        """停止调度器"""
        logger.info("停止报告调度器...")
        self.running = False
        schedule.clear()
        logger.info("报告调度器已停止")


def main():
    """主函数"""
    scheduler = ReportsScheduler()
    scheduler.start()


if __name__ == "__main__":
    main()