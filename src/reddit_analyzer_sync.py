"""
Reddit游戏热度分析模块（同步版本）
解决异步环境警告问题
"""

import os
import praw
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict
import time
import re
from functools import lru_cache

logger = logging.getLogger(__name__)


@dataclass
class RedditMetrics:
    """Reddit数据指标"""
    game_name: str
    heat_score: int  # 0-100
    growth_rate: float  # 百分比
    post_count_24h: int
    total_upvotes_24h: int
    total_comments_24h: int
    top_post_title: str
    top_post_score: int
    top_post_url: str
    avg_comments: float
    unique_authors: int
    sentiment: str  # positive/neutral/negative
    subreddit_distribution: Dict[str, int]
    last_updated: datetime
    status: str = 'success'  # success/failed/no_data
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        data = asdict(self)
        data['last_updated'] = self.last_updated.isoformat()
        return data


class RedditAnalyzerSync:
    """Reddit游戏热度分析器（同步版本）"""
    
    # 目标子版块（按活跃度排序）
    GAMING_SUBREDDITS = [
        'gaming',           # 最大的游戏子版块
        'IndieGaming',      # 独立游戏
        'WebGames',         # 网页游戏
        'incremental_games', # 放置类游戏
        'AndroidGaming',    # 安卓游戏
        'iosgaming',        # iOS游戏
        'html5games'        # HTML5游戏
        # 'browsergames' 暂时移除，403错误
    ]
    
    # 热度计算权重
    HEAT_WEIGHTS = {
        'post_count': 0.25,
        'upvotes': 0.35,
        'comments': 0.25,
        'unique_authors': 0.15
    }
    
    # Reddit API 速率限制
    RATE_LIMIT_REQUESTS = 80  # 每分钟请求数（符合Reddit官方限制）
    RATE_LIMIT_WINDOW = 60    # 时间窗口（秒）
    
    def __init__(self, client_id: str = None, client_secret: str = None, user_agent: str = None):
        """初始化Reddit分析器"""
        self.client_id = client_id or os.getenv('REDDIT_CLIENT_ID')
        self.client_secret = client_secret or os.getenv('REDDIT_CLIENT_SECRET')
        self.user_agent = user_agent or os.getenv('REDDIT_USER_AGENT', 'GameMonitor/1.0')
        
        if not self.client_id or not self.client_secret:
            raise ValueError("Reddit API credentials not provided")
        
        # 初始化Reddit客户端
        self.reddit = praw.Reddit(
            client_id=self.client_id,
            client_secret=self.client_secret,
            user_agent=self.user_agent,
            check_for_async=False  # 禁用异步检查
        )
        
        # 速率限制跟踪
        self.request_times = []
        
        # 缓存历史数据用于计算增长率
        self._history_cache = defaultdict(list)
        
        logger.info("Reddit analyzer initialized (sync version)")
    
    def _rate_limit_check(self):
        """检查并等待速率限制"""
        now = time.time()
        
        # 清理旧的请求记录
        self.request_times = [t for t in self.request_times if now - t < self.RATE_LIMIT_WINDOW]
        
        # 如果达到速率限制，等待
        if len(self.request_times) >= self.RATE_LIMIT_REQUESTS:
            sleep_time = self.RATE_LIMIT_WINDOW - (now - self.request_times[0]) + 1
            if sleep_time > 0:
                logger.info(f"Rate limit reached, sleeping for {sleep_time:.1f} seconds")
                time.sleep(sleep_time)
        
        # 记录这次请求
        self.request_times.append(now)
    
    def analyze_game(self, game_name: str, search_variations: List[str] = None) -> RedditMetrics:
        """
        分析单个游戏的Reddit热度（同步版本）
        
        Args:
            game_name: 游戏名称
            search_variations: 搜索变体列表
            
        Returns:
            RedditMetrics: Reddit指标数据
        """
        try:
            # 准备搜索关键词
            search_terms = [game_name]
            if search_variations:
                search_terms.extend(search_variations)
            
            # 搜索相关帖子
            posts = self._search_game_posts(search_terms)
            
            if not posts:
                return self._create_empty_metrics(game_name, "No discussions found")
            
            # 计算各项指标
            metrics = self._calculate_metrics(posts)
            
            # 计算热度分数
            heat_score = self._calculate_heat_score(metrics)
            
            # 计算增长率
            growth_rate = self._calculate_growth_rate(game_name, metrics)
            
            # 获取最热帖子信息
            top_post = max(posts, key=lambda x: x['score']) if posts else None
            
            # 分析子版块分布
            subreddit_dist = self._analyze_subreddit_distribution(posts)
            
            # 分析情感倾向
            sentiment = self._analyze_sentiment(posts)
            
            return RedditMetrics(
                game_name=game_name,
                heat_score=heat_score,
                growth_rate=growth_rate,
                post_count_24h=len(posts),
                total_upvotes_24h=metrics['total_upvotes'],
                total_comments_24h=metrics['total_comments'],
                top_post_title=top_post['title'] if top_post else "",
                top_post_score=top_post['score'] if top_post else 0,
                top_post_url=top_post['url'] if top_post else "",
                avg_comments=metrics['avg_comments'],
                unique_authors=metrics['unique_authors'],
                sentiment=sentiment,
                subreddit_distribution=subreddit_dist,
                last_updated=datetime.now(),
                status='success'
            )
            
        except Exception as e:
            logger.error(f"Error analyzing game {game_name}: {str(e)}")
            return self._create_empty_metrics(game_name, str(e))
    
    def _search_game_posts(self, search_terms: List[str]) -> List[Dict]:
        """搜索游戏相关帖子（优化版）"""
        all_posts = []
        seen_ids = set()
        
        # 设置时间范围
        time_filter = datetime.now() - timedelta(hours=24)
        
        # 限制每个子版块的搜索数量（减少以降低总请求数）
        max_per_subreddit = 15
        
        # 优先搜索最活跃的子版块（减少子版块数量以降低请求频率）
        priority_subreddits = ['gaming', 'IndieGaming']  # 暂时只搜索最主要的两个
        
        for subreddit_name in priority_subreddits:
            try:
                # 速率限制检查
                self._rate_limit_check()
                
                subreddit = self.reddit.subreddit(subreddit_name)
                
                # 只搜索第一个关键词，避免过多请求
                search_query = search_terms[0]
                
                # 搜索帖子
                posts_found = 0
                for submission in subreddit.search(search_query, time_filter='day', limit=max_per_subreddit):
                    # 避免重复
                    if submission.id in seen_ids:
                        continue
                    
                    seen_ids.add(submission.id)
                    
                    # 检查是否在时间范围内
                    created_time = datetime.fromtimestamp(submission.created_utc)
                    if created_time < time_filter:
                        continue
                    
                    # 收集帖子数据
                    post_data = {
                        'id': submission.id,
                        'title': submission.title,
                        'score': submission.score,
                        'num_comments': submission.num_comments,
                        'author': str(submission.author) if submission.author else '[deleted]',
                        'subreddit': subreddit_name,
                        'url': f"https://reddit.com{submission.permalink}",
                        'created_utc': submission.created_utc,
                        'selftext': submission.selftext[:500] if submission.selftext else ""
                    }
                    
                    all_posts.append(post_data)
                    posts_found += 1
                    
                    if posts_found >= max_per_subreddit:
                        break
                
                logger.debug(f"Found {posts_found} posts in r/{subreddit_name} for '{search_query}'")
                
                # 增加延迟，避免请求过快
                time.sleep(1.0)
                
            except Exception as e:
                logger.warning(f"Error searching {subreddit_name}: {e}")
                continue
        
        logger.info(f"Total posts found: {len(all_posts)} for search terms: {search_terms}")
        return all_posts
    
    def _calculate_metrics(self, posts: List[Dict]) -> Dict:
        """计算帖子指标"""
        if not posts:
            return {
                'total_upvotes': 0,
                'total_comments': 0,
                'avg_comments': 0,
                'unique_authors': 0,
                'post_count': 0
            }
        
        total_upvotes = sum(p['score'] for p in posts)
        total_comments = sum(p['num_comments'] for p in posts)
        unique_authors = len(set(p['author'] for p in posts if p['author'] != '[deleted]'))
        
        return {
            'total_upvotes': total_upvotes,
            'total_comments': total_comments,
            'avg_comments': total_comments / len(posts) if posts else 0,
            'unique_authors': unique_authors,
            'post_count': len(posts)
        }
    
    def _calculate_heat_score(self, metrics: Dict) -> int:
        """计算热度分数（0-100）"""
        # 归一化各项指标
        normalized_scores = {
            'post_count': min(metrics.get('post_count', 0) / 20, 1.0),   # 20个帖子为满分
            'upvotes': min(metrics['total_upvotes'] / 2000, 1.0),        # 2000赞为满分
            'comments': min(metrics['total_comments'] / 500, 1.0),       # 500评论为满分
            'unique_authors': min(metrics['unique_authors'] / 15, 1.0)   # 15个独立作者为满分
        }
        
        # 计算加权分数
        weighted_score = sum(
            normalized_scores.get(key, 0) * weight 
            for key, weight in self.HEAT_WEIGHTS.items()
        )
        
        # 转换为0-100分数
        return int(weighted_score * 100)
    
    def _calculate_growth_rate(self, game_name: str, current_metrics: Dict) -> float:
        """计算增长率"""
        # 获取历史数据
        history = self._history_cache.get(game_name, [])
        
        # 添加当前数据到历史
        history.append({
            'timestamp': datetime.now(),
            'metrics': current_metrics
        })
        
        # 保留最近7天的数据
        cutoff_time = datetime.now() - timedelta(days=7)
        history = [h for h in history if h['timestamp'] > cutoff_time]
        self._history_cache[game_name] = history
        
        # 如果历史数据不足，返回0
        if len(history) < 2:
            return 0.0
        
        # 计算历史平均值（排除当前）
        historical_upvotes = [h['metrics']['total_upvotes'] for h in history[:-1]]
        historical_avg = sum(historical_upvotes) / len(historical_upvotes) if historical_upvotes else 1
        
        # 计算增长率
        current_value = current_metrics['total_upvotes']
        growth_rate = ((current_value - historical_avg) / historical_avg) * 100 if historical_avg > 0 else 0
        
        return round(growth_rate, 2)
    
    def _analyze_subreddit_distribution(self, posts: List[Dict]) -> Dict[str, int]:
        """分析帖子在各子版块的分布"""
        distribution = defaultdict(int)
        for post in posts:
            distribution[post['subreddit']] += 1
        return dict(distribution)
    
    def _analyze_sentiment(self, posts: List[Dict]) -> str:
        """简单的情感分析"""
        positive_keywords = [
            'awesome', 'amazing', 'love', 'great', 'best', 'fun',
            'excellent', 'fantastic', 'wonderful', 'addictive', 'brilliant'
        ]
        negative_keywords = [
            'bad', 'terrible', 'hate', 'worst', 'boring', 'sucks',
            'awful', 'disappointing', 'broken', 'buggy', 'trash'
        ]
        
        positive_count = 0
        negative_count = 0
        
        for post in posts:
            text = (post['title'] + ' ' + post.get('selftext', '')).lower()
            
            for word in positive_keywords:
                if word in text:
                    positive_count += post['score']
            
            for word in negative_keywords:
                if word in text:
                    negative_count += post['score']
        
        if positive_count > negative_count * 1.5:
            return 'positive'
        elif negative_count > positive_count * 1.5:
            return 'negative'
        else:
            return 'neutral'
    
    def _create_empty_metrics(self, game_name: str, error_msg: str = None) -> RedditMetrics:
        """创建空的指标数据"""
        return RedditMetrics(
            game_name=game_name,
            heat_score=0,
            growth_rate=0.0,
            post_count_24h=0,
            total_upvotes_24h=0,
            total_comments_24h=0,
            top_post_title="",
            top_post_score=0,
            top_post_url="",
            avg_comments=0.0,
            unique_authors=0,
            sentiment='neutral',
            subreddit_distribution={},
            last_updated=datetime.now(),
            status='no_data' if not error_msg else 'failed',
            error_message=error_msg
        )
    
    @lru_cache(maxsize=100)
    def validate_subreddit(self, subreddit_name: str) -> bool:
        """验证子版块是否存在且可访问"""
        try:
            self._rate_limit_check()
            subreddit = self.reddit.subreddit(subreddit_name)
            _ = subreddit.id
            return True
        except Exception:
            return False