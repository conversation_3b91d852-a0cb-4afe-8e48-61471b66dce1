#!/usr/bin/env python3
"""
Production-ready web scraper for CentOS deployment
Uses requests + BeautifulSoup for stability and resource efficiency
"""

import requests
import asyncio
import aiohttp
import hashlib
import logging
import time
import random
from datetime import datetime
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from typing import List, Dict, Optional
import json
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from models import db, Game, SiteConfig

# Setup logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL.upper()),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Game sites configuration - 优化后的列表，移除需要JavaScript的网站
GAME_SITES = [
    "https://itch.io/games/new-and-popular/platform-web",
    "https://itch.io/games/new-and-popular/free", 
    "https://itch.io/games/new-and-popular/has-demo",
    "https://itch.io/games/new-and-popular",
    "https://www.yiv.com/hot-games",
    "https://y8.com/new/games",
    # "https://poki.com/",  # 需要JavaScript，已移除
    "https://itch.io/games/has-demo",
    "https://www.yad.com/",
    "https://www.yiv.com/",
    "https://y8.com/popular/games",
    "https://gamaverse.com/",
    "https://itch.io/games",
    "https://www.miniplay.com/trending-games",
    "https://www.miniplay.com/daily-games",
    "https://www.crazygames.com/new",
    "https://www.crazygames.com/hot",
    "https://www.crazygames.com/updated",
    "https://y8.com/",
    "https://www.funnygames.org/new-games.html",
    "https://www.1001games.com/new",
    "https://www.sonsaur.com/",
    "https://www.play123.com/",
    "https://itch.io/games/newest/fresh",
    "https://kizi.com/",
    "https://www.yiv.com/best-games"
    # "https://www.pacogames.com/latest-games"  # 需要JavaScript，已移除
]

# Site-specific configurations for better parsing
SITE_CONFIGS = {
    'itch.io': {
        'game_selector': '.grid_outer',
        'name_selector': '.game_title a',
        'image_selector': '.game_thumb img',
        'url_selector': '.game_title a',
        'url_attr': 'href',
        'description_selector': '.game_text',
        'base_url': 'https://itch.io'
    },
    'yiv.com': {
        'game_selector': '.game-list-item, .game-item, .item, [class*="game"]',
        'name_selector': '.game-title, .title, h3, h4, a[title], img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description, .desc',
        'base_url': 'https://www.yiv.com'
    },
    'y8.com': {
        'game_selector': '.thumbs a, .thumb, .game-item',
        'name_selector': '.title, .name, img[alt], img[title]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://y8.com'
    },
    'poki.com': {
        'game_selector': '[data-cy="game-item"], .game-item, [class*="game"], .list-item',
        'name_selector': '[data-cy="game-title"], .game-title, h3, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://poki.com'
    },
    'crazygames.com': {
        'game_selector': '.list-item, .game-item, [class*="game"], .thumb',
        'name_selector': '.game-title, .title, h3, h4, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.crazygames.com'
    },
    'miniplay.com': {
        'game_selector': '.game, .game-item, .list-item, [class*="game"]',
        'name_selector': '.game-title, .title, h3, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.miniplay.com'
    },
    'funnygames.org': {
        'game_selector': '.game-item, .game, .item, [class*="game"]',
        'name_selector': '.game-title, .title, h3, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.funnygames.org'
    },
    '1001games.com': {
        'game_selector': '.game-item, .item, .thumb, [class*="game"]',
        'name_selector': '.title, h3, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.1001games.com'
    },
    'kizi.com': {
        'game_selector': '.game-item, .game, .list-item, [class*="game"]',
        'name_selector': '.game-title, .title, h3, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://kizi.com'
    },
    'pacogames.com': {
        'game_selector': '.game-item, .game, .list-item, [class*="game"]',
        'name_selector': '.title, h3, .game-title, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.pacogames.com'
    },
    'sonsaur.com': {
        'game_selector': '.game-item, .game, .item, [class*="game"]',
        'name_selector': '.title, h3, .game-title, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.sonsaur.com'
    },
    'play123.com': {
        'game_selector': '.game-item, .game, .item, [class*="game"]',
        'name_selector': '.title, h3, .game-title, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.play123.com'
    },
    'yad.com': {
        'game_selector': '.game-item, .game, .item, [class*="game"]',
        'name_selector': '.title, h3, .game-title, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://www.yad.com'
    },
    'gamaverse.com': {
        'game_selector': '.game-item, .game, .item, [class*="game"]',
        'name_selector': '.title, h3, .game-title, img[alt]',
        'image_selector': 'img',
        'url_selector': 'a',
        'url_attr': 'href',
        'description_selector': '.description',
        'base_url': 'https://gamaverse.com'
    }
}

class ProductionGameScraper:
    """Production-ready game scraper optimized for CentOS deployment"""
    
    def __init__(self):
        self.session = None
        self.scraped_count = 0
        self.error_count = 0
        self.start_time = time.time()
        
        # Setup request session with headers
        self.setup_session()
    
    def setup_session(self):
        """Setup requests session with appropriate headers and timeout"""
        self.session = requests.Session()
        
        # Common headers to appear more like a real browser
        self.session.headers.update({
            'User-Agent': Config.USER_AGENT,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        # Configure timeout and retry adapter
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=Config.MAX_RETRIES,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        # Configure proxy if enabled
        if Config.USE_PROXY and Config.PROXY_HTTP:
            proxies = {
                'http': Config.PROXY_HTTP,
                'https': Config.PROXY_HTTPS or Config.PROXY_HTTP
            }
            self.session.proxies.update(proxies)
            logger.info("Proxy configuration enabled")
    
    def get_site_config(self, url: str) -> Dict:
        """Get site-specific configuration"""
        domain = urlparse(url).netloc.lower()
        
        # Remove www. prefix for matching
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Find matching configuration
        for site_key, config in SITE_CONFIGS.items():
            if site_key in domain:
                return config
        
        # Return default configuration
        return {
            'game_selector': '.game, .game-item, .game-card, [class*="game"], .item, .card',
            'name_selector': '.title, .name, .game-title, h3, h4, a[title]',
            'image_selector': 'img',
            'url_selector': 'a',
            'url_attr': 'href',
            'description_selector': '.description, .desc, p',
            'base_url': f"https://{urlparse(url).netloc}"
        }
    
    def scrape_url(self, url: str) -> Optional[str]:
        """Scrape single URL with retry mechanism"""
        for attempt in range(Config.MAX_RETRIES):
            try:
                logger.info(f"Scraping {url} (attempt {attempt + 1})")
                
                # Add random delay to avoid being blocked
                if attempt > 0:
                    delay = random.uniform(Config.SCRAPING_DELAY_MIN, Config.SCRAPING_DELAY_MAX)
                    time.sleep(delay)
                
                response = self.session.get(
                    url,
                    timeout=Config.REQUEST_TIMEOUT,
                    allow_redirects=True
                )
                
                response.raise_for_status()
                
                # Check content type
                content_type = response.headers.get('content-type', '').lower()
                if 'text/html' not in content_type:
                    logger.warning(f"Non-HTML content received from {url}: {content_type}")
                    return None
                
                logger.info(f"Successfully scraped {url} ({len(response.content)} bytes)")
                return response.text
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"Scrape attempt {attempt + 1} failed for {url}: {e}")
                if attempt == Config.MAX_RETRIES - 1:
                    logger.error(f"All scrape attempts failed for {url}")
                    self.error_count += 1
                    return None
        
        return None
    
    def parse_games(self, html: str, url: str) -> List[Dict]:
        """Parse games from HTML content"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            config = self.get_site_config(url)
            games = []
            
            # Find game elements
            game_elements = soup.select(config['game_selector'])
            logger.info(f"Found {len(game_elements)} potential game elements on {urlparse(url).netloc}")
            
            for element in game_elements[:50]:  # Limit to 50 games per page
                try:
                    game_data = self.extract_game_data(element, url, config)
                    if game_data and self.validate_game_data(game_data):
                        games.append(game_data)
                except Exception as e:
                    logger.debug(f"Error extracting game data: {e}")
                    continue
            
            logger.info(f"Parsed {len(games)} valid games from {urlparse(url).netloc}")
            return games
            
        except Exception as e:
            logger.error(f"Error parsing games from {url}: {e}")
            return []
    
    def extract_game_data(self, element, url: str, config: Dict) -> Optional[Dict]:
        """Extract game data from HTML element"""
        try:
            # Extract name
            name = self.extract_text(element, config['name_selector'])
            if not name:
                return None
            
            # Extract image URL
            image_url = self.extract_image_url(element, config['image_selector'], config['base_url'])
            
            # Extract game URL
            game_url = self.extract_url(element, config['url_selector'], config['url_attr'], config['base_url'])
            
            # Extract description
            description = self.extract_text(element, config['description_selector'])
            
            game_data = {
                'name': name.strip(),
                'title': name.strip(),
                'description': description.strip() if description else None,
                'thumbnail_url': image_url,
                'game_url': game_url or url,
                'source_site': urlparse(url).netloc,
                'created_at': datetime.utcnow(),
                'is_active': True
            }
            
            # Generate hash ID for deduplication
            game_data['hash_id'] = self.generate_hash_id(game_data)
            
            return game_data
            
        except Exception as e:
            logger.debug(f"Error extracting game data: {e}")
            return None
    
    def extract_text(self, element, selector: str) -> Optional[str]:
        """Extract text from element using selector"""
        try:
            # Try multiple selectors if comma-separated
            selectors = [s.strip() for s in selector.split(',')]
            
            for sel in selectors:
                found = element.select_one(sel)
                if found:
                    # Handle different text extraction methods
                    text = found.get_text(strip=True)
                    if text and len(text) > 2 and len(text) < 200:  # Reasonable game name length
                        return text
                    
                    # Try title attribute as fallback
                    title = found.get('title', '').strip()
                    if title and len(title) > 2 and len(title) < 200:
                        return title
                    
                    # Try alt attribute for images
                    alt = found.get('alt', '').strip()
                    if alt and len(alt) > 2 and len(alt) < 200:
                        return alt
                    
                    # For image tags specifically, prioritize alt and title
                    if found.name == 'img':
                        alt = found.get('alt', '').strip()
                        if alt and len(alt) > 2:
                            return alt
                        title = found.get('title', '').strip()
                        if title and len(title) > 2:
                            return title
            
            # If no specific selector worked, try to find any text in the element
            all_text = element.get_text(strip=True)
            if all_text and 3 <= len(all_text) <= 100:  # Reasonable game name length
                # Clean up the text (remove extra whitespace)
                clean_text = ' '.join(all_text.split())
                return clean_text
            
            return None
            
        except Exception:
            return None
    
    def extract_image_url(self, element, selector: str, base_url: str) -> Optional[str]:
        """Extract image URL from element"""
        try:
            img_elem = element.select_one(selector)
            if img_elem:
                # Try different image URL attributes
                for attr in ['src', 'data-src', 'data-lazy-src', 'data-original']:
                    img_url = img_elem.get(attr)
                    if img_url:
                        # Handle relative URLs
                        if img_url.startswith('//'):
                            img_url = 'https:' + img_url
                        elif img_url.startswith('/'):
                            img_url = urljoin(base_url, img_url)
                        elif not img_url.startswith('http'):
                            img_url = urljoin(base_url, img_url)
                        
                        return img_url
            
            return None
            
        except Exception:
            return None
    
    def extract_url(self, element, selector: str, attr: str, base_url: str) -> Optional[str]:
        """Extract URL from element"""
        try:
            url_elem = element.select_one(selector)
            if url_elem:
                url = url_elem.get(attr)
                if url:
                    # Handle relative URLs
                    if url.startswith('//'):
                        url = 'https:' + url
                    elif url.startswith('/'):
                        url = urljoin(base_url, url)
                    elif not url.startswith('http'):
                        url = urljoin(base_url, url)
                    
                    return url
            
            return None
            
        except Exception:
            return None
    
    def validate_game_data(self, game: Dict) -> bool:
        """Validate game data"""
        if not game.get('name') or not game.get('source_site'):
            return False
        
        name = game['name'].strip()
        if len(name) < 2 or len(name) > 255:
            return False
        
        # Filter out common non-game elements
        invalid_names = [
            'home', 'about', 'contact', 'login', 'register', 'search',
            'menu', 'navigation', 'footer', 'header', 'advertisement',
            'ad', 'loading', 'more', 'next', 'previous', 'page'
        ]
        
        if name.lower() in invalid_names:
            return False
        
        return True
    
    def generate_hash_id(self, game: Dict) -> str:
        """Generate unique hash ID for game"""
        content = f"{game['name']}:{game.get('game_url', '')}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def save_games(self, games: List[Dict]) -> int:
        """Save games to database"""
        if not games:
            return 0
        
        try:
            from app import app
            with app.app_context():
                new_count = 0
                for game_data in games:
                    try:
                        # Check if game already exists
                        existing = Game.query.filter_by(hash_id=game_data['hash_id']).first()
                        if not existing:
                            game = Game(**game_data)
                            db.session.add(game)
                            new_count += 1
                    except Exception as e:
                        logger.debug(f"Error saving individual game: {e}")
                        continue
                
                db.session.commit()
                logger.info(f"Saved {new_count} new games to database")
                return new_count
                
        except Exception as e:
            logger.error(f"Error saving games to database: {e}")
            return 0
    
    def scrape_all_sites(self) -> Dict:
        """Scrape all game sites"""
        logger.info("Starting production scraper for all sites")
        
        results = {
            'total_sites': len(GAME_SITES),
            'successful_sites': 0,
            'total_games': 0,
            'new_games': 0,
            'errors': [],
            'execution_time': 0,
            'sites_processed': []
        }
        
        for url in GAME_SITES:
            try:
                site_name = urlparse(url).netloc
                logger.info(f"Processing site: {site_name}")
                
                # Scrape the site
                html_content = self.scrape_url(url)
                if not html_content:
                    results['errors'].append(f"{site_name}: Failed to fetch content")
                    continue
                
                # Parse games
                games = self.parse_games(html_content, url)
                if not games:
                    results['errors'].append(f"{site_name}: No games found")
                    continue
                
                # Save games
                new_games = self.save_games(games)
                
                # Update results
                results['total_games'] += len(games)
                results['new_games'] += new_games
                results['successful_sites'] += 1
                results['sites_processed'].append({
                    'site': site_name,
                    'url': url,
                    'games_found': len(games),
                    'new_games': new_games
                })
                
                self.scraped_count += 1
                
                # Add delay between sites
                time.sleep(random.uniform(Config.SCRAPING_DELAY_MIN, Config.SCRAPING_DELAY_MAX))
                
            except Exception as e:
                site_name = urlparse(url).netloc
                error_msg = f"{site_name}: {str(e)}"
                logger.error(f"Error processing site {url}: {e}")
                results['errors'].append(error_msg)
                self.error_count += 1
        
        # Calculate execution time
        results['execution_time'] = time.time() - self.start_time
        results['success'] = results['successful_sites'] > 0
        
        logger.info(f"Production scraping completed: {results}")
        return results
    
    def close(self):
        """Clean up resources"""
        if self.session:
            self.session.close()

def run_production_scrape():
    """Run production scraping process"""
    logger.info("Starting production game scraping process")
    
    scraper = None
    try:
        scraper = ProductionGameScraper()
        results = scraper.scrape_all_sites()
        
        # Log summary
        logger.info(f"Scraping Summary:")
        logger.info(f"  Sites processed: {results['successful_sites']}/{results['total_sites']}")
        logger.info(f"  Games found: {results['total_games']}")
        logger.info(f"  New games saved: {results['new_games']}")
        logger.info(f"  Execution time: {results['execution_time']:.1f}s")
        logger.info(f"  Errors: {len(results['errors'])}")
        
        if results['errors']:
            logger.warning("Errors encountered:")
            for error in results['errors']:
                logger.warning(f"  - {error}")
        
        return results
        
    except Exception as e:
        logger.error(f"Production scraping failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'total_sites': len(GAME_SITES),
            'successful_sites': 0,
            'total_games': 0,
            'new_games': 0
        }
    
    finally:
        if scraper:
            scraper.close()

if __name__ == "__main__":
    # Run scraping process
    result = run_production_scrape()
    
    # Exit with appropriate code
    if result.get('success', False):
        sys.exit(0)
    else:
        sys.exit(1)