"""
增强版Google Trends分析器
包含完善的错误处理和重试机制
"""

import asyncio
import logging
from typing import Dict, Optional, List
from pytrends.request import TrendReq
from datetime import datetime, timedelta
import time
import random
from src.webshare_proxy_manager import get_webshare_proxy_manager

logger = logging.getLogger(__name__)


class RobustGoogleTrends:
    """带容错机制的Google Trends分析器"""
    
    def __init__(self):
        self.retry_count = 3
        self.proxy_manager = None
        self.try_init_proxy_manager()
        
        # 速率限制配置
        self.rate_limit_delay = 2  # 请求间隔（秒）
        self.last_request_time = 0
        
        # 备用关键词配置
        self.fallback_timeframes = [
            'now 7-d',    # 最近7天
            'today 1-m',   # 最近1个月
            'today 3-m'    # 最近3个月
        ]
    
    def try_init_proxy_manager(self):
        """尝试初始化代理管理器"""
        try:
            self.proxy_manager = get_webshare_proxy_manager()
            logger.info("Proxy manager initialized for Google Trends")
        except Exception as e:
            logger.warning(f"Failed to initialize proxy manager: {e}")
            self.proxy_manager = None
    
    async def get_trend_data(self, keyword: str, timeframe: str = 'now 7-d') -> Optional[Dict]:
        """
        获取趋势数据，失败返回None
        
        Args:
            keyword: 搜索关键词
            timeframe: 时间范围
            
        Returns:
            Dict: 包含趋势数据的字典，或None
        """
        # 速率限制
        await self._rate_limit()
        
        for attempt in range(self.retry_count):
            try:
                # 获取代理
                proxy = self._get_proxy() if self.proxy_manager else None
                
                # 创建pytrends实例
                pytrends = self._create_pytrends_instance(proxy)
                
                # 构建查询
                pytrends.build_payload(
                    kw_list=[keyword],
                    timeframe=timeframe,
                    geo='',  # 全球数据
                    gprop=''  # 所有类别
                )
                
                # 获取时间序列数据
                interest_over_time = pytrends.interest_over_time()
                
                if not interest_over_time.empty and keyword in interest_over_time.columns:
                    # 提取数据
                    values = interest_over_time[keyword].values
                    current_value = int(values[-1])
                    
                    # 计算增长率
                    growth_rate = self._calculate_growth_rate(values)
                    
                    # 获取相关查询
                    related_queries = self._get_related_queries(pytrends, keyword)
                    
                    return {
                        'keyword': keyword,
                        'current_value': current_value,
                        'growth_rate': growth_rate,
                        'trend_data': values.tolist(),
                        'related_queries': related_queries,
                        'timeframe': timeframe,
                        'status': 'success',
                        'timestamp': datetime.now().isoformat()
                    }
                else:
                    # 数据为空，尝试不同的时间范围
                    if attempt < len(self.fallback_timeframes) - 1:
                        timeframe = self.fallback_timeframes[attempt + 1]
                        logger.info(f"No data for {keyword} in {timeframe}, trying {self.fallback_timeframes[attempt + 1]}")
                        continue
                    
                    return self._create_empty_result(keyword, "No trend data available")
                    
            except Exception as e:
                logger.warning(f"Google Trends attempt {attempt + 1} failed for '{keyword}': {e}")
                
                # 根据错误类型决定是否重试
                if self._should_retry(e):
                    # 指数退避
                    await asyncio.sleep((2 ** attempt) + random.uniform(0, 1))
                    
                    # 尝试更换代理
                    if self.proxy_manager and attempt < self.retry_count - 1:
                        self.proxy_manager.mark_proxy_failed(proxy)
                else:
                    break
        
        # 所有尝试失败
        return self._create_empty_result(keyword, "Failed to fetch trend data")
    
    async def get_batch_trends(self, keywords: List[str]) -> Dict[str, Dict]:
        """
        批量获取多个关键词的趋势数据
        
        Args:
            keywords: 关键词列表
            
        Returns:
            Dict: 关键词到趋势数据的映射
        """
        results = {}
        
        for keyword in keywords:
            try:
                data = await self.get_trend_data(keyword)
                results[keyword] = data
            except Exception as e:
                logger.error(f"Error getting trends for {keyword}: {e}")
                results[keyword] = self._create_empty_result(keyword, str(e))
        
        return results
    
    def _create_pytrends_instance(self, proxy: Optional[str]) -> TrendReq:
        """创建pytrends实例"""
        params = {
            'hl': 'en-US',
            'tz': 360,
            'timeout': (10, 30),
            'retries': 2,
            'backoff_factor': 0.1
        }
        
        if proxy:
            params['proxies'] = [proxy]
            
        # 添加自定义headers
        params['requests_args'] = {
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        }
        
        return TrendReq(**params)
    
    def _get_proxy(self) -> Optional[str]:
        """获取代理"""
        if self.proxy_manager:
            proxy_info = self.proxy_manager.get_proxy()
            if proxy_info:
                return f"http://{proxy_info['username']}:{proxy_info['password']}@{proxy_info['proxy_address']}:{proxy_info['port']}"
        return None
    
    def _calculate_growth_rate(self, values: List[float]) -> float:
        """计算增长率"""
        if len(values) < 2:
            return 0.0
        
        # 计算前半部分和后半部分的平均值
        mid_point = len(values) // 2
        first_half_avg = sum(values[:mid_point]) / mid_point
        second_half_avg = sum(values[mid_point:]) / (len(values) - mid_point)
        
        # 避免除零
        if first_half_avg == 0:
            return 100.0 if second_half_avg > 0 else 0.0
        
        # 计算增长率
        growth_rate = ((second_half_avg - first_half_avg) / first_half_avg) * 100
        
        return round(growth_rate, 2)
    
    def _get_related_queries(self, pytrends: TrendReq, keyword: str) -> List[Dict]:
        """获取相关查询"""
        try:
            related = pytrends.related_queries()
            
            if keyword in related and 'rising' in related[keyword]:
                rising_queries = related[keyword]['rising']
                if rising_queries is not None and not rising_queries.empty:
                    # 返回前5个相关查询
                    queries = []
                    for _, row in rising_queries.head(5).iterrows():
                        queries.append({
                            'query': row['query'],
                            'value': row['value']
                        })
                    return queries
        except Exception as e:
            logger.debug(f"Failed to get related queries: {e}")
        
        return []
    
    def _should_retry(self, error: Exception) -> bool:
        """判断是否应该重试"""
        error_str = str(error).lower()
        
        # 这些错误不应该重试
        no_retry_errors = [
            'too many requests',
            'quota exceeded',
            'invalid',
            'bad request'
        ]
        
        for no_retry in no_retry_errors:
            if no_retry in error_str:
                return False
        
        return True
    
    async def _rate_limit(self):
        """速率限制"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        self.last_request_time = time.time()
    
    def _create_empty_result(self, keyword: str, error_message: str = None) -> Dict:
        """创建空结果"""
        return {
            'keyword': keyword,
            'current_value': 0,
            'growth_rate': 0.0,
            'trend_data': [],
            'related_queries': [],
            'status': 'failed',
            'error': error_message,
            'timestamp': datetime.now().isoformat()
        }


# 单例实例
_robust_trends_instance = None


def get_robust_google_trends() -> RobustGoogleTrends:
    """获取RobustGoogleTrends单例实例"""
    global _robust_trends_instance
    if _robust_trends_instance is None:
        _robust_trends_instance = RobustGoogleTrends()
    return _robust_trends_instance