#!/usr/bin/env python3
"""
独立的Google趋势分析定时任务模块
负责定期分析游戏的搜索趋势
"""

import schedule
import time
import logging
import sys
import os
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from src.trends_analyzer import run_trends_analysis
from src.notifications import send_surge_notifications, send_error_notification

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scheduler_trends.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class TrendsScheduler:
    """趋势分析定时任务调度器"""
    
    def __init__(self):
        self.running = False
        self.last_run = None
        
        # 确保日志目录存在
        os.makedirs('logs', exist_ok=True)
    
    def run_trends_analysis_job(self):
        """执行趋势分析任务"""
        logger.info("开始执行Google趋势分析任务...")
        
        try:
            # 运行趋势分析
            result = run_trends_analysis()
            self.last_run = datetime.now()
            
            if result.get('success', False):
                logger.info(f"趋势分析成功: 分析游戏数={result.get('analyzed_count', 0)}, "
                          f"爆发游戏数={result.get('surge_count', 0)}")
                
                # 记录结构化统计信息
                logger.info(f"[STATS] analyzed={result.get('analyzed_count', 0)} "
                          f"surge={result.get('surge_count', 0)} "
                          f"errors={result.get('error_count', 0)} "
                          f"duration={result.get('duration_seconds', 0)}")
                
                # 如果有爆发的游戏，发送通知
                if result.get('surge_games'):
                    logger.info(f"发现 {len(result['surge_games'])} 个爆发游戏，发送通知...")
                    send_surge_notifications(result['surge_games'])
            else:
                logger.error(f"趋势分析失败: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            logger.error(f"趋势分析任务异常: {e}", exc_info=True)
            try:
                send_error_notification(f"趋势分析失败: {str(e)}", "Trends Scheduler")
            except:
                pass
    
    def check_surge_notifications(self):
        """检查并发送爆发通知"""
        logger.info("检查爆发游戏通知...")
        
        try:
            from app import app
            with app.app_context():
                from models import Trend
                from datetime import timedelta
                
                # 查找最近的高增长游戏
                recent_surges = Trend.query.filter(
                    Trend.growth_rate > Config.SURGE_THRESHOLD,
                    Trend.created_at >= datetime.utcnow() - timedelta(hours=1)
                ).all()
                
                if recent_surges:
                    logger.info(f"发现 {len(recent_surges)} 个近期爆发游戏")
                    send_surge_notifications(recent_surges)
                else:
                    logger.info("未发现新的爆发游戏")
                    
        except Exception as e:
            logger.error(f"检查爆发通知失败: {e}")
    
    def setup_schedule(self):
        """设置定时任务"""
        logger.info("设置趋势分析定时任务...")
        
        # 每4小时执行一次趋势分析
        interval = Config.TREND_CHECK_INTERVAL
        schedule.every(interval).hours.do(self.run_trends_analysis_job)
        
        # 每30分钟检查一次爆发通知
        schedule.every(30).minutes.do(self.check_surge_notifications)
        
        logger.info(f"趋势分析定时任务设置完成")
        logger.info(f"趋势分析频率: 每{interval}小时")
        logger.info(f"爆发检查频率: 每30分钟")
    
    def start(self):
        """启动调度器"""
        logger.info("启动趋势分析调度器...")
        
        try:
            self.setup_schedule()
            
            # 启动时立即执行一次
            logger.info("启动时执行一次趋势分析...")
            self.run_trends_analysis_job()
            
            self.running = True
            logger.info("趋势分析调度器启动成功")
            
            # 主循环
            while self.running:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
                
        except KeyboardInterrupt:
            logger.info("趋势分析调度器被用户停止")
            self.stop()
        except Exception as e:
            logger.error(f"趋势分析调度器错误: {e}")
            self.stop()
    
    def stop(self):
        """停止调度器"""
        logger.info("停止趋势分析调度器...")
        self.running = False
        schedule.clear()
        logger.info("趋势分析调度器已停止")


def main():
    """主函数"""
    scheduler = TrendsScheduler()
    scheduler.start()


if __name__ == "__main__":
    main()