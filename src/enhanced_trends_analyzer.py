#!/usr/bin/env python3
"""
增强的游戏趋势分析器
支持代理IP池、爆发增长检测、趋势可视化
"""

import logging
import time
import random
import json
import requests
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
from pytrends.request import TrendReq
from pytrends.exceptions import TooManyRequestsError, ResponseError
from config import Config
from models import db, Game, Trend, Keyword, Alert
from src.webshare_proxy_manager import get_webshare_proxy_manager
from src.notifications import send_error_notification
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProxyManager:
    """代理IP池管理器"""
    
    def __init__(self):
        self.proxy_pool = []
        self.current_proxy_index = 0
        self.load_proxy_pool()
    
    def load_proxy_pool(self):
        """从文件加载代理IP池"""
        try:
            with open(Config.PROXY_POOL_FILE, 'r') as f:
                proxies = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                self.proxy_pool = proxies
                logger.info(f"Loaded {len(self.proxy_pool)} proxies from pool")
        except FileNotFoundError:
            logger.warning(f"Proxy pool file {Config.PROXY_POOL_FILE} not found")
        except Exception as e:
            logger.error(f"Error loading proxy pool: {e}")
    
    def get_next_proxy(self) -> Optional[Dict]:
        """获取下一个可用代理"""
        if not self.proxy_pool or not Config.PROXY_ROTATION_ENABLED:
            return None
        
        proxy_url = self.proxy_pool[self.current_proxy_index]
        self.current_proxy_index = (self.current_proxy_index + 1) % len(self.proxy_pool)
        
        return {
            'http': proxy_url,
            'https': proxy_url
        }
    
    def test_proxy(self, proxy_dict: Dict) -> bool:
        """测试代理是否可用"""
        try:
            response = requests.get(
                'https://httpbin.org/ip', 
                proxies=proxy_dict, 
                timeout=10
            )
            return response.status_code == 200
        except:
            return False

class EnhancedTrendsAnalyzer:
    """增强的Google Trends分析器"""
    
    def __init__(self):
        # 代理管理器选择
        if Config.WEBSHARE_ENABLED and Config.WEBSHARE_API_TOKEN:
            logger.info("Using Webshare API proxy manager")
            self.webshare_manager = get_webshare_proxy_manager()
            self.proxy_manager = None
        else:
            logger.info("Using local proxy pool manager")
            self.proxy_manager = ProxyManager()
            self.webshare_manager = None
            
        self.current_proxy = None
        self.request_count = 0
        self.last_request_time = 0
        self.failed_requests = 0
        self.max_failed_requests = 5
        self.consecutive_failures = 0
        
        # 基准词汇池 - 用于相对热度对比
        baseline_config = getattr(Config, 'BASELINE_KEYWORDS', None)
        if baseline_config and isinstance(baseline_config, list):
            self.baseline_keywords = [kw.strip() for kw in baseline_config if kw.strip()]
        else:
            # 默认基准词汇
            self.baseline_keywords = ['minecraft', 'fortnite', 'game', 'mobile game', 'puzzle game']
        
        # 搜索量级别基准 (基于真实搜索数据)
        self.search_volume_benchmarks = {
            'ultra_high': ['good morning images'],          # 100k+ 搜索量
            'high': ['happy birthday image', 'mobile game'], # 50k+ 搜索量  
            'medium': ['baby shower', 'puzzle game'],       # 20k+ 搜索量
            'low': ['image to text converter'],             # 10k+ 搜索量
            'very_low': ['gpts', 'minecraft mod']           # 5k+ 搜索量
        }
        
        # 合并所有基准词汇
        all_benchmark_words = []
        for level_words in self.search_volume_benchmarks.values():
            all_benchmark_words.extend(level_words)
        self.baseline_keywords.extend([w for w in all_benchmark_words if w not in self.baseline_keywords])
        
        logger.info(f"Using {len(self.baseline_keywords)} baseline keywords with volume levels")
        
        # 缓存基准数据
        self.baseline_cache = {}
        self.cache_timestamp = None
        self.cache_duration = 3600  # 1小时缓存
        
        self._initialize_pytrends()
    
    def _initialize_pytrends(self):
        """初始化pytrends客户端"""
        try:
            # 获取代理
            if Config.USE_PROXY:
                if self.webshare_manager:
                    proxy_data = self.webshare_manager.get_next_proxy()
                    self.current_proxy = proxy_data if proxy_data else None
                elif self.proxy_manager:
                    self.current_proxy = self.proxy_manager.get_next_proxy()
                else:
                    self.current_proxy = None
            else:
                self.current_proxy = None
            
            # 初始化pytrends
            proxy_dict = {}  # 使用空字典而不是None
            if self.current_proxy:
                if 'http' in self.current_proxy:
                    # Webshare格式
                    proxy_dict = {
                        'http': self.current_proxy['http'],
                        'https': self.current_proxy['https']
                    }
                else:
                    # 传统格式
                    proxy_dict = self.current_proxy
            
            self.pytrends = TrendReq(
                hl='en-US',
                tz=360,
                timeout=(15, 30),
                proxies=proxy_dict if proxy_dict else {}
            )
            
            proxy_info = "No"
            if self.current_proxy:
                if 'proxy_info' in self.current_proxy:
                    endpoint = self.current_proxy['proxy_info']['endpoint']
                    proxy_info = f"Yes (Webshare: {endpoint})"
                else:
                    proxy_info = "Yes (Local)"
            
            logger.info(f"Initialized TrendReq with proxy: {proxy_info}")
            
        except Exception as e:
            error_msg = f"Failed to initialize pytrends: {e}"
            logger.error(error_msg)
            send_error_notification(error_msg, "Trends Analyzer")
            raise
    
    def _rotate_proxy(self):
        """轮换代理IP"""
        try:
            if not Config.USE_PROXY:
                return
            
            old_proxy = self.current_proxy
            
            # 标记当前代理为失败（如果适用）
            if old_proxy and self.webshare_manager:
                self.webshare_manager.mark_proxy_failed(old_proxy)
            
            # 获取新代理
            if self.webshare_manager:
                new_proxy = self.webshare_manager.get_next_proxy()
            elif self.proxy_manager:
                new_proxy = self.proxy_manager.get_next_proxy()
            else:
                new_proxy = None
            
            if new_proxy != old_proxy:
                self.current_proxy = new_proxy
                logger.info("Rotating to new proxy")
                self._initialize_pytrends()
                
        except Exception as e:
            error_msg = f"Error rotating proxy: {e}"
            logger.error(error_msg)
            send_error_notification(error_msg, "Proxy Rotation")
    
    def _rate_limit(self):
        """实现Google Trends API速率限制"""
        current_time = time.time()
        
        # 基础延迟
        if current_time - self.last_request_time < 2:
            time.sleep(2)
        
        self.last_request_time = time.time()
        self.request_count += 1
        
        # 每10个请求后长暂停
        if self.request_count % 10 == 0:
            logger.info("Rate limiting: Taking longer pause")
            time.sleep(random.uniform(15, 30))
        
        # 每5个请求轮换代理
        if self.request_count % 5 == 0:
            self._rotate_proxy()
    
    def get_trends_data_with_retry(self, keywords: List[str], timeframe: str = 'today 7-d', 
                                 geo: str = '', max_retries: int = 3) -> Optional[pd.DataFrame]:
        """带重试机制的趋势数据获取"""
        for attempt in range(max_retries):
            try:
                return self._get_trends_data(keywords, timeframe, geo)
            except TooManyRequestsError:
                self.consecutive_failures += 1
                error_msg = f"Google Trends rate limit exceeded (attempt {attempt + 1}/{max_retries})"
                logger.warning(error_msg)
                
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * 60  # 指数退避
                    logger.info(f"Waiting {wait_time} seconds before retry")
                    time.sleep(wait_time)
                    self._rotate_proxy()
                else:
                    # 最后一次重试失败，发送通知
                    send_error_notification(f"{error_msg}. All retries exhausted.", "Google Trends API")
                    
            except Exception as e:
                self.consecutive_failures += 1
                error_msg = f"Trends API error in attempt {attempt + 1}: {e}"
                logger.error(error_msg)
                
                if attempt < max_retries - 1:
                    time.sleep(30)
                    self._rotate_proxy()
                else:
                    # 发送详细错误通知
                    send_error_notification(
                        f"Failed to get trends data after {max_retries} attempts. "
                        f"Keywords: {keywords}. Error: {e}",
                        "Google Trends API"
                    )
        
        logger.error(f"Failed to get trends data after {max_retries} attempts")
        return None
    
    def get_baseline_data(self, timeframe: str = 'today 1-m', geo: str = '') -> Dict:
        """获取基准词汇的趋势数据"""
        current_time = time.time()
        
        # 检查缓存
        if (self.baseline_cache and self.cache_timestamp and 
            current_time - self.cache_timestamp < self.cache_duration):
            logger.debug("Using cached baseline data")
            return self.baseline_cache
        
        try:
            # 分批获取基准数据 - 每次只查询3个keyword避免API限制
            baseline_metrics = {}
            batch_size = 3
            
            for i in range(0, len(self.baseline_keywords), batch_size):
                batch = self.baseline_keywords[i:i+batch_size]
                logger.info(f"Fetching baseline batch {i//batch_size + 1}: {batch}")
                
                try:
                    baseline_df = self.get_trends_data_with_retry(
                        batch, 
                        timeframe=timeframe, 
                        geo=geo,
                        max_retries=1  # 减少重试避免过多API调用
                    )
                    
                    if baseline_df is not None and not baseline_df.empty:
                        # 计算该批次的基准指标
                        for keyword in batch:
                            if keyword in baseline_df.columns:
                                values = baseline_df[keyword].values
                                values = values[values >= 0]
                                
                                if len(values) > 0:
                                    baseline_metrics[keyword] = {
                                        'current_avg': float(values[-3:].mean()) if len(values) >= 3 else float(values.mean()),
                                        'overall_avg': float(values.mean()),
                                        'max_value': float(values.max()),
                                        'stability': float(np.std(values)) if len(values) > 1 else 0.0
                                    }
                    
                    # 批次间延迟避免率限制
                    if i + batch_size < len(self.baseline_keywords):
                        time.sleep(10)
                        
                except Exception as e:
                    logger.warning(f"Failed to get baseline batch {batch}: {e}")
                    continue
            
            if baseline_metrics:
                # 缓存结果
                self.baseline_cache = baseline_metrics
                self.cache_timestamp = current_time
                logger.info(f"Updated baseline cache with {len(baseline_metrics)} keywords")
            else:
                logger.warning("Failed to get any baseline data, using cached or default")
                
            return baseline_metrics if baseline_metrics else (self.baseline_cache if self.baseline_cache else {})
            
        except Exception as e:
            logger.error(f"Error getting baseline data: {e}")
            return self.baseline_cache if self.baseline_cache else {}

    def _get_trends_data(self, keywords: List[str], timeframe: str, geo: str) -> pd.DataFrame:
        """获取趋势数据"""
        self._rate_limit()
        
        # 限制关键词数量
        keywords = keywords[:5]
        
        logger.info(f"Fetching trends for: {keywords}")
        
        self.pytrends.build_payload(
            kw_list=keywords,
            timeframe=timeframe,
            geo=geo or Config.GOOGLE_TRENDS_GEO
        )
        
        interest_df = self.pytrends.interest_over_time()
        
        if interest_df.empty:
            logger.warning(f"No trends data for keywords: {keywords}")
            return None
        
        # 移除isPartial列
        if 'isPartial' in interest_df.columns:
            interest_df = interest_df.drop(columns=['isPartial'])
        
        return interest_df
    
    def calculate_advanced_growth_metrics(self, data: pd.DataFrame, keyword: str) -> Dict:
        """计算高级增长指标 - 包含多基准线对比"""
        try:
            if keyword not in data.columns:
                return {}
            
            values = data[keyword].values
            values = values[values >= 0]  # 保留0值但移除负值
            
            if len(values) < 4:
                return {}
            
            # 基础指标计算
            current_value = float(values[-1])
            previous_value = float(values[-2])
            recent_avg = float(values[-3:].mean())
            baseline_avg = float(values[:-3].mean())
            
            # 自身基准增长率
            if baseline_avg > 0:
                self_growth_rate = ((recent_avg - baseline_avg) / baseline_avg) * 100
            else:
                self_growth_rate = 0.0
            
            # 趋势方向
            if previous_value > 0:
                day_over_day = ((current_value - previous_value) / previous_value) * 100
            else:
                day_over_day = 0.0
            
            # 波动性计算
            volatility = float(np.std(values)) if len(values) > 1 else 0.0
            
            # 获取基准数据进行相对热度分析
            baseline_data = self.get_baseline_data()
            relative_metrics = self._calculate_relative_metrics(recent_avg, current_value, baseline_data)
            
            # 综合增长率评估 (自身基准 + 相对热度)
            growth_rate = self._calculate_comprehensive_growth_rate(
                self_growth_rate, 
                relative_metrics
            )
            
            # 趋势强度评估
            trend_strength = self._evaluate_trend_strength(
                growth_rate, 
                relative_metrics['heat_level']
            )
            
            # 峰值检测
            max_value = float(values.max())
            max_index = int(np.argmax(values))
            is_at_peak = max_index >= len(values) - 2
            
            return {
                'current_value': current_value,
                'previous_value': previous_value,
                'recent_avg': recent_avg,
                'baseline_avg': baseline_avg,
                'self_growth_rate': self_growth_rate,  # 自身基准增长率
                'growth_rate': growth_rate,           # 综合增长率
                'day_over_day': day_over_day,
                'volatility': volatility,
                'trend_strength': trend_strength,
                'max_value': max_value,
                'min_value': float(values.min()),
                'is_at_peak': is_at_peak,
                'trend_data': values.tolist(),
                'data_points': len(values),
                # 新增相对热度指标
                'relative_heat': relative_metrics['relative_heat'],
                'heat_level': relative_metrics['heat_level'],
                'volume_level': relative_metrics['volume_level'],
                'max_heat_score': relative_metrics.get('max_heat_score', 1.0),
                'benchmark_comparison': relative_metrics['comparison_details']
            }
            
        except Exception as e:
            logger.error(f"Error calculating metrics for {keyword}: {e}")
            return {}
    
    def _calculate_relative_metrics(self, recent_avg: float, current_value: float, baseline_data: Dict) -> Dict:
        """计算相对热度指标 - 包含搜索量级别分析"""
        try:
            if not baseline_data:
                return {
                    'relative_heat': 1.0,
                    'heat_level': 'unknown',
                    'comparison_details': {},
                    'volume_level': 'unknown'
                }
            
            # 与基准词汇对比
            comparisons = {}
            heat_scores = []
            volume_indicators = []
            
            for keyword, metrics in baseline_data.items():
                baseline_current = metrics['current_avg']
                if baseline_current > 0:
                    relative_ratio = recent_avg / baseline_current
                    
                    # 确定该基准词的搜索量级别
                    volume_level = self._get_keyword_volume_level(keyword)
                    
                    comparisons[keyword] = {
                        'ratio': relative_ratio,
                        'baseline_value': baseline_current,
                        'status': 'higher' if relative_ratio > 1.0 else 'lower',
                        'volume_level': volume_level
                    }
                    
                    # 根据搜索量级别加权热度分数
                    weighted_score = self._apply_volume_weight(relative_ratio, volume_level)
                    heat_scores.append(weighted_score)
                    
                    # 记录音量指标
                    if relative_ratio > 1.0:
                        volume_indicators.append(volume_level)
            
            # 计算综合热度分数
            if heat_scores:
                avg_heat_score = sum(heat_scores) / len(heat_scores)
                max_heat_score = max(heat_scores)
            else:
                avg_heat_score = 1.0
                max_heat_score = 1.0
            
            # 评估整体搜索量级别
            estimated_volume_level = self._estimate_volume_level(volume_indicators, max_heat_score)
            
            # 确定热度级别 (结合相对热度和搜索量级别)
            heat_level = self._determine_heat_level(max_heat_score, estimated_volume_level)
            
            return {
                'relative_heat': avg_heat_score,
                'heat_level': heat_level,
                'comparison_details': comparisons,
                'volume_level': estimated_volume_level,
                'max_heat_score': max_heat_score
            }
            
        except Exception as e:
            logger.error(f"Error calculating relative metrics: {e}")
            return {
                'relative_heat': 1.0,
                'heat_level': 'unknown', 
                'comparison_details': {},
                'volume_level': 'unknown'
            }
    
    def _get_keyword_volume_level(self, keyword: str) -> str:
        """获取关键词的搜索量级别"""
        for level, keywords in self.search_volume_benchmarks.items():
            if keyword in keywords:
                return level
        return 'unknown'
    
    def _apply_volume_weight(self, ratio: float, volume_level: str) -> float:
        """根据搜索量级别对热度比例加权"""
        weights = {
            'ultra_high': 1.5,   # 超高搜索量词汇权重最高
            'high': 1.3,         # 高搜索量
            'medium': 1.1,       # 中等搜索量
            'low': 0.9,          # 低搜索量
            'very_low': 0.7,     # 极低搜索量权重较低
            'unknown': 1.0       # 未知保持原值
        }
        
        weight = weights.get(volume_level, 1.0)
        return ratio * weight
    
    def _estimate_volume_level(self, volume_indicators: List[str], max_heat_score: float) -> str:
        """估算目标关键词的搜索量级别"""
        if not volume_indicators:
            return 'unknown'
        
        # 统计超过基准的搜索量级别
        level_counts = {}
        for level in volume_indicators:
            level_counts[level] = level_counts.get(level, 0) + 1
        
        # 找到最常见的级别
        most_common_level = max(level_counts, key=level_counts.get)
        
        # 根据热度分数调整级别估算
        if max_heat_score > 5.0:
            # 极高热度，可能达到更高搜索量级别
            level_order = ['very_low', 'low', 'medium', 'high', 'ultra_high']
            current_index = level_order.index(most_common_level) if most_common_level in level_order else 2
            upgraded_index = min(current_index + 1, len(level_order) - 1)
            return level_order[upgraded_index]
        elif max_heat_score > 2.0:
            return most_common_level
        else:
            # 较低热度，可能级别偏低
            level_order = ['very_low', 'low', 'medium', 'high', 'ultra_high']
            current_index = level_order.index(most_common_level) if most_common_level in level_order else 2
            downgraded_index = max(current_index - 1, 0)
            return level_order[downgraded_index]
    
    def _determine_heat_level(self, max_heat_score: float, volume_level: str) -> str:
        """综合热度分数和搜索量级别确定最终热度级别"""
        # 基于热度分数的基础判断
        if max_heat_score > 10.0:
            base_level = 'extremely_hot'
        elif max_heat_score > 5.0:
            base_level = 'very_hot'
        elif max_heat_score > 2.0:
            base_level = 'hot'
        elif max_heat_score > 1.0:
            base_level = 'moderate'
        elif max_heat_score > 0.5:
            base_level = 'cool'
        else:
            base_level = 'cold'
        
        # 根据搜索量级别进行调整
        if volume_level in ['ultra_high', 'high']:
            # 高搜索量级别的词汇更容易被认为是热门
            heat_levels = ['cold', 'cool', 'moderate', 'hot', 'very_hot', 'extremely_hot']
            current_index = heat_levels.index(base_level) if base_level in heat_levels else 2
            boosted_index = min(current_index + 1, len(heat_levels) - 1)
            return heat_levels[boosted_index]
        elif volume_level in ['very_low']:
            # 极低搜索量的词汇热度打折
            heat_levels = ['cold', 'cool', 'moderate', 'hot', 'very_hot', 'extremely_hot']
            current_index = heat_levels.index(base_level) if base_level in heat_levels else 2
            reduced_index = max(current_index - 1, 0)
            return heat_levels[reduced_index]
        else:
            return base_level
    
    def _calculate_comprehensive_growth_rate(self, self_growth: float, relative_metrics: Dict) -> float:
        """计算综合增长率"""
        try:
            relative_heat = relative_metrics.get('relative_heat', 1.0)
            heat_level = relative_metrics.get('heat_level', 'moderate')
            
            # 基础增长率
            base_growth = self_growth
            
            # 根据相对热度调整
            if heat_level == 'extremely_hot':
                # 极高热度：增长率加权提升
                adjusted_growth = base_growth * 1.5
            elif heat_level == 'very_hot':
                # 很高热度：适度提升
                adjusted_growth = base_growth * 1.3
            elif heat_level == 'hot':
                # 高热度：小幅提升
                adjusted_growth = base_growth * 1.1
            elif heat_level in ['cool', 'cold']:
                # 低热度：降低权重，避免虚假爆发
                adjusted_growth = base_growth * 0.7
            else:
                # 中等热度：保持原值
                adjusted_growth = base_growth
            
            return round(adjusted_growth, 2)
            
        except Exception as e:
            logger.error(f"Error calculating comprehensive growth rate: {e}")
            return self_growth
    
    def _evaluate_trend_strength(self, growth_rate: float, heat_level: str) -> str:
        """评估趋势强度"""
        try:
            # 基于综合增长率和热度级别
            if growth_rate > 100 and heat_level in ['extremely_hot', 'very_hot']:
                return 'explosive'
            elif growth_rate > 50:
                return 'strong_up'
            elif growth_rate > 20:
                return 'moderate_up'
            elif growth_rate > 0:
                return 'mild_up'
            elif growth_rate < -20:
                return 'declining'
            else:
                return 'stable'
                
        except Exception as e:
            logger.error(f"Error evaluating trend strength: {e}")
            return 'stable'
    
    def detect_surge_patterns(self, metrics: Dict) -> Dict:
        """检测爆发模式"""
        patterns = {
            'is_surge': False,
            'surge_type': 'none',
            'surge_confidence': 0.0,
            'alert_level': 'normal'
        }
        
        try:
            growth_rate = metrics.get('growth_rate', 0)
            day_over_day = metrics.get('day_over_day', 0)
            trend_strength = metrics.get('trend_strength', 'stable')
            is_at_peak = metrics.get('is_at_peak', False)
            
            # 爆发检测逻辑
            if growth_rate >= Config.SURGE_THRESHOLD:
                patterns['is_surge'] = True
                
                # 确定爆发类型
                if growth_rate > 200 and day_over_day > 100:
                    patterns['surge_type'] = 'viral_explosion'
                    patterns['surge_confidence'] = 0.95
                    patterns['alert_level'] = 'critical'
                elif growth_rate > 100:
                    patterns['surge_type'] = 'strong_surge'
                    patterns['surge_confidence'] = 0.85
                    patterns['alert_level'] = 'high'
                else:
                    patterns['surge_type'] = 'moderate_surge'
                    patterns['surge_confidence'] = 0.7
                    patterns['alert_level'] = 'medium'
                
                # 调整置信度
                if is_at_peak:
                    patterns['surge_confidence'] *= 1.1
                if trend_strength == 'explosive':
                    patterns['surge_confidence'] *= 1.2
                
                patterns['surge_confidence'] = min(patterns['surge_confidence'], 1.0)
        
        except Exception as e:
            logger.error(f"Error detecting surge patterns: {e}")
        
        return patterns
    
    def analyze_batch_games(self, games: List[Game]) -> List[Dict]:
        """批量分析游戏趋势"""
        results = []
        batch_size = 3  # 减小批次大小避免API限制
        total_batches = (len(games) + batch_size - 1) // batch_size
        successful_batches = 0
        failed_batches = 0
        
        logger.info(f"Starting batch analysis for {len(games)} games ({total_batches} batches)")
        start_time = datetime.now()
        
        for i in range(0, len(games), batch_size):
            batch_num = i // batch_size + 1
            batch = games[i:i + batch_size]
            keywords = [game.name for game in batch]
            
            try:
                logger.info(f"Processing batch {batch_num}/{total_batches}: {keywords}")
                
                # 获取趋势数据
                trends_df = self.get_trends_data_with_retry(
                    keywords,
                    timeframe=Config.GOOGLE_TRENDS_TIMEFRAME,
                    geo=Config.GOOGLE_TRENDS_GEO
                )
                
                if trends_df is None or trends_df.empty:
                    failed_batches += 1
                    error_msg = f"No trends data for batch {batch_num}: {keywords}"
                    logger.warning(error_msg)
                    
                    # 连续失败过多时发送警报
                    if self.consecutive_failures >= 3:
                        send_error_notification(
                            f"Multiple consecutive failures in trends analysis. "
                            f"Current batch: {batch_num}/{total_batches}. "
                            f"Failed batches: {failed_batches}",
                            "Trends Analysis"
                        )
                    continue
                
                # 重置连续失败计数
                self.consecutive_failures = 0
                successful_batches += 1
                
                # 分析每个游戏
                batch_results = 0
                for game in batch:
                    if game.name in trends_df.columns:
                        try:
                            metrics = self.calculate_advanced_growth_metrics(trends_df, game.name)
                            surge_patterns = self.detect_surge_patterns(metrics)
                            
                            result = {
                                'game': game,
                                'keyword': game.name,
                                'metrics': metrics,
                                'surge_patterns': surge_patterns,
                                'timestamp': datetime.utcnow(),
                                'analysis_version': '2.0',
                                'batch_info': {
                                    'batch_number': batch_num,
                                    'total_batches': total_batches
                                }
                            }
                            
                            results.append(result)
                            batch_results += 1
                            
                            level = surge_patterns['alert_level']
                            growth = metrics.get('growth_rate', 0)
                            logger.info(f"{game.name}: Growth {growth:.1f}% - {level}")
                            
                            # 发现爆发增长时立即记录
                            if surge_patterns['is_surge'] and growth > 100:
                                logger.warning(f"🚀 SURGE DETECTED: {game.name} - {growth:.1f}% growth!")
                                
                        except Exception as e:
                            logger.error(f"Error analyzing game {game.name}: {e}")
                            continue
                
                logger.info(f"Batch {batch_num} completed: {batch_results}/{len(batch)} games analyzed")
                
                # 批次间延迟
                if i + batch_size < len(games):
                    delay = random.uniform(30, 60)  # 增加延迟时间
                    logger.debug(f"Waiting {delay:.1f}s before next batch...")
                    time.sleep(delay)
                
            except Exception as e:
                failed_batches += 1
                error_msg = f"Critical error in batch {batch_num}: {e}"
                logger.error(error_msg)
                send_error_notification(
                    f"Batch analysis failure. Batch {batch_num}/{total_batches}. "
                    f"Keywords: {keywords}. Error: {str(e)}",
                    "Trends Analysis"
                )
                continue
        
        # 分析完成统计
        execution_time = (datetime.now() - start_time).total_seconds()
        success_rate = (successful_batches / total_batches) * 100 if total_batches > 0 else 0
        
        summary_msg = (
            f"Batch analysis completed: {len(results)} games analyzed. "
            f"Success: {successful_batches}/{total_batches} batches ({success_rate:.1f}%). "
            f"Execution time: {execution_time:.1f}s"
        )
        
        logger.info(summary_msg)
        
        # 如果成功率过低，发送警报
        if success_rate < 50 and total_batches > 2:
            send_error_notification(
                f"Low success rate in trends analysis: {success_rate:.1f}%. "
                f"Only {successful_batches}/{total_batches} batches succeeded. "
                f"This may indicate proxy or API issues.",
                "Trends Analysis Performance"
            )
        
        return results
    
    def generate_trend_chart_data(self, trend_data: List[float], keyword: str) -> Dict:
        """生成趋势图表数据"""
        try:
            # 创建时间序列
            end_date = datetime.now()
            dates = [(end_date - timedelta(days=len(trend_data)-1-i)).strftime('%m-%d') 
                    for i in range(len(trend_data))]
            
            # 计算移动平均
            window_size = min(3, len(trend_data))
            moving_avg = []
            for i in range(len(trend_data)):
                start_idx = max(0, i - window_size + 1)
                avg = sum(trend_data[start_idx:i+1]) / (i - start_idx + 1)
                moving_avg.append(round(avg, 2))
            
            return {
                'keyword': keyword,
                'dates': dates,
                'values': [round(v, 2) for v in trend_data],
                'moving_average': moving_avg,
                'max_value': max(trend_data),
                'min_value': min(trend_data),
                'chart_type': 'line',
                'color': self._get_trend_color(trend_data)
            }
            
        except Exception as e:
            logger.error(f"Error generating chart data for {keyword}: {e}")
            return {}
    
    def _get_trend_color(self, trend_data: List[float]) -> str:
        """根据趋势数据确定图表颜色"""
        if len(trend_data) < 2:
            return '#6c757d'  # 灰色
        
        recent_avg = sum(trend_data[-3:]) / min(3, len(trend_data))
        baseline_avg = sum(trend_data[:-3]) / max(1, len(trend_data) - 3)
        
        if recent_avg > baseline_avg * 1.5:
            return '#dc3545'  # 红色（爆发）
        elif recent_avg > baseline_avg * 1.2:
            return '#fd7e14'  # 橙色（上升）
        elif recent_avg > baseline_avg * 0.8:
            return '#28a745'  # 绿色（稳定）
        else:
            return '#6f42c1'  # 紫色（下降）
    
    def save_enhanced_results(self, results: List[Dict]):
        """保存增强的分析结果"""
        from app import app
        
        saved_count = 0
        surge_count = 0
        
        with app.app_context():
            for result in results:
                try:
                    game = result['game']
                    metrics = result['metrics']
                    surge_patterns = result['surge_patterns']
                    
                    # 创建趋势记录
                    trend = Trend(
                        keyword=result['keyword'],
                        game_id=game.id,
                        trend_value=metrics.get('current_value', 0),
                        comparison_value=metrics.get('baseline_avg', 0),
                        growth_rate=metrics.get('growth_rate', 0),
                        trend_data=json.dumps({
                            'metrics': metrics,
                            'surge_patterns': surge_patterns,
                            'chart_data': self.generate_trend_chart_data(
                                metrics.get('trend_data', []), 
                                result['keyword']
                            )
                        }),
                        date=datetime.utcnow().date(),
                        timeframe=Config.GOOGLE_TRENDS_TIMEFRAME,
                        region=Config.GOOGLE_TRENDS_GEO or 'global'
                    )
                    
                    db.session.add(trend)
                    saved_count += 1
                    
                    # 创建爆发警报
                    if surge_patterns['is_surge']:
                        alert = Alert(
                            game_id=game.id,
                            keyword=result['keyword'],
                            alert_type=surge_patterns['surge_type'],
                            threshold_value=Config.SURGE_THRESHOLD,
                            current_value=metrics.get('growth_rate', 0),
                            message=f"{surge_patterns['surge_type']} detected: {result['keyword']} "
                                   f"growth {metrics.get('growth_rate', 0):.1f}% "
                                   f"(confidence: {surge_patterns['surge_confidence']:.1%})",
                            is_sent=False
                        )
                        
                        db.session.add(alert)
                        surge_count += 1
                
                except Exception as e:
                    logger.error(f"Error saving result for {result['keyword']}: {e}")
                    db.session.rollback()
                    continue
            
            try:
                db.session.commit()
                logger.info(f"Saved {saved_count} trends, {surge_count} surge alerts")
            except Exception as e:
                logger.error(f"Error committing results: {e}")
                db.session.rollback()

def run_enhanced_trends_analysis(limit: int = 50):
    """运行增强的趋势分析"""
    analyzer = EnhancedTrendsAnalyzer()
    
    try:
        from app import app
        with app.app_context():
            # 获取最近的游戏
            recent_games = Game.query\
                .filter_by(is_active=True)\
                .filter(Game.created_at >= datetime.utcnow() - timedelta(days=3))\
                .order_by(Game.created_at.desc())\
                .limit(limit).all()
            
            if not recent_games:
                logger.warning("No recent games found")
                return {'analyzed': 0, 'surges': 0}
            
            logger.info(f"Starting enhanced analysis for {len(recent_games)} games")
            
            # 执行分析
            results = analyzer.analyze_batch_games(recent_games)
            
            if results:
                analyzer.save_enhanced_results(results)
                surges = [r for r in results if r['surge_patterns']['is_surge']]
                
                return {
                    'analyzed': len(results),
                    'surges': len(surges),
                    'success': True
                }
            
            return {'analyzed': 0, 'surges': 0, 'success': False}
    
    except Exception as e:
        logger.error(f"Enhanced trends analysis failed: {e}")
        raise

if __name__ == "__main__":
    run_enhanced_trends_analysis()