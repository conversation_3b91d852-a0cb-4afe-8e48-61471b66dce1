"""
简单的认证模块
"""

import hashlib
import jwt
import datetime
from functools import wraps
from flask import request, jsonify, current_app
from config import Config

def generate_token(username):
    """生成 JWT token"""
    payload = {
        'username': username,
        'exp': datetime.datetime.utcnow() + datetime.timedelta(seconds=Config.AUTH_TOKEN_EXPIRY),
        'iat': datetime.datetime.utcnow()
    }
    return jwt.encode(payload, Config.AUTH_SECRET_KEY, algorithm='HS256')

def verify_password(password):
    """验证密码是否正确"""
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    return password_hash == Config.AUTH_PASSWORD_HASH

def verify_token(token):
    """验证 JWT token"""
    try:
        payload = jwt.decode(token, Config.AUTH_SECRET_KEY, algorithms=['HS256'])
        return payload['username']
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def auth_required(f):
    """需要认证的装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]  # Bearer <token>
            except IndexError:
                return jsonify({'message': '无效的认证头'}), 401
        
        if not token:
            return jsonify({'message': '缺少认证令牌'}), 401
        
        username = verify_token(token)
        if not username:
            return jsonify({'message': '无效或过期的令牌'}), 401
        
        return f(*args, **kwargs)
    
    return decorated_function