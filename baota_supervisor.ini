# Supervisor 配置文件（宝塔进程守护管理器）
# 在宝塔面板 - 进程守护管理器中添加以下配置

[program:game_monitor_app]
# Flask 应用
directory=/www/wwwroot/game-monitor
command=/www/wwwroot/game-monitor/venv/bin/python app.py
user=www
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/game-monitor/logs/app_supervisor.log
environment=PATH="/www/wwwroot/game-monitor/venv/bin:%(ENV_PATH)s",PYTHONPATH="/www/wwwroot/game-monitor"

[program:game_monitor_reports]
# 报告生成调度器
directory=/www/wwwroot/game-monitor
command=/www/wwwroot/game-monitor/venv/bin/python -m src.scheduler_reports
user=www
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/game-monitor/logs/reports_supervisor.log
environment=PATH="/www/wwwroot/game-monitor/venv/bin:%(ENV_PATH)s",PYTHONPATH="/www/wwwroot/game-monitor"

[program:game_monitor_monitor]
# 系统监控调度器
directory=/www/wwwroot/game-monitor
command=/www/wwwroot/game-monitor/venv/bin/python -m scripts.monitoring.scheduler_monitor
user=www
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/game-monitor/logs/monitor_supervisor.log
environment=PATH="/www/wwwroot/game-monitor/venv/bin:%(ENV_PATH)s",PYTHONPATH="/www/wwwroot/game-monitor"

[program:game_monitor_scraper]
# 爬虫调度器
directory=/www/wwwroot/game-monitor
command=/www/wwwroot/game-monitor/venv/bin/python -m src.scheduler_scraper
user=www
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/game-monitor/logs/scraper_supervisor.log
environment=PATH="/www/wwwroot/game-monitor/venv/bin:%(ENV_PATH)s",PYTHONPATH="/www/wwwroot/game-monitor"

[program:game_monitor_trends]
# 趋势分析调度器
directory=/www/wwwroot/game-monitor
command=/www/wwwroot/game-monitor/venv/bin/python -m src.scheduler_trends
user=www
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/game-monitor/logs/trends_supervisor.log
environment=PATH="/www/wwwroot/game-monitor/venv/bin:%(ENV_PATH)s",PYTHONPATH="/www/wwwroot/game-monitor"

[program:game_monitor_reddit]
# Reddit 分析调度器
directory=/www/wwwroot/game-monitor
command=/www/wwwroot/game-monitor/venv/bin/python -m src.scheduler_reddit
user=www
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/www/wwwroot/game-monitor/logs/reddit_supervisor.log
environment=PATH="/www/wwwroot/game-monitor/venv/bin:%(ENV_PATH)s",PYTHONPATH="/www/wwwroot/game-monitor"