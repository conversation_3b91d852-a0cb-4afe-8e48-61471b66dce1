version: '3.8'

services:
  app:
    build: .
    container_name: game-monitor-app-${USER:-default}
    restart: unless-stopped
    ports:
      - "8088:8088"
    environment:
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - game-monitor-network
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mysql:
    image: mysql:8.0
    container_name: game-monitor-mysql-${USER:-default}
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - game-monitor-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 60s

  redis:
    image: redis:7-alpine
    container_name: game-monitor-redis-${USER:-default}
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - game-monitor-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

  scheduler:
    build: .
    container_name: game-monitor-scheduler-${USER:-default}
    restart: unless-stopped
    command: python scheduler.py
    environment:
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - game-monitor-network
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

volumes:
  mysql_data:
  redis_data:

networks:
  game-monitor-network:
    driver: bridge