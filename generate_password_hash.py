#!/usr/bin/env python3
"""
生成密码的 SHA256 哈希值
使用方法: python generate_password_hash.py
"""

import hashlib
import getpass

def generate_sha256_hash(password):
    """生成密码的 SHA256 哈希值"""
    return hashlib.sha256(password.encode()).hexdigest()

def main():
    print("=== 密码哈希生成器 ===")
    print("用于生成 AUTH_PASSWORD_HASH 配置")
    print()
    
    # 获取密码输入（不显示）
    password = getpass.getpass("请输入密码: ")
    confirm_password = getpass.getpass("请再次输入密码: ")
    
    if password != confirm_password:
        print("\n错误：两次输入的密码不一致！")
        return
    
    if not password:
        print("\n错误：密码不能为空！")
        return
    
    # 生成哈希
    password_hash = generate_sha256_hash(password)
    
    print("\n生成的密码哈希值：")
    print(password_hash)
    print("\n请将此哈希值复制到 .env 文件的 AUTH_PASSWORD_HASH 配置项中")
    
    # 示例验证
    print("\n示例 .env 配置：")
    print(f"AUTH_USERNAME=Zacharyhu")
    print(f"AUTH_PASSWORD_HASH={password_hash}")

if __name__ == "__main__":
    main()