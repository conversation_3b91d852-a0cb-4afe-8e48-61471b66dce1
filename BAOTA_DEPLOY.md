# 宝塔 LNMP 环境部署指南

本指南专门针对宝塔 Linux 面板的 LNMP 环境，不使用 Docker。

## 前置要求

- 宝塔 Linux 面板已安装
- LNMP 环境（Nginx + MySQL + PHP）
- Python 3.11+ （在软件商店安装）
- Node.js 18+ （在软件商店安装）
- Redis（可选，在软件商店安装）

## 快速部署步骤

### 1. 准备项目

```bash
# 进入网站目录
cd /www/wwwroot

# 克隆项目
git clone https://github.com/yourusername/game-monitor.git
cd game-monitor

# 设置权限
chown -R www:www .
```

### 2. 创建数据库

在宝塔面板 - 数据库管理：
- 创建数据库：`game_monitor`
- 创建用户：`game_monitor`
- 设置密码并记住

### 3. 配置项目

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置（使用宝塔文件管理器或命令行）
# 修改数据库配置为刚才创建的信息
nano .env
```

或直接使用提供的宝塔配置：
```bash
cp config_baota.py config_local.py
# 编辑 config_local.py 修改数据库密码
```

### 4. 运行部署脚本

```bash
# 添加执行权限
chmod +x deploy_baota.sh

# 运行部署脚本
./deploy_baota.sh
```

### 5. 在宝塔配置网站

#### 5.1 创建网站
- 网站管理 → 添加站点
- 域名：game-monitor.7makj.com
- 根目录：/www/wwwroot/game-monitor/frontend/build
- PHP版本：纯静态

#### 5.2 配置 Nginx
在网站设置 → 配置文件中，添加以下内容到 server 块中：

```nginx
# API 代理
location /api {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
}

# 旧版页面（可选）
location ~ ^/(classic|games|trends|keyword) {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# React 路由支持
location / {
    try_files $uri /index.html;
}
```

### 6. 设置进程守护

在宝塔面板 - 进程守护管理器，添加守护进程：

#### 主应用
- 名称：game-monitor-app
- 启动用户：www
- 运行目录：/www/wwwroot/game-monitor
- 启动命令：/www/wwwroot/game-monitor/venv/bin/python app.py

#### 调度器（5个独立进程）
1. **爬虫调度器**
   - 名称：game-monitor-scraper
   - 启动用户：www
   - 运行目录：/www/wwwroot/game-monitor
   - 启动命令：/www/wwwroot/game-monitor/venv/bin/python src/scheduler_scraper.py

2. **趋势分析调度器**
   - 名称：game-monitor-trends
   - 启动用户：www
   - 运行目录：/www/wwwroot/game-monitor
   - 启动命令：/www/wwwroot/game-monitor/venv/bin/python src/scheduler_trends.py

3. **Reddit分析调度器**
   - 名称：game-monitor-reddit
   - 启动用户：www
   - 运行目录：/www/wwwroot/game-monitor
   - 启动命令：/www/wwwroot/game-monitor/venv/bin/python src/scheduler_reddit.py

4. **报告生成调度器**
   - 名称：game-monitor-reports
   - 启动用户：www
   - 运行目录：/www/wwwroot/game-monitor
   - 启动命令：/www/wwwroot/game-monitor/venv/bin/python src/scheduler_reports.py

5. **系统监控调度器**
   - 名称：game-monitor-monitor
   - 启动用户：www
   - 运行目录：/www/wwwroot/game-monitor
   - 启动命令：/www/wwwroot/game-monitor/venv/bin/python scripts/monitoring/scheduler_monitor.py

### 7. 管理命令

使用提供的管理脚本：
```bash
# 启动所有服务
./manage.sh start

# 停止所有服务
./manage.sh stop

# 重启服务
./manage.sh restart

# 查看状态
./manage.sh status

# 查看日志
./manage.sh logs
```

## 端口配置

### 默认端口
- Flask API: 8088（内部使用，通过 Nginx 代理）
- 外部访问：80/443（通过 Nginx）

### 自定义端口
如需修改 Flask 端口，编辑 `.env` 文件：
```bash
PORT=9000  # 自定义端口
```

同时更新 Nginx 配置中的 proxy_pass。

## 常见问题

### 1. Python 版本问题
```bash
# 检查 Python 版本
python3 -V

# 如果版本低于 3.8，在宝塔软件商店安装更高版本
# 然后修改 deploy_baota.sh 中的 PYTHON_VERSION
```

### 2. 权限问题
```bash
# 确保 www 用户有权限
chown -R www:www /www/wwwroot/game-monitor
chmod 755 /www/wwwroot/game-monitor/logs
```

### 3. 模块导入错误
```bash
# 激活虚拟环境后重新安装
source venv/bin/activate
pip install -r requirements.txt
```

### 4. 数据库连接失败
- 检查数据库用户权限
- 确认密码正确
- 检查 MySQL 是否允许本地连接

### 5. 趋势分析错误
从日志看到 pytrends 的代理配置问题，修复方法：
```python
# 在 trends_analyzer.py 中修改
self.pytrends = TrendReq(
    hl='en-US',
    tz=360,
    timeout=(10, 25),
    proxies=[],  # 改为空列表而不是 None
    retries=2,
    backoff_factor=0.1
)
```

## 性能优化

### 1. 开启 Redis 缓存
在宝塔软件商店安装 Redis，项目会自动使用。

### 2. 配置 Nginx 缓存
```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 3. 开启 Gzip 压缩
在宝塔 Nginx 管理中开启 Gzip 压缩。

## 监控和日志

### 查看日志
```bash
# 应用日志
tail -f logs/app.log

# 各调度器日志
tail -f logs/scraper.log          # 爬虫调度器
tail -f logs/trends.log           # 趋势分析调度器
tail -f logs/reddit.log           # Reddit调度器
tail -f logs/reports.log          # 报告调度器
tail -f logs/monitor.log          # 监控调度器

# 查看所有日志
tail -f logs/*.log
```

### 监控面板
访问 https://your-domain.com 查看监控中心，实时了解系统状态。

## 更新部署

```bash
# 拉取最新代码
git pull

# 如果有新依赖
source venv/bin/activate
pip install -r requirements.txt

# 如果前端有更新
cd frontend
npm install
npm run build
cd ..

# 重启服务
./manage.sh restart
```

## 安全建议

1. **修改默认端口**：不要使用默认的 8088 端口
2. **配置防火墙**：只开放必要端口
3. **使用 HTTPS**：在宝塔申请 SSL 证书
4. **定期备份**：使用宝塔计划任务备份数据库
5. **限制 API 访问**：可以在 Nginx 配置 IP 白名单

## 联系支持

如遇到问题，请提供：
- 错误日志（logs/ 目录下）
- 宝塔面板版本
- Python 版本
- 具体错误信息