# Database Configuration
MYSQL_ROOT_PASSWORD=game_monitor_root_2024
MYSQL_DATABASE=game_monitor
MYSQL_USER=game_monitor_user
MYSQL_PASSWORD=game_monitor_pass_2024
MYSQL_HOST=mysql
MYSQL_PORT=3306

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secret-key-here-change-in-production
HOST=0.0.0.0
PORT=8088

# Google Trends Configuration
GOOGLE_TRENDS_GEO=
GOOGLE_TRENDS_TIMEFRAME=today 7-d
SURGE_THRESHOLD=50.0

# Baseline Keywords for Relative Heat Analysis (comma-separated)
BASELINE_KEYWORDS=minecraft,fortnite,game,mobile game,puzzle game

# Reddit API Configuration
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=GameMonitor/1.0 by YourUsername

# Feishu Bot Configuration
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/de8e56c9-6e4a-4a92-a503-ae0187bda905
FEISHU_SECRET=your-feishu-secret
ENABLE_FEISHU_ALERTS=true

# Scraping Configuration
SCRAPING_DELAY_MIN=1
SCRAPING_DELAY_MAX=3
MAX_RETRIES=3
REQUEST_TIMEOUT=30
CONCURRENT_SCRAPERS=5
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Proxy Configuration (Optional)
USE_PROXY=true
PROXY_HTTP=
PROXY_HTTPS=
PROXY_POOL_FILE=proxy_pool.txt
PROXY_ROTATION_ENABLED=false

# Webshare API Configuration (Advanced Proxy Management)
WEBSHARE_ENABLED=true
WEBSHARE_API_TOKEN=sh2xfr45kgbgy4ki9lysimx9322fklf6l627g62c
WEBSHARE_REFRESH_INTERVAL=3600

# Scheduling Configuration
DAILY_SCRAPE_TIME=02:00
TREND_CHECK_INTERVAL=4
DAILY_REPORT_TIME=09:00

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Authentication Configuration
AUTH_USERNAME=Zacharyhu
AUTH_PASSWORD_HASH=  # Use SHA256 hash of your password
AUTH_SECRET_KEY=your-jwt-secret-key-change-in-production
AUTH_TOKEN_EXPIRY=86400  # 24 hours in seconds