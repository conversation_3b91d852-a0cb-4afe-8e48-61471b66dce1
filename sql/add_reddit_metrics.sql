-- 添加Reddit分析指标表
-- 用于存储Reddit游戏热度分析数据

CREATE TABLE IF NOT EXISTS reddit_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    game_id INT NOT NULL,
    heat_score INT DEFAULT 0 COMMENT 'Heat score 0-100',
    growth_rate FLOAT DEFAULT 0.0 COMMENT 'Growth rate percentage',
    post_count_24h INT DEFAULT 0 COMMENT 'Post count in 24 hours',
    total_upvotes_24h INT DEFAULT 0 COMMENT 'Total upvotes in 24 hours',
    total_comments_24h INT DEFAULT 0 COMMENT 'Total comments in 24 hours',
    top_post_title TEXT COMMENT 'Top post title',
    top_post_score INT DEFAULT 0 COMMENT 'Top post score',
    top_post_url VARCHAR(500) COMMENT 'Top post URL',
    avg_comments FLOAT DEFAULT 0.0 COMMENT 'Average comments per post',
    unique_authors INT DEFAULT 0 COMMENT 'Unique author count',
    sentiment VARCHAR(20) DEFAULT 'neutral' COMMENT 'Sentiment: positive/neutral/negative',
    subreddit_distribution JSON COMMENT 'Distribution across subreddits',
    raw_data JSON COMMENT 'Raw Reddit data',
    status VARCHAR(20) DEFAULT 'success' COMMENT 'Status: success/failed/no_data',
    error_message TEXT COMMENT 'Error message if failed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    INDEX idx_reddit_game_created (game_id, created_at),
    INDEX idx_reddit_heat (heat_score),
    INDEX idx_reddit_growth (growth_rate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加示例查询注释
-- 获取最新Reddit热度数据：
-- SELECT g.name, r.* FROM reddit_metrics r 
-- JOIN games g ON r.game_id = g.id 
-- WHERE r.created_at = (SELECT MAX(created_at) FROM reddit_metrics WHERE game_id = r.game_id)
-- ORDER BY r.heat_score DESC;