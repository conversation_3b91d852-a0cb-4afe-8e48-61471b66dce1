-- Create database if not exists
CREATE DATABASE IF NOT EXISTS game_monitor DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE game_monitor;

-- Games table
CREATE TABLE IF NOT EXISTS games (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL COMMENT 'Game name',
    title VARCHAR(255) COMMENT 'Game title',
    description TEXT COMMENT 'Game description',
    thumbnail_url VARCHAR(500) COMMENT 'Thumbnail URL',
    game_url VARCHAR(500) COMMENT 'Game URL',
    source_site VARCHAR(255) COMMENT 'Source website',
    genre VARCHAR(100) COMMENT 'Game genre',
    tags TEXT COMMENT 'Tags list',
    player_count VARCHAR(50) COMMENT 'Player count',
    rating DECIMAL(3,2) COMMENT 'Rating',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    hash_id VARCHAR(64) UNIQUE COMMENT 'Unique identifier',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Is active',
    INDEX idx_name (name),
    INDEX idx_source (source_site),
    INDEX idx_created (created_at),
    INDEX idx_hash (hash_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Trends table
CREATE TABLE IF NOT EXISTS trends (
    id INT AUTO_INCREMENT PRIMARY KEY,
    keyword VARCHAR(255) NOT NULL COMMENT 'Keyword',
    game_id INT COMMENT 'Associated game ID',
    trend_value INT DEFAULT 0 COMMENT 'Trend value',
    comparison_value INT DEFAULT 0 COMMENT 'Comparison value',
    growth_rate DECIMAL(8,2) DEFAULT 0 COMMENT 'Growth rate %',
    trend_data JSON COMMENT 'Complete trend data',
    date DATE COMMENT 'Data date',
    timeframe VARCHAR(20) DEFAULT '7d' COMMENT 'Time range',
    region VARCHAR(10) DEFAULT 'global' COMMENT 'Region',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    INDEX idx_keyword (keyword),
    INDEX idx_date (date),
    INDEX idx_growth (growth_rate),
    INDEX idx_game_date (game_id, date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Keywords table
CREATE TABLE IF NOT EXISTS keywords (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_id INT COMMENT 'Associated game ID',
    keyword VARCHAR(255) NOT NULL COMMENT 'Main keyword',
    related_keywords JSON COMMENT 'Related keywords',
    search_suggestions JSON COMMENT 'Search suggestions',
    search_volume INT DEFAULT 0 COMMENT 'Search volume',
    competition_level VARCHAR(20) COMMENT 'Competition level',
    avg_cpc DECIMAL(6,2) COMMENT 'Average CPC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    INDEX idx_game_id (game_id),
    INDEX idx_keyword (keyword),
    INDEX idx_volume (search_volume),
    UNIQUE KEY unique_game_keyword (game_id, keyword)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Alerts table
CREATE TABLE IF NOT EXISTS alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    game_id INT COMMENT 'Associated game ID',
    keyword VARCHAR(255) COMMENT 'Keyword',
    alert_type VARCHAR(50) DEFAULT 'surge' COMMENT 'Alert type',
    threshold_value DECIMAL(8,2) DEFAULT 50.0 COMMENT 'Threshold value',
    current_value DECIMAL(8,2) DEFAULT 0 COMMENT 'Current value',
    message TEXT COMMENT 'Alert message',
    is_sent BOOLEAN DEFAULT FALSE COMMENT 'Is sent',
    sent_at TIMESTAMP NULL COMMENT 'Sent time',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE,
    INDEX idx_game_id (game_id),
    INDEX idx_sent (is_sent),
    INDEX idx_type (alert_type),
    INDEX idx_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Site configurations table
CREATE TABLE IF NOT EXISTS site_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_name VARCHAR(255) NOT NULL COMMENT 'Site name',
    site_url VARCHAR(500) NOT NULL COMMENT 'Site URL',
    selectors JSON COMMENT 'CSS selectors config',
    is_active BOOLEAN DEFAULT TRUE COMMENT 'Is active',
    last_scraped TIMESTAMP NULL COMMENT 'Last scraped time',
    scrape_count INT DEFAULT 0 COMMENT 'Scrape count',
    error_count INT DEFAULT 0 COMMENT 'Error count',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_site_name (site_name),
    UNIQUE KEY unique_site_url (site_url)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial site configurations
INSERT INTO site_configs (site_name, site_url, selectors, is_active) VALUES
('Itch.io New Popular Web', 'https://itch.io/games/new-and-popular/platform-web', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Itch.io New Popular Free', 'https://itch.io/games/new-and-popular/free', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Itch.io New Popular Demo', 'https://itch.io/games/new-and-popular/has-demo', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Itch.io New Popular', 'https://itch.io/games/new-and-popular', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Itch.io Demo', 'https://itch.io/games/has-demo', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Itch.io All Games', 'https://itch.io/games', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Itch.io Newest Fresh', 'https://itch.io/games/newest/fresh', '{"game_selector": ".game_cell", "name_selector": ".title", "image_selector": ".game_thumb img", "description_selector": ".game_text", "url_selector": ".title", "url_attr": "href"}', TRUE),
('Poki', 'https://poki.com/', '{"game_selector": "[data-cy=\"game-item\"]", "name_selector": "[data-cy=\"game-title\"]", "image_selector": "img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Y8 New Games', 'https://y8.com/new/games', '{"game_selector": ".game-item", "name_selector": ".game-name", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Y8 Popular Games', 'https://y8.com/popular/games', '{"game_selector": ".game-item", "name_selector": ".game-name", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Y8 All Games', 'https://y8.com/', '{"game_selector": ".game-item", "name_selector": ".game-name", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('CrazyGames New', 'https://www.crazygames.com/new', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('CrazyGames Hot', 'https://www.crazygames.com/hot', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('CrazyGames Updated', 'https://www.crazygames.com/updated', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Kizi', 'https://kizi.com/', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Yiv Hot Games', 'https://www.yiv.com/hot-games', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Yiv All Games', 'https://www.yiv.com/', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Yiv Best Games', 'https://www.yiv.com/best-games', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Yad', 'https://www.yad.com/', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Gamaverse', 'https://gamaverse.com/', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Miniplay Trending', 'https://www.miniplay.com/trending-games', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Miniplay Daily', 'https://www.miniplay.com/daily-games', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('FunnyGames New', 'https://www.funnygames.org/new-games.html', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('1001Games New', 'https://www.1001games.com/new', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Sonsaur', 'https://www.sonsaur.com/', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('Play123', 'https://www.play123.com/', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE),
('PacoGames Latest', 'https://www.pacogames.com/latest-games', '{"game_selector": ".game-item", "name_selector": ".game-title", "image_selector": ".game-thumb img", "url_selector": "a", "url_attr": "href"}', TRUE);

-- Create indexes for performance
CREATE INDEX idx_games_compound ON games(source_site, created_at, is_active);
CREATE INDEX idx_trends_compound ON trends(keyword, date, growth_rate);
CREATE INDEX idx_alerts_compound ON alerts(alert_type, is_sent, created_at);

-- Show tables
SHOW TABLES;