# HTML5 Game Monitor System 🎮

一个自动化的 HTML5 游戏监控系统，实现游戏数据爬取、趋势分析、Reddit 社区监控和智能通知。

## 系统架构概览 🏗️

```
┌─────────────────────────────────────────┐
│      Next.js 全栈应用 (端口 3000)        │
│   (前端 + API + Prisma 数据库访问)       │
└────────────────┬────────────────────────┘
                 │
                 ↓
┌─────────────────────────────────────────┐
│            MySQL 数据库                  │
└────────────────↑────────────────────────┘
                 │
┌─────────────────────────────────────────┐
│        Python 数据采集脚本               │
│  (爬虫、Reddit分析、趋势分析、通知)      │
└─────────────────────────────────────────┘
```

## 核心功能 ✨

- **🕷️ 自动爬虫**: 监控 27 个游戏网站，每日定时抓取最新游戏
- **📊 趋势分析**: 集成 Google Trends API，实时分析游戏热度趋势
- **💬 Reddit 监控**: 通过 Reddit API 监测游戏社区讨论热度
- **🔔 智能通知**: 飞书机器人推送热度暴涨提醒和每日报告
- **📈 现代化仪表板**: Next.js 14 + TypeScript + Tailwind CSS 实现的专业数据可视化界面
- **🔧 模块化架构**: 5 个独立调度器，互不干扰，高可用设计
- **📱 响应式设计**: 完美支持桌面端和移动端访问

## 快速开始 🚀

### 1. 克隆项目
```bash
git clone https://github.com/yourusername/game-monitor.git
cd game-monitor
```

### 2. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入必要信息
# - 数据库配置 (MySQL)
# - Redis 配置（可选）
# - 飞书 Webhook URL
# - Reddit API 凭据
nano .env
```

### 3. 启动 Next.js 应用

```bash
# 进入 Next.js 目录
cd game-monitor-nextjs

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库连接

# 初始化 Prisma
npx prisma generate

# 开发环境启动
npm run dev  # 默认运行在 http://localhost:3000

# 生产环境构建
npm run build
npm start
```

### 4. 启动 Python 数据采集脚本

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动各个调度器
python -m src.scheduler_scraper    # 网站爬虫
python -m src.scheduler_reddit     # Reddit 分析
python -m src.scheduler_trends     # 趋势分析
python -m src.scheduler_monitor    # 系统监控
```

## 系统功能详解 📚

### 1. 仪表板 (Dashboard)
- **核心指标卡片**: 实时显示游戏总数、今日新增、热度暴涨、监控网站数
- **7日游戏发现趋势**: 动态折线图展示每日新增游戏数量
- **网站贡献度分析**: 饼图展示各网站游戏贡献占比
- **实时热门游戏**: 列表展示最新发现的游戏及增长率

### 2. 游戏中心 (Game Center)
- **高级搜索**: 支持游戏名称、描述、标签搜索
- **多维筛选**: 按来源网站、时间排序
- **视图切换**: 表格视图和卡片视图自由切换
- **游戏详情**: 查看游戏完整信息和趋势分析

### 3. 趋势分析 (Trends Analysis)
- **Google Trends 集成**: 可视化展示搜索趋势数据
- **Reddit 热度监控**: 显示社区讨论热度指标
- **多维度分析**: 结合搜索量和社区活跃度综合评估
- **趋势图表**: ECharts 实现的交互式数据可视化

### 4. 监控中心 (Monitor Center)
- **调度器状态**: 实时显示5个调度器运行状态
- **日志分析**: 从日志文件自动解析运行情况
- **错误追踪**: 显示最近错误信息和失败率
- **系统指标**: 数据库连接、API响应等系统健康度

## API 接口文档 📡

### 统计接口
```
GET /api/stats
返回: {
  "total_games": 1057,
  "today_games": 368,
  "trending_count": 6,
  "active_sites": 18
}
```

### 游戏列表
```
GET /api/games?page=1&per_page=20&search=&source=
返回: {
  "games": [...],
  "total": 1057,
  "pages": 53,
  "current_page": 1
}
```

### 趋势数据
```
GET /api/trending-games
返回: {
  "trending_games": [...],
  "total": 10,
  "timestamp": "2025-08-03T15:35:55"
}
```

### 调度器状态
```
GET /api/scheduler/logs
返回: {
  "scraper_manager": {
    "name": "爬虫调度器",
    "status": "healthy",
    "success_rate": 94.2,
    "last_run": "2025-08-03 02:00:00"
  },
  ...
}
```

## 技术栈 🛠️

### 后端
- **Flask**: Web 框架
- **SQLAlchemy**: ORM 数据库管理
- **Playwright**: 高级网页爬虫
- **pytrends**: Google Trends API
- **PRAW**: Reddit API 客户端
- **APScheduler**: 定时任务调度

### 前端
- **React 18**: 现代化前端框架
- **TypeScript**: 类型安全的 JavaScript
- **Ant Design 5**: 企业级 UI 组件库
- **ECharts**: 强大的数据可视化库
- **Axios**: HTTP 客户端

### 基础设施
- **MySQL**: 主数据库存储
- **Redis**: 缓存和会话管理
- **Docker**: 容器化部署
- **Nginx**: 反向代理（生产环境）

## 调度器说明 ⚙️

系统包含 5 个独立运行的调度器：

1. **爬虫调度器** (`scheduler_scraper.py`)
   - 每日凌晨 2:00 执行
   - 爬取 27 个游戏网站
   - 支持反爬虫策略

2. **趋势分析器** (`scheduler_trends.py`)
   - 每 4 小时执行一次
   - 调用 Google Trends API
   - 计算游戏热度增长率

3. **Reddit 分析器** (`scheduler_reddit.py`)
   - 每 4 小时执行一次
   - 监控游戏相关 subreddit
   - 统计帖子和评论数据

4. **报告生成器** (`scheduler_reports.py`)
   - 每日上午 9:00 执行
   - 生成数据分析报告
   - 发送飞书通知

5. **系统监控器** (`scheduler_monitor.py`)
   - 每 30 分钟执行一次
   - 检查系统健康状态
   - 异常告警

## 部署指南 🚢

### Docker 部署（推荐）
```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 传统部署
1. 安装 MySQL 和 Redis
2. 配置 Nginx 反向代理
3. 使用 Supervisor 管理进程
4. 配置系统定时任务

## 监控和维护 🔍

### 日志位置
- 应用日志: `logs/app.log`
- 爬虫日志: `logs/scraper_manager.log`
- 趋势分析: `logs/trends_manager.log`
- Reddit 监控: `logs/reddit_manager.log`
- 报告生成: `logs/reports_manager.log`

### 常见问题排查
1. **爬虫失败**: 检查网站结构是否变化，更新选择器
2. **趋势分析错误**: 检查代理配置，Google Trends 可能需要代理
3. **通知失败**: 验证飞书 Webhook URL 是否正确
4. **数据库连接**: 确认 MySQL 服务运行正常

## 贡献指南 🤝

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 提交 Pull Request

## 许可证 📄

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式 📧

- 项目维护者: [Your Name]
- Email: <EMAIL>
- 项目地址: https://github.com/yourusername/game-monitor

---

**注意**: 本系统仅供学习和研究使用，请遵守相关网站的使用条款和爬虫协议。