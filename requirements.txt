# Core Flask dependencies
Flask>=2.3.0,<4.0.0
Flask-CORS>=4.0.0
Flask-SQLAlchemy>=3.0.0
Flask-Migrate>=4.0.0
Flask-APScheduler>=1.13.0
PyJWT>=2.8.0

# Web server
gunicorn>=21.0.0

# Database
pymysql>=1.1.0
cryptography>=41.0.0

# Cache and queuing
redis>=5.0.0
celery>=5.3.0

# Web scraping (Playwright removed for CentOS 8 compatibility)
beautifulsoup4>=4.12.0
aiohttp>=3.9.0
requests>=2.31.0
fake-useragent>=1.4.0
selenium>=4.0.0

# Data analysis
pytrends>=4.9.0
pandas>=2.0.0,<3.0.0
numpy>=1.24.0,<2.0.0
matplotlib>=3.7.0

# Reddit API
praw>=7.7.0
seaborn>=0.12.0

# Image processing
Pillow>=10.0.0

# Utilities
python-dotenv>=1.0.0
schedule>=1.2.0
APScheduler>=3.10.0
python-dateutil>=2.8.0
urllib3>=2.0.0,<3.0.0