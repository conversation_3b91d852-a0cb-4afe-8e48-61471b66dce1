"""
宝塔环境专用配置文件
使用方法：将此文件内容复制到 config.py 或创建 config_local.py
"""

import os
from datetime import timedelta

class Config:
    """宝塔 LNMP 环境配置"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'
    
    # 数据库配置（使用宝塔创建的数据库）
    MYSQL_HOST = '127.0.0.1'  # 本地数据库
    MYSQL_PORT = 3306
    MYSQL_USER = 'game_monitor'  # 在宝塔数据库管理中创建
    MYSQL_PASSWORD = 'your_db_password'  # 数据库密码
    MYSQL_DATABASE = 'game_monitor'  # 数据库名
    
    # SQLAlchemy 配置
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False
    
    # Redis 配置（如果在宝塔安装了 Redis）
    REDIS_HOST = '127.0.0.1'
    REDIS_PORT = 6379
    REDIS_PASSWORD = ''  # 如果设置了密码
    REDIS_DB = 0
    CACHE_REDIS_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
    
    # Flask 配置
    HOST = '127.0.0.1'  # 本地监听，通过 nginx 代理
    PORT = 8088  # Flask 端口
    FLASK_DEBUG = False  # 生产环境关闭调试
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    LOG_DIR = '/www/wwwroot/game-monitor/logs'
    
    # 文件上传（如需要）
    UPLOAD_FOLDER = '/www/wwwroot/game-monitor/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = True  # HTTPS 时启用
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 跨域配置（如果前端分离部署）
    CORS_ORIGINS = [
        'http://localhost:3000',  # 开发环境
        'https://your-domain.com'  # 生产域名
    ]
    
    # API 限流（可选）
    RATELIMIT_STORAGE_URL = CACHE_REDIS_URL if REDIS_HOST else 'memory://'
    RATELIMIT_DEFAULT = '100 per hour'
    
    # 爬虫配置
    SCRAPER_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    SCRAPER_TIMEOUT = 30
    SCRAPER_RETRY_TIMES = 3
    
    # Google Trends 配置
    TRENDS_LANGUAGE = 'zh-CN'
    TRENDS_TIMEZONE = 'Asia/Shanghai'
    SURGE_THRESHOLD = 50  # 增长率阈值
    
    # 代理配置（如需要）
    USE_PROXY = False
    PROXY_POOL_FILE = 'config/proxies.txt'
    
    # 通知配置
    FEISHU_WEBHOOK_URL = os.environ.get('FEISHU_WEBHOOK_URL', '')
    
    # 调度器配置
    SCHEDULER_SCRAPER_HOUR = 2  # 凌晨2点
    SCHEDULER_TRENDS_INTERVAL = 4  # 每4小时
    SCHEDULER_REDDIT_INTERVAL = 4  # 每4小时
    SCHEDULER_REPORT_HOUR = 9  # 早上9点
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    
    # 性能优化
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 静态文件缓存1年
    
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 确保日志目录存在
        os.makedirs(Config.LOG_DIR, exist_ok=True)
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)