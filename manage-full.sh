#!/bin/bash
# Game Monitor 全栈管理脚本（包含前端和后端）

VENV_PATH="./venv"
FRONTEND_PATH="./frontend"
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 检查前端是否已安装依赖
check_frontend_deps() {
    if [ ! -d "$FRONTEND_PATH/node_modules" ]; then
        echo -e "${YELLOW}检测到前端依赖未安装，正在安装...${NC}"
        cd $FRONTEND_PATH
        npm install
        cd ..
    fi
}

case "$1" in
    start)
        echo -e "${GREEN}启动 Game Monitor 全栈应用...${NC}"
        
        # 启动后端
        source $VENV_PATH/bin/activate
        export PYTHONPATH="$PWD:$PYTHONPATH"
        
        echo -e "${GREEN}启动后端服务...${NC}"
        nohup python app.py > logs/app.log 2>&1 &
        echo $! > app.pid
        
        # 启动5个调度器
        nohup python -m src.scheduler_scraper > logs/scraper.log 2>&1 &
        echo $! > scheduler_scraper.pid
        nohup python -m src.scheduler_trends > logs/trends.log 2>&1 &
        echo $! > scheduler_trends.pid
        nohup python -m src.scheduler_reddit > logs/reddit.log 2>&1 &
        echo $! > scheduler_reddit.pid
        nohup python -m src.scheduler_reports > logs/reports.log 2>&1 &
        echo $! > scheduler_reports.pid
        nohup python scripts/monitoring/scheduler_monitor.py > logs/monitor.log 2>&1 &
        echo $! > scheduler_monitor.pid
        
        # 启动前端
        echo -e "${GREEN}启动前端服务...${NC}"
        check_frontend_deps
        cd $FRONTEND_PATH
        nohup npm start > ../logs/frontend.log 2>&1 &
        echo $! > ../frontend.pid
        cd ..
        
        echo -e "${GREEN}全栈启动成功！${NC}"
        echo -e "${GREEN}后端地址: http://localhost:8088${NC}"
        echo -e "${GREEN}前端地址: http://localhost:3000${NC}"
        ;;
        
    stop)
        echo -e "${RED}停止 Game Monitor 全栈应用...${NC}"
        
        # 停止后端
        if [ -f app.pid ]; then
            kill $(cat app.pid) 2>/dev/null
            rm app.pid
        fi
        
        # 停止调度器
        for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports scheduler_monitor; do
            if [ -f ${scheduler}.pid ]; then
                kill $(cat ${scheduler}.pid) 2>/dev/null
                rm ${scheduler}.pid
            fi
        done
        
        # 停止前端
        if [ -f frontend.pid ]; then
            kill $(cat frontend.pid) 2>/dev/null
            rm frontend.pid
        fi
        
        echo -e "${GREEN}全栈停止成功！${NC}"
        ;;
        
    restart)
        $0 stop
        sleep 3
        $0 start
        ;;
        
    status)
        echo "检查服务状态..."
        
        # 后端状态
        if [ -f app.pid ] && ps -p $(cat app.pid) > /dev/null 2>&1; then
            echo -e "${GREEN}Flask 后端: 运行中 (http://localhost:8088)${NC}"
        else
            echo -e "${RED}Flask 后端: 已停止${NC}"
        fi
        
        # 调度器状态
        for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports scheduler_monitor; do
            if [ -f ${scheduler}.pid ] && ps -p $(cat ${scheduler}.pid) > /dev/null 2>&1; then
                echo -e "${GREEN}${scheduler}: 运行中${NC}"
            else
                echo -e "${RED}${scheduler}: 已停止${NC}"
            fi
        done
        
        # 前端状态
        if [ -f frontend.pid ] && ps -p $(cat frontend.pid) > /dev/null 2>&1; then
            echo -e "${GREEN}React 前端: 运行中 (http://localhost:3000)${NC}"
        else
            echo -e "${RED}React 前端: 已停止${NC}"
        fi
        ;;
        
    logs)
        case "$2" in
            frontend)
                tail -f logs/frontend.log
                ;;
            backend|app)
                tail -f logs/app.log
                ;;
            all)
                tail -f logs/app.log logs/frontend.log logs/scraper.log logs/trends.log logs/reddit.log logs/reports.log logs/monitor.log
                ;;
            *)
                tail -f logs/app.log logs/scraper.log logs/trends.log logs/reddit.log logs/reports.log logs/monitor.log
                ;;
        esac
        ;;
        
    install-deps)
        echo -e "${GREEN}安装所有依赖...${NC}"
        
        # 安装Python依赖
        echo -e "${GREEN}安装Python依赖...${NC}"
        source $VENV_PATH/bin/activate
        pip install -r requirements.txt
        
        # 安装前端依赖
        echo -e "${GREEN}安装前端依赖...${NC}"
        cd $FRONTEND_PATH
        npm install
        cd ..
        
        echo -e "${GREEN}依赖安装完成！${NC}"
        ;;
        
    build-frontend)
        echo -e "${GREEN}构建前端生产版本...${NC}"
        check_frontend_deps
        cd $FRONTEND_PATH
        npm run build
        cd ..
        echo -e "${GREEN}前端构建完成！${NC}"
        ;;
        
    dev)
        echo -e "${GREEN}启动开发模式（前后端分开运行）...${NC}"
        
        # 启动后端
        echo -e "${GREEN}在新终端启动后端...${NC}"
        gnome-terminal -- bash -c "source $VENV_PATH/bin/activate; export PYTHONPATH='$PWD:$PYTHONPATH'; python app.py; exec bash" 2>/dev/null || \
        xterm -e "source $VENV_PATH/bin/activate; export PYTHONPATH='$PWD:$PYTHONPATH'; python app.py; bash" 2>/dev/null || \
        echo -e "${YELLOW}请手动在新终端运行: source venv/bin/activate && python app.py${NC}"
        
        # 启动前端
        echo -e "${GREEN}在新终端启动前端...${NC}"
        gnome-terminal -- bash -c "cd $FRONTEND_PATH; npm start; exec bash" 2>/dev/null || \
        xterm -e "cd $FRONTEND_PATH; npm start; bash" 2>/dev/null || \
        echo -e "${YELLOW}请手动在新终端运行: cd frontend && npm start${NC}"
        
        echo -e "${GREEN}开发模式启动完成！${NC}"
        ;;
        
    *)
        echo "用法: $0 {start|stop|restart|status|logs|install-deps|build-frontend|dev}"
        echo ""
        echo "命令说明:"
        echo "  start         - 启动全栈应用（前端+后端）"
        echo "  stop          - 停止全栈应用"
        echo "  restart       - 重启全栈应用"
        echo "  status        - 查看服务状态"
        echo "  logs [type]   - 查看日志 (frontend/backend/all)"
        echo "  install-deps  - 安装所有依赖"
        echo "  build-frontend - 构建前端生产版本"
        echo "  dev           - 开发模式（在新终端分别启动）"
        exit 1
        ;;
esac