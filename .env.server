# 服务器直连配置文件
# Database Configuration - 直连宝塔MySQL
# 注意：只需要普通用户，不需要root密码
MYSQL_DATABASE=game_monitor
MYSQL_USER=game_monitor_user
MYSQL_PASSWORD=your_mysql_password
MYSQL_HOST=localhost
MYSQL_PORT=3306

# Redis Configuration - 如果服务器有Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secret-key-here-change-in-production
HOST=0.0.0.0
PORT=8088

# Google Trends Configuration
GOOGLE_TRENDS_GEO=CN
GOOGLE_TRENDS_TIMEFRAME=today 7-d
SURGE_THRESHOLD=50.0

# Feishu Bot Configuration (可选)
FEISHU_WEBHOOK_URL=
FEISHU_SECRET=
ENABLE_FEISHU_ALERTS=false

# Scraping Configuration
SCRAPING_DELAY_MIN=1
SCRAPING_DELAY_MAX=3
MAX_RETRIES=3
REQUEST_TIMEOUT=30
CONCURRENT_SCRAPERS=5
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Proxy Configuration (Optional)
USE_PROXY=false
PROXY_HTTP=
PROXY_HTTPS=

# Scheduling Configuration
DAILY_SCRAPE_TIME=02:00
TREND_CHECK_INTERVAL=4
DAILY_REPORT_TIME=09:00

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5