#!/bin/bash
# 检查调度器文件路径是否正确

echo "=== 检查调度器文件路径 ==="
echo

schedulers=(
    "src/scheduler_scraper.py"
    "src/scheduler_trends.py"
    "src/scheduler_reddit.py"
    "src/scheduler_reports.py"
    "scripts/monitoring/scheduler_monitor.py"
)

for scheduler in "${schedulers[@]}"; do
    if [ -f "$scheduler" ]; then
        echo "✓ $scheduler - 文件存在"
    else
        echo "✗ $scheduler - 文件不存在！"
    fi
done

echo
echo "=== 检查主要文件 ==="
main_files=(
    "app.py"
    "requirements.txt"
    "config.py"
    ".env.example"
)

for file in "${main_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file - 文件存在"
    else
        echo "✗ $file - 文件不存在！"
    fi
done