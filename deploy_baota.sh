#!/bin/bash
# Game Monitor 宝塔部署脚本
# 适用于宝塔 Linux 面板 LNMP 环境

# 配置变量
PROJECT_PATH="/www/wwwroot/game-monitor"
PYTHON_VERSION="3.9"
VENV_PATH="$PROJECT_PATH/venv"
API_PORT=8088
FRONTEND_PORT=3000

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}=== Game Monitor 宝塔部署脚本 ===${NC}"

# 检查是否在项目目录
if [ ! -f "requirements.txt" ]; then
    echo -e "${RED}错误：请在项目根目录运行此脚本${NC}"
    exit 1
fi

# 1. 安装 Python 依赖
echo -e "\n${YELLOW}1. 配置 Python 环境${NC}"
if [ ! -d "$VENV_PATH" ]; then
    echo "创建 Python 虚拟环境..."
    python$PYTHON_VERSION -m venv venv
fi

echo "激活虚拟环境并安装依赖..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# 2. 安装 Node.js（如果未安装）
echo -e "\n${YELLOW}2. 检查 Node.js 环境${NC}"
if ! command -v node &> /dev/null; then
    echo "Node.js 未安装，请在宝塔面板软件商店安装 Node.js 18.x"
    echo "或运行: yum install -y nodejs npm"
else
    echo "Node.js 已安装: $(node -v)"
fi

# 3. 构建前端
echo -e "\n${YELLOW}3. 构建 React 前端${NC}"
cd frontend
if [ ! -d "node_modules" ]; then
    echo "安装前端依赖..."
    npm install
fi
echo "构建生产版本..."
npm run build
cd ..

# 4. 创建 systemd 服务文件
echo -e "\n${YELLOW}4. 创建系统服务${NC}"
cat > game-monitor.service << EOF
[Unit]
Description=Game Monitor Flask Application
After=network.target mysql.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=$PROJECT_PATH
Environment="PATH=$VENV_PATH/bin"
Environment="PYTHONPATH=$PROJECT_PATH"
ExecStart=$VENV_PATH/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 5. 创建调度器服务（5个独立调度器）
# 创建前4个在 src/ 目录的调度器
for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports; do
cat > game-monitor-${scheduler}.service << EOF
[Unit]
Description=Game Monitor ${scheduler}
After=network.target mysql.service game-monitor.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=$PROJECT_PATH
Environment="PATH=$VENV_PATH/bin"
Environment="PYTHONPATH=$PROJECT_PATH"
ExecStart=$VENV_PATH/bin/python -m src.${scheduler}
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF
done

# 创建 monitor 调度器（在 scripts/monitoring/ 目录）
cat > game-monitor-scheduler_monitor.service << EOF
[Unit]
Description=Game Monitor scheduler_monitor
After=network.target mysql.service game-monitor.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=$PROJECT_PATH
Environment="PATH=$VENV_PATH/bin"
Environment="PYTHONPATH=$PROJECT_PATH"
ExecStart=$VENV_PATH/bin/python -m scripts.monitoring.scheduler_monitor
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target
EOF

# 6. 创建 nginx 配置
echo -e "\n${YELLOW}5. 创建 Nginx 配置${NC}"
cat > nginx_game_monitor.conf << 'EOF'
# Game Monitor Nginx 配置
# 请将此配置添加到宝塔网站配置中

# API 和旧版页面代理
location /api {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 超时设置
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
}

# 旧版 Flask 页面
location /classic {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

location /games {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

location /trends {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# 新版 React 静态文件（默认）
location / {
    root /www/wwwroot/game-monitor/frontend/build;
    try_files $uri /index.html;
    
    # 防止缓存 index.html
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
}

# 静态资源缓存
location /static {
    root /www/wwwroot/game-monitor/frontend/build;
    expires 1y;
    add_header Cache-Control "public, immutable";
}
EOF

# 7. 创建启动脚本
echo -e "\n${YELLOW}6. 创建管理脚本${NC}"
cat > manage.sh << 'EOF'
#!/bin/bash
# Game Monitor 管理脚本

VENV_PATH="./venv"
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

case "$1" in
    start)
        echo -e "${GREEN}启动 Game Monitor...${NC}"
        source $VENV_PATH/bin/activate
        nohup python app.py > logs/app.log 2>&1 &
        echo $! > app.pid
        # 启动5个调度器
        nohup python src/scheduler_scraper.py > logs/scraper.log 2>&1 &
        echo $! > scheduler_scraper.pid
        nohup python src/scheduler_trends.py > logs/trends.log 2>&1 &
        echo $! > scheduler_trends.pid
        nohup python src/scheduler_reddit.py > logs/reddit.log 2>&1 &
        echo $! > scheduler_reddit.pid
        nohup python src/scheduler_reports.py > logs/reports.log 2>&1 &
        echo $! > scheduler_reports.pid
        nohup python scripts/monitoring/scheduler_monitor.py > logs/monitor.log 2>&1 &
        echo $! > scheduler_monitor.pid
        echo -e "${GREEN}启动成功！${NC}"
        ;;
    stop)
        echo -e "${RED}停止 Game Monitor...${NC}"
        if [ -f app.pid ]; then
            kill $(cat app.pid)
            rm app.pid
        fi
        # 停止5个调度器
        for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports scheduler_monitor; do
            if [ -f ${scheduler}.pid ]; then
                kill $(cat ${scheduler}.pid)
                rm ${scheduler}.pid
            fi
        done
        echo -e "${GREEN}停止成功！${NC}"
        ;;
    restart)
        $0 stop
        sleep 2
        $0 start
        ;;
    status)
        echo "检查服务状态..."
        if [ -f app.pid ] && ps -p $(cat app.pid) > /dev/null; then
            echo -e "${GREEN}Flask 应用: 运行中${NC}"
        else
            echo -e "${RED}Flask 应用: 已停止${NC}"
        fi
        # 检查5个调度器状态
        for scheduler in scheduler_scraper scheduler_trends scheduler_reddit scheduler_reports scheduler_monitor; do
            if [ -f ${scheduler}.pid ] && ps -p $(cat ${scheduler}.pid) > /dev/null; then
                echo -e "${GREEN}${scheduler}: 运行中${NC}"
            else
                echo -e "${RED}${scheduler}: 已停止${NC}"
            fi
        done
        ;;
    logs)
        tail -f logs/app.log logs/scraper.log logs/trends.log logs/reddit.log logs/reports.log logs/monitor.log
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|logs}"
        exit 1
        ;;
esac
EOF

chmod +x manage.sh

# 8. 设置权限
echo -e "\n${YELLOW}7. 设置文件权限${NC}"
chown -R www:www .
chmod 755 logs
chmod 644 logs/*.log

# 9. 创建宝塔 Python 项目配置
echo -e "\n${YELLOW}8. 创建宝塔配置文件${NC}"
cat > bt_python_config.json << EOF
{
    "project_name": "game-monitor",
    "project_path": "$PROJECT_PATH",
    "python_version": "$PYTHON_VERSION",
    "port": $API_PORT,
    "startup_file": "app.py",
    "venv_path": "$VENV_PATH",
    "requirements": "requirements.txt",
    "proxy_config": {
        "location": "/",
        "proxy_pass": "http://127.0.0.1:$API_PORT"
    }
}
EOF

echo -e "\n${GREEN}=== 部署准备完成 ===${NC}"
echo -e "\n${YELLOW}请按以下步骤完成部署：${NC}"
echo "1. 在宝塔面板创建网站，设置域名"
echo "2. 在网站设置中添加 nginx_game_monitor.conf 的配置"
echo "3. 在宝塔 Python 项目管理器中导入项目"
echo "4. 使用 ./manage.sh start 启动服务"
echo "5. 或在宝塔进程守护管理器中添加："
echo "   - 名称: game-monitor-app"
echo "   - 启动命令: $VENV_PATH/bin/python $PROJECT_PATH/app.py"
echo "   - 各个调度器:"
echo "     src/scheduler_scraper.py - 爬虫调度器"
echo "     src/scheduler_trends.py - 趋势分析调度器"
echo "     src/scheduler_reddit.py - Reddit分析调度器"
echo "     src/scheduler_reports.py - 报告生成调度器"
echo "     scripts/monitoring/scheduler_monitor.py - 系统监控调度器"