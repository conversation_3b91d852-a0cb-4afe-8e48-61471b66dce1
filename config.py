import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Database Configuration
    MYSQL_HOST = os.getenv('MYSQL_HOST', 'localhost')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', 3306))
    MYSQL_USER = os.getenv('MYSQL_USER', 'game_monitor_user')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD', 'game_monitor_pass_2024')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE', 'game_monitor')
    
    # SQLAlchemy Configuration
    SQLALCHEMY_DATABASE_URI = f'mysql+pymysql://{MYSQL_USER}:{MYSQL_PASSWORD}@{MYSQL_HOST}:{MYSQL_PORT}/{MYSQL_DATABASE}?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 3600,
        'pool_timeout': 20,
        'max_overflow': 20
    }
    
    # Redis Configuration
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', 6379))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')
    REDIS_DB = int(os.getenv('REDIS_DB', 0))
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here-change-in-production')
    FLASK_ENV = os.getenv('FLASK_ENV', 'production')
    FLASK_DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    HOST = os.getenv('HOST', '0.0.0.0')
    PORT = int(os.getenv('PORT', 8088))
    
    # Google Trends Configuration
    GOOGLE_TRENDS_GEO = os.getenv('GOOGLE_TRENDS_GEO', '')
    GOOGLE_TRENDS_TIMEFRAME = os.getenv('GOOGLE_TRENDS_TIMEFRAME', 'today 1-m')
    SURGE_THRESHOLD = float(os.getenv('SURGE_THRESHOLD', 50.0))
    
    # Baseline Keywords for Relative Comparison
    BASELINE_KEYWORDS = os.getenv('BASELINE_KEYWORDS', 'minecraft,fortnite,game,mobile game,puzzle game').split(',')
    
    # Feishu Bot Configuration
    FEISHU_WEBHOOK_URL = os.getenv('FEISHU_WEBHOOK_URL', '')
    FEISHU_SECRET = os.getenv('FEISHU_SECRET', '')
    ENABLE_FEISHU_ALERTS = os.getenv('ENABLE_FEISHU_ALERTS', 'true').lower() == 'true'
    
    # Scraping Configuration
    SCRAPING_DELAY_MIN = float(os.getenv('SCRAPING_DELAY_MIN', 1))
    SCRAPING_DELAY_MAX = float(os.getenv('SCRAPING_DELAY_MAX', 3))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', 3))
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', 30))
    CONCURRENT_SCRAPERS = int(os.getenv('CONCURRENT_SCRAPERS', 5))
    USER_AGENT = os.getenv('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # Proxy Configuration
    USE_PROXY = os.getenv('USE_PROXY', 'false').lower() == 'true'
    PROXY_HTTP = os.getenv('PROXY_HTTP', '')
    PROXY_HTTPS = os.getenv('PROXY_HTTPS', '')
    PROXY_POOL_FILE = os.getenv('PROXY_POOL_FILE', 'proxy_pool.txt')
    PROXY_ROTATION_ENABLED = os.getenv('PROXY_ROTATION_ENABLED', 'false').lower() == 'true'
    
    # Webshare API Configuration
    WEBSHARE_API_TOKEN = os.getenv('WEBSHARE_API_TOKEN', '')
    WEBSHARE_ENABLED = os.getenv('WEBSHARE_ENABLED', 'false').lower() == 'true'
    WEBSHARE_REFRESH_INTERVAL = int(os.getenv('WEBSHARE_REFRESH_INTERVAL', 3600))  # 1 hour
    
    # Scheduling Configuration
    DAILY_SCRAPE_TIME = os.getenv('DAILY_SCRAPE_TIME', '02:00')
    TREND_CHECK_INTERVAL = int(os.getenv('TREND_CHECK_INTERVAL', 4))
    DAILY_REPORT_TIME = os.getenv('DAILY_REPORT_TIME', '09:00')
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE_MAX_SIZE = os.getenv('LOG_FILE_MAX_SIZE', '10MB')
    LOG_BACKUP_COUNT = int(os.getenv('LOG_BACKUP_COUNT', 5))
    
    # Additional Configuration
    JSON_AS_ASCII = False
    JSONIFY_PRETTYPRINT_REGULAR = True
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1 year for static files
    
    # Monitor Configuration
    MONITOR_CHECK_INTERVAL = int(os.getenv('MONITOR_CHECK_INTERVAL', 30))  # minutes
    MONITOR_REPORT_TIME = os.getenv('MONITOR_REPORT_TIME', '09:00')
    MONITOR_ALERT_THRESHOLD = float(os.getenv('MONITOR_ALERT_THRESHOLD', 0.8))  # 80% uptime threshold
    MONITOR_ENABLE_ALERTS = os.getenv('MONITOR_ENABLE_ALERTS', 'true').lower() == 'true'
    MONITOR_QUICK_CHECK_INTERVAL = int(os.getenv('MONITOR_QUICK_CHECK_INTERVAL', 60))  # minutes
    
    # Authentication Configuration
    AUTH_USERNAME = os.getenv('AUTH_USERNAME', 'Zacharyhu')
    AUTH_PASSWORD_HASH = os.getenv('AUTH_PASSWORD_HASH', '')  # SHA256 hash of password
    AUTH_SECRET_KEY = os.getenv('AUTH_SECRET_KEY', 'your-jwt-secret-key-change-in-production')
    AUTH_TOKEN_EXPIRY = int(os.getenv('AUTH_TOKEN_EXPIRY', 86400))  # 24 hours