# Next.js 前端认证配置示例
# 将此文件复制为 .env 并配置

# 认证配置
AUTH_USERNAME=Zacharyhu
AUTH_PASSWORD_HASH=85777f270ad7cf2a790981bbae3c4e484a1dc55e24a77390d692fbf1cffa12fa  # 默认密码: test123456
AUTH_SECRET_KEY=your-jwt-secret-key-change-in-production
AUTH_TOKEN_EXPIRY=86400  # 24小时

# 生成自定义密码的 hash:
# python3 -c "import hashlib; print(hashlib.sha256('你的密码'.encode()).hexdigest())"

# 其他必要的配置（从 .env.example 复制）
FLASK_ENV=production
FLASK_DEBUG=False
SECRET_KEY=your-secret-key-here-change-in-production
HOST=0.0.0.0
PORT=8088

# 数据库配置等...