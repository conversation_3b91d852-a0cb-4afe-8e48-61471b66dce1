# 服务器配置更新指南

本指南总结了从 commit 9d219d1 之后需要在服务器上更新的配置。

## 主要变更

### 1. 项目结构重组
- 调度器文件从根目录移至 `src/` 目录
- 监控调度器在 `scripts/monitoring/` 目录
- 前端项目新增在 `frontend/` 目录

### 2. 新增 React 前端
- 需要 Node.js 18+ 和 npm
- 前端默认端口 3000（开发）
- 生产环境通过 Nginx 提供静态文件

### 3. 新增 API 端点
- `/api/scheduler/logs` - 获取调度器实时状态
- `/api/trending-games` - 趋势游戏数据
- `/api/enhanced-trends` - 增强趋势分析

## 更新步骤

### 1. 安装 Node.js（如未安装）
```bash
# 宝塔面板软件商店安装 Node.js 18.x
# 或命令行安装
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

### 2. 更新项目代码
```bash
cd /www/wwwroot/game-monitor
git pull origin main
```

### 3. 更新 Python 依赖
```bash
source venv/bin/activate
pip install -r requirements.txt
```

### 4. 构建前端
```bash
cd frontend
npm install
npm run build
cd ..
```

### 5. 更新 Nginx 配置
在宝塔网站设置中添加以下配置：

```nginx
# API 代理
location /api {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
}

# 旧版页面（可选保留）
location ~ ^/(classic|games|trends|keyword) {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# React 路由支持（新增）
location / {
    root /www/wwwroot/game-monitor/frontend/build;
    try_files $uri /index.html;
}

# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 6. 更新进程守护配置

删除旧的 scheduler.py，添加 5 个新调度器：

1. **爬虫调度器**
   - 启动命令：`/www/wwwroot/game-monitor/venv/bin/python src/scheduler_scraper.py`

2. **趋势分析调度器**
   - 启动命令：`/www/wwwroot/game-monitor/venv/bin/python src/scheduler_trends.py`

3. **Reddit分析调度器**
   - 启动命令：`/www/wwwroot/game-monitor/venv/bin/python src/scheduler_reddit.py`

4. **报告生成调度器**
   - 启动命令：`/www/wwwroot/game-monitor/venv/bin/python src/scheduler_reports.py`

5. **系统监控调度器**
   - 启动命令：`/www/wwwroot/game-monitor/venv/bin/python scripts/monitoring/scheduler_monitor.py`

### 7. 重启服务
```bash
# 停止旧服务
./manage.sh stop

# 启动新服务
./manage.sh start

# 或使用宝塔进程守护管理器重启
```

## 端口配置

### 默认配置
- Flask API: 8088（内部端口）
- 前端访问：通过 Nginx 80/443

### 自定义端口
编辑 `.env` 文件：
```bash
PORT=9000  # 自定义 API 端口
```

记得同步更新 Nginx 配置中的 proxy_pass。

## 访问方式

### 新版 React 界面（推荐）
- https://your-domain.com
- 现代化界面，响应式设计
- 实时数据更新

### 旧版 Flask 界面（兼容）
- https://your-domain.com/classic
- https://your-domain.com/games
- https://your-domain.com/trends

## 验证部署

### 1. 检查服务状态
```bash
./manage.sh status
```

### 2. 检查 API
```bash
curl http://localhost:8088/api/stats
curl http://localhost:8088/api/scheduler/logs
```

### 3. 访问前端
浏览器访问 https://your-domain.com 应看到新的 React 界面。

## 注意事项

1. **文件权限**：确保 www 用户有权限访问所有文件
   ```bash
   chown -R www:www /www/wwwroot/game-monitor
   ```

2. **日志目录**：确保日志目录可写
   ```bash
   chmod 755 logs/
   ```

3. **环境变量**：检查 `.env` 文件配置是否正确

4. **防火墙**：确保必要端口已开放（通常只需 80/443）

## 回滚方案

如需回滚到旧版本：
```bash
git checkout 9d219d1
source venv/bin/activate
pip install -r requirements.txt
./manage.sh restart
```