module.exports = {
  apps: [
    {
      name: 'game-monitor-web',
      script: 'venv/bin/gunicorn',
      args: '--bind 0.0.0.0:8088 --workers 4 --timeout 120 app:app',
      cwd: '/www/wwwroot/game-monitor',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production'
      },
      log_file: 'logs/combined.log',
      out_file: 'logs/out.log',
      error_file: 'logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm Z'
    },
    {
      name: 'game-monitor-scheduler',
      script: 'venv/bin/python',
      args: 'scheduler.py',
      cwd: '/www/wwwroot/game-monitor',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production'
      },
      log_file: 'logs/scheduler-combined.log',
      out_file: 'logs/scheduler-out.log',
      error_file: 'logs/scheduler-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm Z'
    }
  ]
};