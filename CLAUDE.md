# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an HTML5 Game Monitor System that automatically scrapes 27 gaming websites, analyzes trends using enhanced Google Trends analysis with multi-baseline comparison algorithms, monitors Reddit community discussions, and sends notifications via Feishu bot. The system features a modern Next.js frontend (Next.js 14 + TypeScript + Tailwind CSS) with real-time data visualization, monitoring capabilities, and responsive design for both desktop and mobile devices.

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│          Next.js Full-Stack Application (3000)                  │
│         (Next.js 14 + TypeScript + Tailwind CSS)                │
│               Frontend + API Routes + Prisma ORM                 │
│         /game-monitor-nextjs/ - 完整的全栈应用                    │
├─────────────────────────────────────────────────────────────────┤
│                    Database (MySQL)                              │
│                         ↑                                        │
│                    直接写入数据                                    │
│                         ↑                                        │
├────────────────┬────────────────┬───────────────┬───────────────┤
│  Web Scraper   │ Reddit Analyzer│ Trends Analyzer│ Notification │
│  (27 sites)    │  (PRAW API)    │ (Google Trends)│  (Feishu)    │
│                   Python Scripts (数据采集层)                      │
└─────────────────────────────────────────────────────────────────┘
```

## Development Commands

### System Management
```bash
# Initialize environment and dependencies
./game-monitor init

# Start all services (checks ports, pulls images, starts containers)
./game-monitor start all

# Stop all services
./game-monitor stop

# Restart services
./game-monitor restart

# View live application logs
./game-monitor logs

# Check system status and container health
./game-monitor status

# Run comprehensive system tests
./game-monitor test system

# Check application health endpoint
./game-monitor health

# Update system (pull latest images, restart)
./game-monitor update

# Clean up old logs and Docker resources
./game-monitor cleanup
```

### Pre-deployment Safety
```bash
# Run pre-deployment checks (ports, resources, conflicts)
./pre-deploy-check.sh
```

### Docker Operations
```bash
# View container status
docker-compose ps

# View specific service logs
docker-compose logs -f app
docker-compose logs -f scheduler
docker-compose logs -f mysql

# Access database directly
docker-compose exec mysql mysql -u root -p

# Execute commands in app container
docker-compose exec app python -c "from app import app; print('App loaded')"

# Rebuild containers after code changes
docker-compose build --no-cache
docker-compose up -d
```

### Testing Individual Components
```bash
# Test database connectivity only
python -c "from test_system import test_database_connection; test_database_connection()"

# Test web scraper
python -c "import asyncio; from test_system import test_scraper; asyncio.run(test_scraper())"

# Test Google Trends API
python -c "from test_system import test_trends_analyzer; test_trends_analyzer()"

# Test Feishu notifications
python -c "from test_system import test_notifications; test_notifications()"

# Test enhanced trends analysis with multi-baseline comparison
python -c "from enhanced_trends_analyzer import run_enhanced_trends_analysis; run_enhanced_trends_analysis()"

# Test baseline analysis system
python test_baseline_analysis.py

# Test Webshare API integration
python -c "from webshare_proxy_manager import get_webshare_proxy_manager; manager = get_webshare_proxy_manager(); print(f'Proxy status: {manager.get_proxy_stats()}')"

# Test Reddit integration
python scripts/testing/test_reddit_integration.py
```

### Frontend Development (Next.js)
```bash
# 前端项目位于独立目录
cd game-monitor-nextjs

# 安装依赖
npm install

# 启动开发服务器
npm run dev  # 默认运行在 http://localhost:3000

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 运行测试
npm run test:e2e
```

## Architecture Overview

### Project Structure
```
game-monitor/                    # Python数据采集项目
├── app.py                      # Flask 服务器（已废弃，仅供迁移参考）
├── models.py                   # SQLAlchemy 模型（Python脚本仍在使用）
├── config.py                   # 配置管理
├── game-monitor               # 统一命令行工具
│
├── src/                       # 核心数据采集模块
│   ├── production_scraper.py     # 生产爬虫
│   ├── reddit_analyzer_sync.py   # Reddit 分析器
│   ├── enhanced_trends_analyzer.py # 增强趋势分析
│   ├── notifications.py          # 通知系统
│   ├── scheduler_*.py            # 各种调度器
│   └── webshare_proxy_manager.py # 代理管理
│
├── scripts/                   # 管理脚本
│   ├── setup/                # 安装部署
│   ├── testing/              # 测试脚本  
│   ├── monitoring/           # 监控管理
│   └── data/                 # 数据处理
│
├── docs/                     # 项目文档
├── sql/                      # 数据库脚本
└── logs/                     # 日志文件

game-monitor-nextjs/             # Next.js 全栈应用
├── app/                        # Next.js App Router
│   ├── (auth)/                # 认证相关页面
│   ├── (dashboard)/           # 仪表板页面
│   │   ├── dashboard/         # 概览页
│   │   ├── games/             # 游戏列表
│   │   ├── trends/            # 趋势分析（含Reddit）
│   │   └── monitor/           # 监控中心
│   └── api/                   # API 路由（所有API都在这里）
│       ├── games/             # 游戏数据API
│       ├── trends/            # 趋势数据API
│       ├── reddit/            # Reddit数据API
│       ├── stats/             # 统计数据API
│       └── surge-alerts/      # 警报数据API
├── components/                # React 组件
│   └── reddit/               # Reddit 相关组件
├── lib/                      # 工具库
│   └── prisma.ts            # Prisma 客户端
├── types/                    # TypeScript 类型定义
└── prisma/                   # Prisma ORM 配置
    └── schema.prisma        # 数据库模式定义
```

### Multi-Scheduler System
The application runs with 5 independent schedulers for high availability:

1. **scheduler_scraper** - Web scraping scheduler
   - Scrapes 27 gaming websites daily at 02:00
   - Logs: logs/scraper.log

2. **scheduler_reddit** - Reddit analysis scheduler
   - Analyzes game-related subreddits every 4 hours
   - Logs: logs/reddit_analyzer.log

3. **scheduler_trends** - Google Trends analysis scheduler
   - Performs trend analysis every 6 hours
   - Logs: logs/trends_analyzer.log

4. **scheduler_reports** - Report generation scheduler
   - Generates and sends daily reports at 09:00
   - Logs: logs/reports.log

5. **scheduler_monitor** - System monitoring scheduler
   - Monitors system health every 30 minutes
   - Logs: logs/monitor_manager.log

### Core Modules

**Next.js Application** (game-monitor-nextjs/)
- Full-stack application with server-side rendering
- API Routes handle all data requests using Prisma ORM
- Direct database access without intermediate API layer
- TypeScript for type safety throughout the application

**Python Data Collection Scripts** (game-monitor/src/)
- Run independently to collect and store data
- Write directly to MySQL database using SQLAlchemy
- No longer expose APIs - purely data collection focused

**scheduler_*.py** - Modular scheduler system
- Each scheduler runs independently to avoid coupling
- Uses `schedule` library for cron-like functionality
- Structured logging with [STATS] format for monitoring

**src/production_scraper.py** - Web scraping engine
- `SmartScraper` class uses Playwright for JavaScript-rendered sites
- `IntelligentParser` adapts to different website structures
- Handles 27 gaming websites with site-specific configurations

**src/reddit_analyzer_sync.py** - Reddit community analysis
- Synchronous version to avoid async warnings
- Analyzes game discussions across multiple subreddits
- Calculates heat scores based on posts, comments, and upvotes

**src/enhanced_trends_analyzer.py** - Advanced Google Trends analysis
- Multi-tier baseline keywords (130k, 50k, 25k, 10k, 5k search volumes)
- Comprehensive growth rate calculation
- Smart surge detection with confidence levels
- Integrates with Webshare proxy management

**src/notifications.py** - Communication system
- `FeishuBot` sends rich card notifications
- `send_text_message()` for simple text notifications
- `send_monitor_report()` for formatted monitoring reports
- Supports surge alerts and daily summary reports

**models.py** - Database schema using SQLAlchemy ORM
- `Game`, `Trend`, `Keyword`, `Alert`, `SiteConfig` models
- `RedditMetric` for Reddit analysis data
- Includes relationships, indexes, and utility methods

### Configuration Architecture

**config.py** - Centralized configuration management
- Loads from environment variables with sensible defaults
- Handles database, Redis, API keys, scheduling parameters
- Monitoring configuration for unified reporting

**.env** - Environment-specific configuration (not in git)
- Database credentials, API keys, webhook URLs
- Copy from `.env.example` and customize for deployment

**docker-compose.yml** - Service orchestration
- Defines container dependencies and networking
- Volume mounts for persistent data and logs
- Resource limits and health checks

### Data Flow

1. **Scheduled Scraping**: scheduler_scraper triggers daily scraping at 2 AM
2. **Website Processing**: Scraper visits 27 gaming sites, extracts game data
3. **Reddit Analysis**: scheduler_reddit analyzes community discussions every 4 hours
4. **Trend Analysis**: scheduler_trends queries Google Trends every 6 hours
5. **Data Storage**: All Python scripts write directly to MySQL database
6. **Surge Detection**: Growth rates above threshold trigger alerts
7. **Notifications**: Feishu bot sends formatted messages to configured channels
8. **Monitoring**: scheduler_monitor reports system health every 30 minutes
9. **Web Access**: Users access Next.js application which queries database directly
10. **Real-time Updates**: Next.js API routes serve fresh data from database

### Database Schema Relationships

- `games` (central table) → `trends` (one-to-many)
- `games` → `keywords` (one-to-many) 
- `games` → `alerts` (one-to-many)
- `games` → `reddit_metrics` (one-to-many)
- `site_configs` (standalone) stores scraping configurations

### Port and Network Configuration

- Next.js Application: Port 3000 (both development and production)
- MySQL Database: Port 3306 (default MySQL port)
- Python scripts connect directly to MySQL (no API layer)
- Internal services communicate via Docker network (when using Docker)
- Supports custom port mapping via environment variables
- No proxy configuration needed - Next.js connects directly to database

## Environment Setup

### Required Configuration
Before starting, copy `.env.example` to `.env` and configure:
- Database credentials (MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD)
- Flask secret key (SECRET_KEY)
- Feishu webhook URL for notifications
- Reddit API credentials (CLIENT_ID, CLIENT_SECRET)
- Optional: Webshare API key for proxy support

### Port Conflicts
If port 8088 is in use:
1. Check with `lsof -i :8088`
2. Modify PORT in `.env` file
3. Restart the application

### Resource Requirements
- Minimum 2GB RAM, 10GB disk space
- Python 3.8+ required
- MySQL 5.7+ and Redis 5.0+ (optional)

## Key Implementation Details

### Anti-Bot Scraping Strategy
- Playwright runs in stealth mode with random delays
- User-agent rotation and request header manipulation
- Intelligent CSS selector fallbacks for different site structures
- Error handling allows individual site failures without stopping entire process

### Reddit Analysis Features
- Monitors multiple gaming-related subreddits
- Calculates engagement metrics (posts, comments, upvotes)
- Identifies trending games based on discussion volume
- Stores historical data for trend analysis

### Trend Analysis Algorithm
- Compares recent period average vs baseline period average
- Multi-baseline comparison for accurate heat scores
- Configurable surge threshold (default 50% growth)
- Handles API rate limiting with exponential backoff

### Monitoring System
- Unified Feishu notifications for all schedulers
- Real-time status checking via process monitoring
- Log parsing for statistics extraction
- Daily consolidated reports with key metrics

### Database Performance
- Composite indexes on frequently queried columns
- Hash-based deduplication prevents duplicate game entries
- JSON columns store complex data (trend arrays, keyword lists)
- Regular cleanup tasks remove old data to manage growth

## Important Architecture Notes

### Simplified Full-Stack Architecture
1. **Next.js is the complete application** - Handles both frontend and API
2. **No Flask dependency** - All APIs are implemented in Next.js API routes
3. **Direct database access** - Prisma ORM connects directly to MySQL
4. **Python for data collection only** - Scripts run independently for scraping/analysis
5. **Single deployment** - One Next.js application to deploy and maintain

### Data Flow Architecture
```
Users → Next.js App (Port 3000) → MySQL Database
                                        ↑
                                   Python Scripts
                              (Data Collection Only)
```

### Migration Notes
- Flask API endpoints have been replaced with Next.js API routes
- All database queries now use Prisma instead of SQLAlchemy (for web app)
- Python scripts still use SQLAlchemy for data insertion
- No need to run Flask server anymore - only Next.js and Python schedulers

## Important Instructions

### When Adding New Features
1. Check existing modules in `src/` directory first
2. Follow the modular architecture pattern
3. Add appropriate logging with structured format
4. Update relevant tests in `scripts/testing/`
5. Document new configuration options

### When Fixing Issues
1. Check logs in `logs/` directory for error details
2. Run specific component tests before full system test
3. Ensure backward compatibility with existing data
4. Update documentation if behavior changes

### Code Style Guidelines
- Use descriptive variable names in English
- Add comments for complex logic (in Chinese is acceptable)
- Follow existing code patterns and conventions
- Keep functions focused and modular
- Handle errors gracefully with proper logging

### Testing Requirements
- Test new features with `./game-monitor test system`
- Ensure all 5 core tests pass (database, config, scraper, web, full flow)
- Add specific tests for new functionality
- Verify scheduler independence when modifying schedulers

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.