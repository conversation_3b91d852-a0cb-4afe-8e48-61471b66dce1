from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json
import hashlib

db = SQLAlchemy()

class Game(db.Model):
    __tablename__ = 'games'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False, comment='Game name')
    title = db.Column(db.String(255), comment='Game title')
    description = db.Column(db.Text, comment='Game description')
    thumbnail_url = db.Column(db.String(500), comment='Thumbnail URL')
    game_url = db.Column(db.String(500), comment='Game URL')
    embed_url = db.Column(db.String(500), comment='Embed iframe URL')
    source_site = db.Column(db.String(255), comment='Source website')
    source_url = db.Column(db.String(500), comment='Original scraping URL')
    genre = db.Column(db.String(100), comment='Game genre')
    tags = db.Column(db.Text, comment='Tags list')
    author = db.Column(db.String(255), comment='Game author/developer')
    player_count = db.Column(db.String(50), comment='Player count')
    rating = db.Column(db.Numeric(3, 2), comment='Rating')
    scrape_method = db.Column(db.String(20), comment='Scraping method used')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    hash_id = db.Column(db.String(64), unique=True, comment='Unique identifier')
    is_active = db.Column(db.Boolean, default=True, comment='Is active')
    
    # Relationships
    trends = db.relationship('Trend', backref='game', lazy='dynamic', cascade='all, delete-orphan')
    keywords = db.relationship('Keyword', backref='game', lazy='dynamic', cascade='all, delete-orphan')
    alerts = db.relationship('Alert', backref='game', lazy='dynamic', cascade='all, delete-orphan')
    reddit_metrics = db.relationship('RedditMetric', backref='game', lazy='dynamic', cascade='all, delete-orphan')
    
    # Indexes
    __table_args__ = (
        db.Index('idx_name', 'name'),
        db.Index('idx_source', 'source_site'),
        db.Index('idx_created', 'created_at'),
        db.Index('idx_hash', 'hash_id'),
        db.Index('idx_games_compound', 'source_site', 'created_at', 'is_active')
    )
    
    def __init__(self, **kwargs):
        super(Game, self).__init__(**kwargs)
        if not self.hash_id and self.name and self.game_url:
            self.generate_hash_id()
    
    def generate_hash_id(self):
        """Generate unique hash ID based on name and URL"""
        if self.name and self.game_url:
            content = f"{self.name}:{self.game_url}"
            self.hash_id = hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'title': self.title,
            'description': self.description,
            'thumbnail_url': self.thumbnail_url,
            'game_url': self.game_url,
            'embed_url': self.embed_url,
            'source_site': self.source_site,
            'source_url': self.source_url,
            'genre': self.genre,
            'tags': self.tags,
            'author': self.author,
            'player_count': self.player_count,
            'rating': float(self.rating) if self.rating else None,
            'scrape_method': self.scrape_method,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'hash_id': self.hash_id,
            'is_active': self.is_active
        }

class Trend(db.Model):
    __tablename__ = 'trends'
    
    id = db.Column(db.Integer, primary_key=True)
    keyword = db.Column(db.String(255), nullable=False, comment='Keyword')
    game_id = db.Column(db.Integer, db.ForeignKey('games.id', ondelete='CASCADE'), comment='Associated game ID')
    trend_value = db.Column(db.Integer, default=0, comment='Trend value')
    comparison_value = db.Column(db.Integer, default=0, comment='Comparison value')
    growth_rate = db.Column(db.Numeric(8, 2), default=0, comment='Growth rate %')
    trend_data = db.Column(db.JSON, comment='Complete trend data')
    date = db.Column(db.Date, comment='Data date')
    timeframe = db.Column(db.String(20), default='7d', comment='Time range')
    region = db.Column(db.String(10), default='global', comment='Region')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        db.Index('idx_keyword', 'keyword'),
        db.Index('idx_date', 'date'),
        db.Index('idx_growth', 'growth_rate'),
        db.Index('idx_game_date', 'game_id', 'date'),
        db.Index('idx_trends_compound', 'keyword', 'date', 'growth_rate')
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'keyword': self.keyword,
            'game_id': self.game_id,
            'trend_value': self.trend_value,
            'comparison_value': self.comparison_value,
            'growth_rate': float(self.growth_rate) if self.growth_rate else 0,
            'trend_data': self.trend_data,
            'date': self.date.isoformat() if self.date else None,
            'timeframe': self.timeframe,
            'region': self.region,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Keyword(db.Model):
    __tablename__ = 'keywords'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.Integer, db.ForeignKey('games.id', ondelete='CASCADE'), comment='Associated game ID')
    keyword = db.Column(db.String(255), nullable=False, comment='Main keyword')
    related_keywords = db.Column(db.JSON, comment='Related keywords')
    search_suggestions = db.Column(db.JSON, comment='Search suggestions')
    search_volume = db.Column(db.Integer, default=0, comment='Search volume')
    competition_level = db.Column(db.String(20), comment='Competition level')
    avg_cpc = db.Column(db.Numeric(6, 2), comment='Average CPC')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        db.Index('idx_game_id', 'game_id'),
        db.Index('idx_keyword', 'keyword'),
        db.Index('idx_volume', 'search_volume'),
        db.UniqueConstraint('game_id', 'keyword', name='unique_game_keyword')
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'game_id': self.game_id,
            'keyword': self.keyword,
            'related_keywords': self.related_keywords,
            'search_suggestions': self.search_suggestions,
            'search_volume': self.search_volume,
            'competition_level': self.competition_level,
            'avg_cpc': float(self.avg_cpc) if self.avg_cpc else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

class Alert(db.Model):
    __tablename__ = 'alerts'
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.Integer, db.ForeignKey('games.id', ondelete='CASCADE'), comment='Associated game ID')
    keyword = db.Column(db.String(255), comment='Keyword')
    alert_type = db.Column(db.String(50), default='surge', comment='Alert type')
    threshold_value = db.Column(db.Numeric(8, 2), default=50.0, comment='Threshold value')
    current_value = db.Column(db.Numeric(8, 2), default=0, comment='Current value')
    message = db.Column(db.Text, comment='Alert message')
    is_sent = db.Column(db.Boolean, default=False, comment='Is sent')
    sent_at = db.Column(db.DateTime, comment='Sent time')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        db.Index('idx_game_id', 'game_id'),
        db.Index('idx_sent', 'is_sent'),
        db.Index('idx_type', 'alert_type'),
        db.Index('idx_created', 'created_at'),
        db.Index('idx_alerts_compound', 'alert_type', 'is_sent', 'created_at')
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'game_id': self.game_id,
            'keyword': self.keyword,
            'alert_type': self.alert_type,
            'threshold_value': float(self.threshold_value) if self.threshold_value else 0,
            'current_value': float(self.current_value) if self.current_value else 0,
            'message': self.message,
            'is_sent': self.is_sent,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class SiteConfig(db.Model):
    __tablename__ = 'site_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    site_name = db.Column(db.String(255), nullable=False, comment='Site name')
    site_url = db.Column(db.String(500), nullable=False, unique=True, comment='Site URL')
    selectors = db.Column(db.JSON, comment='CSS selectors config')
    is_active = db.Column(db.Boolean, default=True, comment='Is active')
    last_scraped = db.Column(db.DateTime, comment='Last scraped time')
    scrape_count = db.Column(db.Integer, default=0, comment='Scrape count')
    error_count = db.Column(db.Integer, default=0, comment='Error count')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        db.Index('idx_active', 'is_active'),
        db.Index('idx_site_name', 'site_name')
    )
    
    def to_dict(self):
        return {
            'id': self.id,
            'site_name': self.site_name,
            'site_url': self.site_url,
            'selectors': self.selectors,
            'is_active': self.is_active,
            'last_scraped': self.last_scraped.isoformat() if self.last_scraped else None,
            'scrape_count': self.scrape_count,
            'error_count': self.error_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }


class RedditMetric(db.Model):
    """Reddit分析指标数据模型"""
    __tablename__ = "reddit_metrics"
    
    id = db.Column(db.Integer, primary_key=True)
    game_id = db.Column(db.Integer, db.ForeignKey("games.id"), nullable=False)
    heat_score = db.Column(db.Integer, default=0, comment="Heat score 0-100")
    growth_rate = db.Column(db.Float, default=0.0, comment="Growth rate percentage")
    post_count_24h = db.Column(db.Integer, default=0, comment="Post count in 24 hours")
    total_upvotes_24h = db.Column(db.Integer, default=0, comment="Total upvotes in 24 hours")
    total_comments_24h = db.Column(db.Integer, default=0, comment="Total comments in 24 hours")
    top_post_title = db.Column(db.Text, comment="Top post title")
    top_post_score = db.Column(db.Integer, default=0, comment="Top post score")
    top_post_url = db.Column(db.String(500), comment="Top post URL")
    avg_comments = db.Column(db.Float, default=0.0, comment="Average comments per post")
    unique_authors = db.Column(db.Integer, default=0, comment="Unique author count")
    sentiment = db.Column(db.String(20), default="neutral", comment="Sentiment: positive/neutral/negative")
    subreddit_distribution = db.Column(db.JSON, comment="Distribution across subreddits")
    raw_data = db.Column(db.JSON, comment="Raw Reddit data")
    status = db.Column(db.String(20), default="success", comment="Status: success/failed/no_data")
    error_message = db.Column(db.Text, comment="Error message if failed")
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Indexes
    __table_args__ = (
        db.Index("idx_reddit_game_created", "game_id", "created_at"),
        db.Index("idx_reddit_heat", "heat_score"),
        db.Index("idx_reddit_growth", "growth_rate")
    )
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "game_id": self.game_id,
            "heat_score": self.heat_score,
            "growth_rate": self.growth_rate,
            "post_count_24h": self.post_count_24h,
            "total_upvotes_24h": self.total_upvotes_24h,
            "total_comments_24h": self.total_comments_24h,
            "top_post_title": self.top_post_title,
            "top_post_score": self.top_post_score,
            "top_post_url": self.top_post_url,
            "avg_comments": self.avg_comments,
            "unique_authors": self.unique_authors,
            "sentiment": self.sentiment,
            "subreddit_distribution": self.subreddit_distribution,
            "status": self.status,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
