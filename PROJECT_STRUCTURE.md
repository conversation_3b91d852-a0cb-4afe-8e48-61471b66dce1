# 项目结构说明

## 目录结构

```
game-monitor/
├── app.py                  # Flask主应用入口
├── models.py               # 数据库模型定义
├── config.py               # 配置文件
├── requirements.txt        # Python依赖
├── game-monitor           # 统一命令行工具 (./game-monitor help)
│
├── src/                   # 核心模块
│   ├── api_enhanced.py    # 增强API接口
│   ├── notifications.py   # 通知系统
│   ├── production_scraper.py  # 生产爬虫
│   ├── scheduler_*.py     # 各种调度器模块
│   ├── *_analyzer.py      # 分析器模块
│   └── webshare_proxy_manager.py  # 代理管理
│
├── scripts/               # 脚本文件
│   ├── setup/            # 安装部署脚本
│   ├── testing/          # 测试脚本
│   ├── monitoring/       # 监控管理脚本
│   ├── data/             # 数据处理脚本
│   └── utils/            # 工具脚本
│
├── templates/            # 前端模板
├── static/               # 静态文件
├── logs/                 # 日志文件
├── data/                 # 数据文件
├── sql/                  # SQL脚本
└── docs/                 # 文档

```

## 快速使用

### 使用统一命令工具
```bash
# 查看帮助
./game-monitor help

# 启动所有服务
./game-monitor start all

# 查看状态
./game-monitor status

# 查看日志
./game-monitor logs scraper -n 100

# 运行测试
./game-monitor test system

# 健康检查
./game-monitor health
```

### 使用管理脚本
```bash
# 调度器管理
python scripts/monitoring/manage_schedulers.py status

# 部署向导
bash scripts/setup/deploy_schedulers.sh

# 运行测试
python scripts/testing/test_system.py
```

## 开发说明

1. **核心模块在 src/ 目录**：所有业务逻辑模块都在这里
2. **脚本在 scripts/ 目录**：各种管理、测试、数据处理脚本
3. **文档在 docs/ 目录**：所有文档都整理在这里
4. **主入口文件保留在根目录**：app.py, models.py, config.py 等核心文件

## 部署说明

参见 [docs/deployment.md](docs/deployment.md)