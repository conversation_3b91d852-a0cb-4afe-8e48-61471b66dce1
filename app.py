"""
[已废弃 - DEPRECATED]
此文件已不再使用。所有 Web 功能已迁移到 Next.js 应用。
请查看 game-monitor-nextjs/ 目录。

保留此文件仅供参考 Python 数据采集脚本的数据库模型。
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for
from flask_cors import CORS
from config import Config
from models import db, Game, Trend, Keyword, Alert, SiteConfig, RedditMetric
from datetime import datetime, timedelta
from sqlalchemy import text
import logging
import os
import redis
import json
import threading

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize extensions
    db.init_app(app)
    CORS(app)
    
    # Setup logging
    setup_logging(app)
    
    # Initialize Redis (optional)
    try:
        app.redis = redis.Redis(
            host=Config.REDIS_HOST,
            port=Config.REDIS_PORT,
            password=Config.REDIS_PASSWORD or None,
            db=Config.REDIS_DB,
            decode_responses=True,
            socket_connect_timeout=5
        )
        # Test Redis connection
        app.redis.ping()
        app.logger.info("Redis connected successfully")
    except Exception as e:
        app.logger.warning(f"Redis connection failed: {e}, continuing without Redis")
        app.redis = None
    
    # Make datetime available to templates
    @app.context_processor
    def inject_datetime():
        return {'datetime': datetime}
    
    # Register routes
    register_routes(app)
    
    # Register enhanced API blueprint
    from src.api_enhanced import enhanced_api
    app.register_blueprint(enhanced_api)
    
    # Create tables
    with app.app_context():
        db.create_all()
    
    return app

def setup_logging(app):
    """Setup application logging"""
    if not app.debug:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, Config.LOG_LEVEL),
            format='%(asctime)s %(levelname)s %(name)s %(message)s',
            handlers=[
                logging.FileHandler('logs/app.log'),
                logging.StreamHandler()
            ]
        )
        
        app.logger.setLevel(getattr(logging, Config.LOG_LEVEL))

def time_ago(date_time):
    """Calculate time difference and return human readable format"""
    if not date_time:
        return "Unknown"
    
    now = datetime.utcnow()
    diff = now - date_time
    
    seconds = int(diff.total_seconds())
    
    if seconds < 60:
        return f"{seconds} seconds ago"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes} minute{'s' if minutes != 1 else ''} ago"
    elif seconds < 86400:
        hours = seconds // 3600
        return f"{hours} hour{'s' if hours != 1 else ''} ago"
    elif seconds < 2592000:  # 30 days
        days = seconds // 86400
        return f"{days} day{'s' if days != 1 else ''} ago"
    elif seconds < 31536000:  # 365 days
        months = seconds // 2592000
        return f"{months} month{'s' if months != 1 else ''} ago"
    else:
        years = seconds // 31536000
        return f"{years} year{'s' if years != 1 else ''} ago"

def register_routes(app):
    """Register all application routes"""
    
    # Add time_ago function to template context
    app.jinja_env.globals.update(time_ago=time_ago)
    
    @app.route('/')
    def index():
        """Enhanced home page with trending analysis"""
        try:
            # Get statistics
            total_games = Game.query.filter_by(is_active=True).count()
            today = datetime.utcnow().date()
            today_games = Game.query.filter(
                Game.created_at >= today,
                Game.is_active == True
            ).count()
            
            # Get trending count
            trending_count = Trend.query\
                .filter(Trend.growth_rate > Config.SURGE_THRESHOLD)\
                .count()
            
            # Get surge count (explosive growth)
            surge_count = Trend.query\
                .filter(Trend.growth_rate > 100)\
                .count()
            
            stats = {
                'total_games': total_games,
                'today_games': today_games,
                'trending_count': trending_count,
                'surge_count': surge_count,
                'sites_count': SiteConfig.query.filter_by(is_active=True).count()
            }
            
            return render_template('index_bootstrap.html', stats=stats)
        except Exception as e:
            app.logger.error(f"Error in index route: {e}")
            return render_template('error.html', error="System error occurred"), 500
    
    @app.route('/spa')
    def spa():
        """New modern SPA frontend"""
        return render_template('index_new.html')
    
    @app.route('/classic')
    def classic_index():
        """Classic home page"""
        try:
            # Get statistics
            total_games = Game.query.filter_by(is_active=True).count()
            today = datetime.utcnow().date()
            today_games = Game.query.filter(
                Game.created_at >= today,
                Game.is_active == True
            ).count()
            
            # Get recent games
            recent_games = Game.query.filter_by(is_active=True)\
                .order_by(Game.created_at.desc())\
                .limit(12).all()
            
            # Get trending keywords
            trending_keywords = Trend.query\
                .filter(Trend.growth_rate > Config.SURGE_THRESHOLD)\
                .order_by(Trend.growth_rate.desc())\
                .limit(10).all()
            
            stats = {
                'total_games': total_games,
                'today_games': today_games,
                'trending_count': len(trending_keywords),
                'sites_count': SiteConfig.query.filter_by(is_active=True).count()
            }
            
            return render_template('index.html',
                                 stats=stats,
                                 recent_games=recent_games,
                                 trending_keywords=trending_keywords)
        except Exception as e:
            app.logger.error(f"Error in classic index route: {e}")
            return render_template('error.html', error="System error occurred"), 500
    
    @app.route('/games')
    def games():
        """Games listing page"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = 20
            search = request.args.get('search', '').strip()
            source = request.args.get('source', '').strip()
            
            query = Game.query.filter_by(is_active=True)
            
            if search:
                query = query.filter(
                    Game.name.contains(search) |
                    Game.description.contains(search) |
                    Game.tags.contains(search)
                )
            
            if source:
                query = query.filter(Game.source_site == source)
            
            games_pagination = query.order_by(Game.created_at.desc())\
                .paginate(page=page, per_page=per_page, error_out=False)
            
            sources = db.session.query(Game.source_site)\
                .filter_by(is_active=True)\
                .distinct().all()
            sources = [s[0] for s in sources]
            
            return render_template('games.html',
                                 games=games_pagination.items,
                                 pagination=games_pagination,
                                 sources=sources,
                                 current_search=search,
                                 current_source=source)
        except Exception as e:
            app.logger.error(f"Error in games route: {e}")
            return render_template('error.html', error="Error loading games"), 500
    
    @app.route('/enhanced-trends')
    def enhanced_trends():
        """Enhanced trends page with Reddit and Google data"""
        return render_template('enhanced_trends.html')
    
    @app.route('/trends')
    def trends():
        """Trends analysis page"""
        try:
            # Get trending keywords
            trending = Trend.query\
                .filter(Trend.growth_rate > 0)\
                .order_by(Trend.growth_rate.desc())\
                .limit(50).all()
            
            # Get surge alerts
            surges = Trend.query\
                .filter(Trend.growth_rate >= Config.SURGE_THRESHOLD)\
                .order_by(Trend.growth_rate.desc())\
                .limit(20).all()
            
            return render_template('trends.html',
                                 trending=trending,
                                 surges=surges,
                                 threshold=Config.SURGE_THRESHOLD)
        except Exception as e:
            app.logger.error(f"Error in trends route: {e}")
            return render_template('error.html', error="Error loading trends"), 500
    
    @app.route('/keyword/<keyword>')
    def keyword_detail(keyword):
        """Keyword detail page"""
        try:
            trends = Trend.query.filter_by(keyword=keyword)\
                .order_by(Trend.created_at.desc()).all()
            
            related_games = []
            if trends:
                game_ids = [t.game_id for t in trends if t.game_id]
                related_games = Game.query.filter(Game.id.in_(game_ids)).all()
            
            return render_template('keyword_detail.html',
                                 keyword=keyword,
                                 trends=trends,
                                 games=related_games)
        except Exception as e:
            app.logger.error(f"Error in keyword detail route: {e}")
            return render_template('error.html', error="Error loading keyword details"), 500
    
    # API Routes
    @app.route('/api/games')
    def api_games():
        """API endpoint for games"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            search = request.args.get('search', '').strip()
            source = request.args.get('source', '').strip()
            
            query = Game.query.filter_by(is_active=True)
            
            if search:
                query = query.filter(
                    Game.name.contains(search) |
                    Game.description.contains(search)
                )
            
            if source:
                query = query.filter(Game.source_site == source)
            
            games_pagination = query.order_by(Game.created_at.desc())\
                .paginate(page=page, per_page=per_page, error_out=False)
            
            return jsonify({
                'games': [game.to_dict() for game in games_pagination.items],
                'total': games_pagination.total,
                'pages': games_pagination.pages,
                'current_page': page,
                'per_page': per_page
            })
        except Exception as e:
            app.logger.error(f"Error in API games: {e}")
            return jsonify({'error': 'Failed to fetch games'}), 500
    
    @app.route('/api/trends/all')
    def api_trends():
        """API endpoint for all trends"""
        try:
            limit = request.args.get('limit', 50, type=int)
            min_growth = request.args.get('min_growth', 0, type=float)
            
            trends = Trend.query\
                .filter(Trend.growth_rate >= min_growth)\
                .order_by(Trend.growth_rate.desc())\
                .limit(limit).all()
            
            return jsonify({
                'trends': [trend.to_dict() for trend in trends]
            })
        except Exception as e:
            app.logger.error(f"Error in API trends: {e}")
            return jsonify({'error': 'Failed to fetch trends'}), 500
    
    @app.route('/api/stats')
    def api_stats():
        """API endpoint for statistics"""
        try:
            total_games = Game.query.filter_by(is_active=True).count()
            today = datetime.utcnow().date()
            today_games = Game.query.filter(
                Game.created_at >= today,
                Game.is_active == True
            ).count()
            
            trending_count = Trend.query\
                .filter(Trend.growth_rate > Config.SURGE_THRESHOLD).count()
            
            active_sites = SiteConfig.query.filter_by(is_active=True).count()
            
            return jsonify({
                'total_games': total_games,
                'today_games': today_games,
                'trending_count': trending_count,
                'active_sites': active_sites
            })
        except Exception as e:
            app.logger.error(f"Error in API stats: {e}")
            return jsonify({'error': 'Failed to fetch statistics'}), 500
    
    @app.route('/api/analyze-trends', methods=['POST'])
    def analyze_trends():
        """Start trend analysis for a specific game"""
        try:
            game_id = request.form.get('game_id') or request.json.get('game_id')
            
            if not game_id:
                return jsonify({'success': False, 'message': 'game_id is required'}), 400
            
            game = Game.query.get(game_id)
            if not game:
                return jsonify({'success': False, 'message': 'Game not found'}), 404
            
            # Start trend analysis in background thread
            def run_analysis():
                try:
                    from enhanced_trends_analyzer import EnhancedTrendsAnalyzer
                    analyzer = EnhancedTrendsAnalyzer()
                    results = analyzer.analyze_batch_games([game])
                    if results:
                        analyzer.save_enhanced_results(results)
                        app.logger.info(f"Trend analysis completed for game {game.name}")
                except Exception as e:
                    app.logger.error(f"Trend analysis failed for game {game.name}: {e}")
            
            thread = threading.Thread(target=run_analysis)
            thread.daemon = True
            thread.start()
            
            return jsonify({
                'success': True, 
                'message': f'Trend analysis started for {game.name}'
            })
            
        except Exception as e:
            app.logger.error(f"Error starting trend analysis: {e}")
            return jsonify({'success': False, 'message': 'Internal server error'}), 500
    
    @app.route('/api/trends')
    def get_trends():
        """Get trend data for games"""
        try:
            game_id = request.args.get('game_id')
            keyword = request.args.get('keyword')
            limit = int(request.args.get('limit', 50))
            
            query = Trend.query
            
            if game_id:
                query = query.filter(Trend.game_id == game_id)
            if keyword:
                query = query.filter(Trend.keyword.ilike(f'%{keyword}%'))
            
            trends = query.order_by(Trend.created_at.desc()).limit(limit).all()
            
            trends_data = []
            for trend in trends:
                trend_dict = trend.to_dict()
                if trend.game:
                    trend_dict['game'] = trend.game.to_dict()
                trends_data.append(trend_dict)
            
            return jsonify({
                'trends': trends_data,
                'total': len(trends_data)
            })
            
        except Exception as e:
            app.logger.error(f"Error fetching trends: {e}")
            return jsonify({'error': 'Failed to fetch trends'}), 500
    
    @app.route('/api/surge-alerts')
    def get_surge_alerts():
        """Get recent surge alerts"""
        try:
            limit = int(request.args.get('limit', 20))
            hours = int(request.args.get('hours', 24))
            
            since = datetime.utcnow() - timedelta(hours=hours)
            
            alerts = Alert.query.filter(
                Alert.created_at >= since,
                Alert.alert_type.in_(['surge', 'viral_explosion', 'strong_surge', 'moderate_surge'])
            ).order_by(Alert.current_value.desc()).limit(limit).all()
            
            alerts_data = []
            for alert in alerts:
                alert_dict = alert.to_dict()
                if alert.game:
                    alert_dict['game'] = alert.game.to_dict()
                alerts_data.append(alert_dict)
            
            return jsonify({
                'alerts': alerts_data,
                'total': len(alerts_data)
            })
            
        except Exception as e:
            app.logger.error(f"Error fetching surge alerts: {e}")
            return jsonify({'error': 'Failed to fetch surge alerts'}), 500
    
    @app.route('/api/trending-games')
    def get_trending_games():
        """Get games with enhanced trending analysis"""
        try:
            limit = int(request.args.get('limit', 24))
            days = int(request.args.get('days', 7))
            
            since = datetime.utcnow() - timedelta(days=days)
            
            # Get games with recent trends including detailed analysis
            trending_query = db.session.query(
                Game,
                Trend.growth_rate,
                Trend.trend_data,
                Trend.created_at.label('trend_date')
            ).join(Trend).filter(
                Trend.created_at >= since,
                Game.is_active == True
            ).order_by(Trend.growth_rate.desc()).limit(limit)
            
            trending_games = []
            for game, growth_rate, trend_data_json, trend_date in trending_query:
                game_dict = game.to_dict()
                game_dict['growth_rate'] = growth_rate or 0
                game_dict['trend_date'] = trend_date.isoformat()
                
                # Parse trend data JSON
                try:
                    if trend_data_json:
                        trend_data = json.loads(trend_data_json)
                        metrics = trend_data.get('metrics', {})
                        
                        # Add enhanced metrics
                        game_dict.update({
                            'heat_level': metrics.get('heat_level', 'moderate'),
                            'volume_level': metrics.get('volume_level', 'unknown'),
                            'trend_strength': metrics.get('trend_strength', 'stable'),
                            'relative_heat': metrics.get('relative_heat', 1.0),
                            'day_over_day': metrics.get('day_over_day', 0),
                            'volatility': metrics.get('volatility', 0),
                            'trend_data': metrics.get('trend_data', []),
                            'chart_data': trend_data.get('chart_data', {}),
                            'is_at_peak': metrics.get('is_at_peak', False)
                        })
                        
                        # Add surge patterns
                        surge_patterns = trend_data.get('surge_patterns', {})
                        game_dict.update({
                            'is_surge': surge_patterns.get('is_surge', False),
                            'surge_type': surge_patterns.get('surge_type', 'none'),
                            'surge_confidence': surge_patterns.get('surge_confidence', 0),
                            'alert_level': surge_patterns.get('alert_level', 'normal')
                        })
                        
                except json.JSONDecodeError:
                    # Fallback values if JSON parsing fails
                    game_dict.update({
                        'heat_level': 'moderate',
                        'volume_level': 'unknown', 
                        'trend_strength': 'stable',
                        'relative_heat': 1.0,
                        'is_surge': False,
                        'surge_type': 'none'
                    })
                
                trending_games.append(game_dict)
            
            return jsonify({
                'trending_games': trending_games,
                'total': len(trending_games),
                'timestamp': datetime.utcnow().isoformat()
            })
            
        except Exception as e:
            app.logger.error(f"Error fetching trending games: {e}")
            return jsonify({'error': 'Failed to fetch trending games'}), 500
    
    @app.route('/api/proxy-status')
    def get_proxy_status():
        """Get proxy system status"""
        try:
            status = {
                'proxy_enabled': Config.USE_PROXY,
                'webshare_enabled': Config.WEBSHARE_ENABLED,
                'local_proxy_file': Config.PROXY_POOL_FILE,
                'rotation_enabled': Config.PROXY_ROTATION_ENABLED
            }
            
            # Check Webshare status if enabled
            if Config.WEBSHARE_ENABLED:
                try:
                    from webshare_proxy_manager import get_webshare_proxy_manager
                    manager = get_webshare_proxy_manager()
                    
                    if manager.api_token:
                        webshare_stats = manager.get_proxy_stats()
                        status['webshare_stats'] = webshare_stats
                        status['webshare_configured'] = True
                    else:
                        status['webshare_configured'] = False
                        status['webshare_error'] = 'API token not configured'
                        
                except Exception as e:
                    status['webshare_error'] = str(e)
                    status['webshare_configured'] = False
            
            # Check local proxy pool if applicable
            if Config.USE_PROXY and not Config.WEBSHARE_ENABLED:
                try:
                    import os
                    if os.path.exists(Config.PROXY_POOL_FILE):
                        with open(Config.PROXY_POOL_FILE, 'r') as f:
                            lines = [line.strip() for line in f if line.strip() and not line.startswith('#')]
                            status['local_proxy_count'] = len(lines)
                    else:
                        status['local_proxy_count'] = 0
                        status['local_proxy_error'] = 'Proxy pool file not found'
                except Exception as e:
                    status['local_proxy_error'] = str(e)
            
            return jsonify(status)
            
        except Exception as e:
            app.logger.error(f"Error getting proxy status: {e}")
            return jsonify({'error': 'Failed to get proxy status'}), 500
    
    @app.route('/api/test-proxy', methods=['POST'])
    def test_proxy_connection():
        """Test proxy connection"""
        try:
            if not Config.USE_PROXY:
                return jsonify({'success': False, 'message': 'Proxy not enabled'}), 400
            
            if Config.WEBSHARE_ENABLED:
                from webshare_proxy_manager import get_webshare_proxy_manager
                manager = get_webshare_proxy_manager()
                
                proxy = manager.get_next_proxy()
                if proxy:
                    is_working, response_time = manager.test_proxy(proxy)
                    
                    return jsonify({
                        'success': is_working,
                        'response_time': response_time,
                        'proxy_info': proxy.get('proxy_info', {}),
                        'message': f"Proxy test {'successful' if is_working else 'failed'}"
                    })
                else:
                    return jsonify({
                        'success': False,
                        'message': 'No proxy available'
                    }), 500
            else:
                return jsonify({
                    'success': False,
                    'message': 'Local proxy testing not implemented'
                }), 501
                
        except Exception as e:
            app.logger.error(f"Error testing proxy: {e}")
            return jsonify({'success': False, 'message': str(e)}), 500

    @app.route('/api/auth/login', methods=['POST'])
    def api_login():
        """Login API endpoint"""
        try:
            from src.auth import verify_password, generate_token
            
            data = request.get_json()
            username = data.get('username', '').strip()
            password = data.get('password', '').strip()
            
            # 验证用户名
            if username != Config.AUTH_USERNAME:
                return jsonify({
                    'success': False,
                    'message': '用户名或密码错误'
                }), 401
            
            # 验证密码
            if not verify_password(password):
                return jsonify({
                    'success': False,
                    'message': '用户名或密码错误'
                }), 401
            
            # 生成 token
            token = generate_token(username)
            
            return jsonify({
                'success': True,
                'token': token,
                'username': username
            })
            
        except Exception as e:
            app.logger.error(f"Login error: {e}")
            return jsonify({
                'success': False,
                'message': '登录失败'
            }), 500

    @app.route('/api/scheduler/logs')
    def get_scheduler_logs():
        """Get scheduler logs and status"""
        import os
        import re
        from datetime import datetime
        
        logs_dir = 'logs'
        scheduler_info = {
            'scraper_manager': {
                'name': '爬虫调度器',
                'log_file': 'scraper_manager.log',
                'status': 'unknown',
                'last_run': None,
                'last_success': None,
                'last_error': None,
                'success_count': 0,
                'error_count': 0
            },
            'trends_manager': {
                'name': '趋势分析器',
                'log_file': 'trends_manager.log',
                'status': 'unknown',
                'last_run': None,
                'last_success': None,
                'last_error': None,
                'success_count': 0,
                'error_count': 0
            },
            'reddit_manager': {
                'name': 'Reddit分析器',
                'log_file': 'reddit_manager.log',
                'status': 'unknown',
                'last_run': None,
                'last_success': None,
                'last_error': None,
                'success_count': 0,
                'error_count': 0
            },
            'reports_manager': {
                'name': '报告生成器',
                'log_file': 'reports_manager.log',
                'status': 'unknown',
                'last_run': None,
                'last_success': None,
                'last_error': None,
                'success_count': 0,
                'error_count': 0
            }
        }
        
        # Read and parse log files
        for scheduler_key, info in scheduler_info.items():
            log_path = os.path.join(logs_dir, info['log_file'])
            if os.path.exists(log_path):
                try:
                    # Get file modification time
                    mtime = os.path.getmtime(log_path)
                    info['last_modified'] = datetime.fromtimestamp(mtime).isoformat()
                    
                    # Read last 100 lines
                    with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                        lines = f.readlines()[-100:]
                    
                    # Parse log lines
                    for line in lines:
                        # Extract timestamp
                        timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                        if timestamp_match:
                            timestamp = timestamp_match.group(1)
                            
                            # Check for different log patterns
                            if 'Started' in line or '启动' in line or 'Starting' in line:
                                info['last_run'] = timestamp
                                info['status'] = 'running'
                            elif 'Completed' in line or 'Success' in line or '成功' in line:
                                info['last_success'] = timestamp
                                info['success_count'] += 1
                                info['status'] = 'healthy'
                            elif 'ERROR' in line or 'Failed' in line or '失败' in line:
                                info['last_error'] = timestamp
                                info['error_count'] += 1
                                info['status'] = 'error'
                                # Extract error message
                                if 'last_error_msg' not in info:
                                    info['last_error_msg'] = line.strip()
                    
                    # Calculate success rate
                    total = info['success_count'] + info['error_count']
                    if total > 0:
                        info['success_rate'] = round((info['success_count'] / total) * 100, 1)
                    else:
                        info['success_rate'] = 100.0
                        
                except Exception as e:
                    info['error'] = str(e)
            else:
                info['status'] = 'not_found'
                info['error'] = 'Log file not found'
        
        return jsonify(scheduler_info)
    
    @app.route('/health')
    def health_check():
        """Health check endpoint"""
        try:
            # Check database connection
            db.session.execute(text('SELECT 1'))
            
            # Check Redis connection (optional)
            redis_status = 'not_configured'
            if app.redis:
                try:
                    app.redis.ping()
                    redis_status = 'healthy'
                except:
                    redis_status = 'unhealthy'
            
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.utcnow().isoformat(),
                'components': {
                    'database': 'healthy',
                    'redis': redis_status
                }
            })
        except Exception as e:
            app.logger.error(f"Health check failed: {e}")
            return jsonify({
                'status': 'unhealthy',
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            }), 500
    
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('error.html', error="Page not found"), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('error.html', error="Internal server error"), 500

# Create the application
app = create_app()

if __name__ == '__main__':
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.FLASK_DEBUG
    )