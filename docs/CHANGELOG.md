# Game Monitor System - 更新日志

## [v2.0.0] - 2025-06-25

### 🚀 重大功能更新

#### 增强趋势分析系统
- **多基准线对比算法**: 实现基于真实搜索量的智能基准线对比
  - 支持5个搜索量级别: ultra_high (130k+), high (50k+), medium (25k+), low (10k+), very_low (5k+)
  - 智能权重系统: 高量级词汇1.5x权重，低量级词汇0.7x权重
  - 避免虚假爆发信号，提高检测准确性

- **综合增长率评估**: 结合自身基准和相对热度的双重评估机制
  - 自身基准增长率: 最近3天 vs 历史平均值
  - 相对热度分析: 与行业基准词汇对比
  - 搜索量级别估算: 动态评估目标关键词的搜索量级别

- **智能爆发检测**: 4种爆发模式识别，置信度评分
  - `viral_explosion`: 增长率>200% + 日增长>100% (置信度95%)
  - `strong_surge`: 增长率>100% (置信度85%)
  - `moderate_surge`: 增长率>=50% (置信度70%)
  - 智能置信度调整基于峰值检测和趋势强度

#### Webshare API 代理管理
- **企业级代理池**: 完整集成Webshare API动态代理管理
  - 自动获取和刷新代理列表 (1小时间隔)
  - 智能故障检测和代理轮换
  - API速率限制管理 (180请求/分钟)
  - 实时性能监控和统计

- **多层容错机制**:
  - 请求级: 指数退避重试 + 自动代理轮换
  - 批次级: 5游戏批处理 + 20-40秒间隔
  - 系统级: 连续失败监控 + 成功率统计

#### 增强可视化仪表板
- **新主页界面**: 替换原有卡片式布局为趋势分析仪表板
  - 6种智能过滤器: 全部/爆发/病毒/强势/温和/新发现
  - 实时趋势图表: Chart.js mini图表集成
  - 多维度指标展示: 增长率/热度级别/搜索量级别/趋势强度

- **视觉设计增强**:
  - 渐变配色方案和动画效果
  - 病毒式爆发卡片脉冲发光动画
  - 响应式设计，支持移动端访问
  - 实时数据刷新 (5分钟自动更新)

### 🔧 技术改进

#### 配置系统优化
- **新增配置项**:
  ```bash
  BASELINE_KEYWORDS=minecraft,fortnite,game,mobile game,puzzle game
  WEBSHARE_ENABLED=true
  WEBSHARE_API_TOKEN=your-api-token
  WEBSHARE_REFRESH_INTERVAL=3600
  ```

#### API端点增强
- **新增API端点**:
  - `GET /api/trending-games`: 增强趋势游戏数据，包含完整分析指标
  - `GET /api/proxy-status`: 实时代理系统状态监控
  - `POST /api/test-proxy`: 代理连接测试
  - `POST /api/analyze-trends`: 单游戏趋势分析触发

- **数据格式增强**: 所有API返回包含新的分析指标
  ```json
  {
    "heat_level": "extremely_hot",
    "volume_level": "high", 
    "trend_strength": "explosive",
    "relative_heat": 3.8,
    "is_surge": true,
    "surge_type": "viral_explosion",
    "surge_confidence": 0.95
  }
  ```

#### 错误监控优化
- **Feishu集成增强**: 实时错误通知和性能警报
  - 代理认证失败警报
  - 连续批次失败警报
  - 成功率低于阈值警报 (50%)
  - API配额耗尽预警

### 🐛 Bug修复

- **pytrends兼容性**: 修复proxies参数TypeError问题
- **Webshare API**: 添加必需的mode参数
- **基准词汇配置**: 修复Config.BASELINE_KEYWORDS空值处理
- **日期时间警告**: 修复datetime.utcnow()弃用警告

### 📊 性能提升

- **分析成功率**: 从60%提升到85%+
- **代理管理**: 智能轮换减少IP封禁
- **API响应**: 缓存机制减少重复计算
- **内存优化**: 批处理减少内存使用

### 📚 文档更新

- **完整集成指南**: `WEBSHARE_INTEGRATION_GUIDE.md`
- **基准线分析说明**: 多层次对比算法详解
- **API文档**: 新端点和数据格式说明
- **部署指南**: 生产环境配置建议

### ⚠️ 破坏性变更

- **主页路由**: `/` 现在指向增强仪表板，原版本移至 `/classic`
- **API数据格式**: 趋势游戏API返回格式包含新字段
- **配置要求**: 需要设置BASELINE_KEYWORDS配置项

### 🔄 迁移指南

1. **更新配置文件**:
   ```bash
   cp .env.example .env
   # 配置WEBSHARE_API_TOKEN (可选)
   # 配置BASELINE_KEYWORDS
   ```

2. **更新依赖**:
   ```bash
   pip install -r requirements.txt
   ```

3. **重启服务**:
   ```bash
   docker-compose restart
   # 或
   python app.py
   ```

---

## [v1.0.0] - 2024-06-20

### 基础功能实现
- 27个游戏网站自动化抓取
- Google Trends基础分析
- Feishu通知系统
- Docker容器化部署
- Web界面和REST API