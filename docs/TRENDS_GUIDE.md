# 游戏趋势分析功能使用指南

## 🎯 功能概述

Game Monitor 现已支持强大的游戏趋势分析功能，基于 Google Trends 数据，可以实时监控游戏关键词的搜索热度变化，自动检测爆发增长的游戏。

## 🚀 核心功能

### 1. 自动趋势分析
- **Google Trends 集成**: 获取真实的搜索趋势数据
- **代理IP池支持**: 避免API限制，提高请求成功率
- **智能增长检测**: 自动识别爆发增长、强劲上升、温和增长等趋势
- **多维度指标**: 增长率、日环比、波动性、趋势强度等

### 2. 可视化界面
- **表格视图**: 游戏列表支持表格和卡片两种显示模式
- **趋势图表**: 使用 Chart.js 绘制专业的趋势图表
- **迷你图表**: 在游戏列表中显示简化的趋势线
- **实时状态**: 显示趋势数据获取状态和更新时间

### 3. 爆发警报
- **多级警报**: 病毒式爆发、强劲增长、温和增长等级别
- **置信度评估**: 基于多项指标计算爆发置信度
- **Feishu 通知**: 自动发送趋势警报到企业微信

## 📊 界面功能

### 游戏列表视图切换
- **卡片视图** (🔳): 传统的卡片布局，适合浏览
- **表格视图** (📊): 新增表格布局，显示详细趋势信息

### 趋势数据列
- **趋势更新**: 显示最后更新时间和状态
- **趋势数据**: 显示增长率和趋势强度
- **迷你图表**: 60x30像素的SVG趋势线
- **操作按钮**: 
  - 🎮 播放游戏
  - 📈 查看趋势图表
  - ℹ️ 查看详细信息

### 趋势徽章说明
- 🔥 **爆发增长** (红色): 增长率 > 50%
- 📈 **上升趋势** (橙色): 增长率 20-50%
- ➡️ **稳定增长** (绿色): 增长率 0-20%
- 📊 **平稳** (灰色): 增长率 <= 0%

## 🛠️ 配置说明

### 代理IP池配置

1. **编辑代理池文件**:
```bash
vim proxy_pool.txt
```

2. **添加代理地址**:
```
# HTTP代理格式
http://proxy1.example.com:8080
http://username:<EMAIL>:3128

# 确保代理支持HTTPS连接
https://secure-proxy.example.com:443
```

3. **启用代理轮换**:
```bash
# 在 .env 文件中配置
USE_PROXY=true
PROXY_ROTATION_ENABLED=true
PROXY_POOL_FILE=proxy_pool.txt
```

### Google Trends 配置

```bash
# 地理位置设置 (可选)
GOOGLE_TRENDS_GEO=US

# 时间范围设置
GOOGLE_TRENDS_TIMEFRAME=today 7-d

# 爆发阈值设置 (百分比)
SURGE_THRESHOLD=50.0
```

## 🔧 API 端点

### 1. 请求趋势分析
```bash
POST /api/analyze-trends
Content-Type: application/json

{
  "game_id": 123
}
```

### 2. 获取趋势数据
```bash
GET /api/trends?game_id=123
GET /api/trends?keyword=game_name&limit=50
```

### 3. 获取爆发警报
```bash
GET /api/surge-alerts?limit=20&hours=24
```

### 4. 获取热门游戏
```bash
GET /api/trending-games?limit=20&days=7
```

## 🚀 使用流程

### 1. 手动触发趋势分析
1. 在游戏列表中找到目标游戏
2. 如果显示"未获取趋势"，点击"获取趋势"按钮
3. 系统在后台开始分析，2-3分钟后刷新页面查看结果

### 2. 自动趋势分析
1. 系统每小时自动抓取新游戏
2. 每4小时自动分析最近50个游戏的趋势
3. 检测到爆发增长时自动发送警报

### 3. 查看趋势图表
1. 点击游戏操作栏的 📈 按钮
2. 弹出详细趋势分析窗口
3. 查看完整的趋势图表和关键指标

## 📈 趋势分析算法

### 增长率计算
```
增长率 = ((最近3天平均值 - 基线平均值) / 基线平均值) × 100%
```

### 爆发检测规则
- **病毒式爆发**: 增长率 > 200% 且日环比 > 100%
- **强劲增长**: 增长率 > 100%
- **温和增长**: 增长率 50-100%

### 置信度评估
- 基础置信度基于增长率
- 如果处于峰值期，置信度 +10%
- 如果趋势强度为"爆炸性"，置信度 +20%

## 🔍 故障排除

### 1. 趋势数据无法获取
**原因**: Google Trends API限制或网络问题
**解决**:
1. 检查网络连接
2. 配置代理IP池
3. 降低请求频率

### 2. 代理IP不可用
**原因**: 代理失效或配置错误
**解决**:
1. 测试代理连接性
2. 更新代理池文件
3. 检查代理格式

### 3. 图表显示异常
**原因**: 趋势数据格式错误
**解决**:
1. 检查浏览器控制台错误
2. 验证趋势数据JSON格式
3. 重新触发趋势分析

## 🎯 最佳实践

### 1. 代理IP管理
- 定期测试代理可用性
- 使用多个不同地区的代理
- 监控代理使用频率避免封禁

### 2. 趋势分析优化
- 合理设置爆发阈值 (推荐50%)
- 定期清理过期趋势数据
- 关注高质量游戏关键词

### 3. 性能优化
- 批量分析游戏以提高效率
- 使用缓存减少重复请求
- 控制并发分析数量

## 📞 技术支持

- **日志文件**: `/www/wwwroot/game-monitor/logs/`
- **配置文件**: `/www/wwwroot/game-monitor/.env`
- **趋势数据**: 存储在 `trends` 数据表中
- **调试模式**: 设置 `LOG_LEVEL=DEBUG` 获取详细日志

---

## 🎉 新功能预览

通过这套趋势分析系统，您可以：

1. **发现爆款游戏**: 第一时间识别快速增长的游戏
2. **市场趋势洞察**: 了解游戏行业的发展方向
3. **投资决策支持**: 基于数据做出游戏相关投资决策
4. **内容创作指导**: 为游戏内容创作者提供热点参考

立即体验新的趋势分析功能，抢占游戏市场先机！