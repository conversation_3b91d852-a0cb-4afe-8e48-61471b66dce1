[{"url": "https://itch.io/games/new-and-popular/platform-web", "domain": "itch.io", "timestamp": "2025-06-25T12:21:17.628457", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 89704, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "Dating Killmulator", "link": "https://coolom-games.itch.io/datingkillmulator"}, {"title": "Deltarune: Lightners Live Plus", "link": "https://ezioeagle.itch.io/lightners-live-plus"}, {"title": "Dungeon Raid", "link": "https://4cats.itch.io/dungeon-raid"}, {"title": "High Fantasy Idle Demo", "link": "https://saizonic.itch.io/hfi"}, {"title": "Chalk Gardens", "link": "https://red-owl-games.itch.io/chalk-gardens"}], "status": "success"}, {"url": "https://itch.io/games/new-and-popular/free", "domain": "itch.io", "timestamp": "2025-06-25T12:21:19.951484", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 89363, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "Necrofugitive Demo", "link": "https://blackgardenstudios.itch.io/necrofugitive-demo"}, {"title": "Dating Killmulator", "link": "https://coolom-games.itch.io/datingkillmulator"}, {"title": "Deltarune: Lightners Live Plus", "link": "https://ezioeagle.itch.io/lightners-live-plus"}, {"title": "Dungeon Raid", "link": "https://4cats.itch.io/dungeon-raid"}, {"title": "High Fantasy Idle Demo", "link": "https://saizonic.itch.io/hfi"}], "status": "success"}, {"url": "https://itch.io/games/new-and-popular/has-demo", "domain": "itch.io", "timestamp": "2025-06-25T12:21:21.513259", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 94242, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "Encounter HD (Oric)", "link": "https://defenceforce.itch.io/encounter"}, {"title": "A Tithe in Blood", "link": "https://vnstudioelan.itch.io/a-tithe-in-blood"}, {"title": "Pre-Odyssey: Love at First Quack [OTOME]", "link": "https://ant-san.itch.io/preodyssey"}, {"title": "Cozy Island Idle", "link": "https://overactiongames.itch.io/cozy-island-idle"}, {"title": "The Good People (<PERSON> <PERSON><PERSON><PERSON>)", "link": "https://moiraimyths.itch.io/tgp"}], "status": "success"}, {"url": "https://itch.io/games/new-and-popular", "domain": "itch.io", "timestamp": "2025-06-25T12:21:23.210059", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 89601, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "Necrofugitive Demo", "link": "https://blackgardenstudios.itch.io/necrofugitive-demo"}, {"title": "Dating Killmulator", "link": "https://coolom-games.itch.io/datingkillmulator"}, {"title": "Deltarune: Lightners Live Plus", "link": "https://ezioeagle.itch.io/lightners-live-plus"}, {"title": "Dungeon Raid", "link": "https://4cats.itch.io/dungeon-raid"}, {"title": "High Fantasy Idle Demo", "link": "https://saizonic.itch.io/hfi"}], "status": "success"}, {"url": "https://www.yiv.com/hot-games", "domain": "yiv.com", "timestamp": "2025-06-25T12:21:24.795024", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 62935, "error": null, "scraping_success": true, "containers_found": 2, "games_extracted": 2, "sample_games": [{"title": "Sprunki Music Scary Beat Box", "link": "/Sprunki-Music-Scary-Beat-Box"}, {"title": "Game Categories", "link": "/Boys-games"}], "status": "success"}, {"url": "https://y8.com/new/games", "domain": "y8.com", "timestamp": "2025-06-25T12:21:27.107756", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 583079, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://poki.com/", "domain": "poki.com", "timestamp": "2025-06-25T12:21:36.916892", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 208316, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://itch.io/games/has-demo", "domain": "itch.io", "timestamp": "2025-06-25T12:21:39.534064", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 95825, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "The Good People (<PERSON> <PERSON><PERSON><PERSON>)", "link": "https://moiraimyths.itch.io/tgp"}, {"title": "A Tithe in Blood", "link": "https://vnstudioelan.itch.io/a-tithe-in-blood"}, {"title": "My <PERSON>! Housemate", "link": "https://sourmiiiilk.itch.io/my-sweet-housemate"}, {"title": "Andromeda Six", "link": "https://wanderlust-games.itch.io/andromeda-six"}, {"title": "The Demon's Apple (FREE Demo)", "link": "https://kakera-art.itch.io/demons-apple"}], "status": "success"}, {"url": "https://www.yad.com/", "domain": "yad.com", "timestamp": "2025-06-25T12:21:41.096550", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 49051, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://www.yiv.com/", "domain": "yiv.com", "timestamp": "2025-06-25T12:21:43.689597", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 62014, "error": null, "scraping_success": true, "containers_found": 2, "games_extracted": 2, "sample_games": [{"title": "<PERSON><PERSON><PERSON><PERSON>", "link": "/Tralalelo-Mukbang-Asmr"}, {"title": "Game Categories", "link": "/Boys-games"}], "status": "success"}, {"url": "https://y8.com/popular/games", "domain": "y8.com", "timestamp": "2025-06-25T12:21:45.308058", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 643809, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://gamaverse.com/", "domain": "gamaverse.com", "timestamp": "2025-06-25T12:21:51.453900", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 148076, "error": null, "scraping_success": true, "containers_found": 9, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://itch.io/games", "domain": "itch.io", "timestamp": "2025-06-25T12:21:55.197811", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 88585, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "<PERSON><PERSON><PERSON>'s Library", "link": "https://danka-dobric.itch.io/brevskys-library"}, {"title": "Garbage Take Out Day", "link": "https://dankud-dev.itch.io/garbage-take-out-day"}, {"title": "The Freak Circus", "link": "https://garula.itch.io/the-freak-circus"}, {"title": "THE MIDNIGHT BUS", "link": "https://itandfeel.itch.io/the-midnight-bus"}, {"title": "BUNKER 73", "link": "https://bragekh.itch.io/bunker-73"}], "status": "success"}, {"url": "https://www.miniplay.com/trending-games", "domain": "miniplay.com", "timestamp": "2025-06-25T12:21:56.823011", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 224276, "error": null, "scraping_success": true, "containers_found": 143, "games_extracted": 2, "sample_games": [{"title": ">Super Mario World Online", "link": "https://www.miniplay.com/game/super-mario-world-online"}, {"title": ">Bloxd.io", "link": "https://www.miniplay.com/game/bloxd-io"}], "status": "success"}, {"url": "https://www.miniplay.com/daily-games", "domain": "miniplay.com", "timestamp": "2025-06-25T12:22:01.017344", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 231166, "error": null, "scraping_success": true, "containers_found": 143, "games_extracted": 2, "sample_games": [{"title": ">Google Snake", "link": "https://www.miniplay.com/game/google-snake"}, {"title": ">Geometry Dash", "link": "https://www.miniplay.com/game/geometry-dash"}], "status": "success"}, {"url": "https://www.crazygames.com/new", "domain": "crazygames.com", "timestamp": "2025-06-25T12:22:03.491827", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 216191, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://www.crazygames.com/hot", "domain": "crazygames.com", "timestamp": "2025-06-25T12:22:06.395979", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 171828, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://www.crazygames.com/updated", "domain": "crazygames.com", "timestamp": "2025-06-25T12:22:08.233842", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 214186, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://y8.com/", "domain": "y8.com", "timestamp": "2025-06-25T12:22:10.091145", "has_config": true, "config_type": "dynamic", "accessible": true, "status_code": 200, "content_length": 1002442, "error": null, "scraping_success": true, "containers_found": 0, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://www.funnygames.org/new-games.html", "domain": "funnygames.org", "timestamp": "2025-06-25T12:22:15.702544", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 422096, "error": null, "scraping_success": true, "containers_found": 1, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://www.1001games.com/new", "domain": "1001games.com", "timestamp": "2025-06-25T12:22:18.733210", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 232513, "error": null, "scraping_success": true, "containers_found": 140, "games_extracted": 5, "sample_games": [{"title": "Master Blender", "link": "No link"}, {"title": "HexAquatic Kraken", "link": "No link"}, {"title": "Solitaire Master", "link": "No link"}, {"title": "Jewel Dress Up", "link": "No link"}, {"title": "iColorcoin <PERSON>uzzle", "link": "No link"}], "status": "success"}, {"url": "https://www.sonsaur.com/", "domain": "sonsaur.com", "timestamp": "2025-06-25T12:22:21.774974", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 161294, "error": null, "scraping_success": true, "containers_found": 4, "games_extracted": 0, "sample_games": [], "status": "no_games"}, {"url": "https://www.play123.com/", "domain": "play123.com", "timestamp": "2025-06-25T12:22:24.315306", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 68993, "error": null, "scraping_success": true, "containers_found": 116, "games_extracted": 4, "sample_games": [{"title": "Brain Master Game for Genius", "link": "https://www.play123.com/game/brain-master-game-for-genius"}, {"title": "Explode the viruses and make them disappear from t", "link": "No link"}, {"title": "In this game, you need to slice obstacles ahead of", "link": "No link"}, {"title": "Pop, spin, and conquer the tower in Om Nom Tower 3", "link": "No link"}], "status": "success"}, {"url": "https://itch.io/games/newest/fresh", "domain": "itch.io", "timestamp": "2025-06-25T12:22:27.207517", "has_config": true, "config_type": "static", "accessible": true, "status_code": 200, "content_length": 90744, "error": null, "scraping_success": true, "containers_found": 36, "games_extracted": 5, "sample_games": [{"title": "Kabuto Park", "link": "https://doottinygames.itch.io/kabuto-park"}, {"title": "PossiblyAxolotl's PlayPack", "link": "https://possiblyaxolotl.itch.io/playpack"}, {"title": "Key Gunner", "link": "https://zad-man.itch.io/key-gunner"}, {"title": "Cold Nights", "link": "https://fizzlymike.itch.io/cold-nights"}, {"title": "Encounter HD (Oric)", "link": "https://defenceforce.itch.io/encounter"}], "status": "success"}, {"url": "https://kizi.com/", "domain": "kizi.com", "timestamp": "2025-06-25T12:22:28.788350", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 52984, "error": null, "scraping_success": true, "containers_found": 64, "games_extracted": 3, "sample_games": [{"title": "Recent Games", "link": "No link"}, {"title": "RECENTLY PLAYED GAMES", "link": "No link"}, {"title": "Hyper Nurse Hospital Games", "link": "https://kizi.com/games/hyper-nurse-hospital-games"}], "status": "success"}, {"url": "https://www.yiv.com/best-games", "domain": "yiv.com", "timestamp": "2025-06-25T12:22:31.971115", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 63005, "error": null, "scraping_success": true, "containers_found": 2, "games_extracted": 2, "sample_games": [{"title": "Avatar World", "link": "/Avatar-World"}, {"title": "Game Categories", "link": "/Boys-games"}], "status": "success"}, {"url": "https://www.pacogames.com/latest-games", "domain": "pacogames.com", "timestamp": "2025-06-25T12:22:34.288296", "has_config": true, "config_type": "static_domain_match", "accessible": true, "status_code": 200, "content_length": 44236, "error": null, "scraping_success": true, "containers_found": 2, "games_extracted": 0, "sample_games": [], "status": "no_games"}]