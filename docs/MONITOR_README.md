# 调度器监控系统使用指南

## 概述
调度器监控系统提供了统一的监控和报告功能，可以实时监控所有调度器的运行状态，并通过飞书机器人发送监控报告。

## 功能特性

### 1. 实时状态监控
- 监控所有调度器的运行状态（运行中/已停止）
- 监控进程资源使用（CPU、内存）
- 解析日志提取执行统计信息
- 异常自动告警

### 2. 定期监控报告
- 每日定时发送综合监控报告
- 包含调度器状态、业务数据统计、系统健康度
- 支持自定义报告时间

### 3. 结构化日志
所有调度器现在都会输出结构化日志，格式如下：
```
[STATS] key1=value1 key2=value2 ...
```

监控器会自动解析这些日志来提取统计信息。

## 使用方法

### 1. 启动监控器
```bash
# 使用管理脚本启动
python manage_schedulers.py start monitor

# 或者直接运行
python scheduler_monitor.py
```

### 2. 查看监控状态
```bash
# 查看所有调度器状态（包括监控器）
python manage_schedulers.py status

# 查看监控器日志
python manage_schedulers.py logs monitor
```

### 3. 测试监控功能
```bash
# 测试所有功能
python test_monitor.py --all

# 仅测试状态检查
python test_monitor.py --status

# 仅测试飞书通知
python test_monitor.py --notify

# 仅测试报告生成
python test_monitor.py --report
```

## 配置说明

在 `config.py` 或 `.env` 文件中可以配置：

```bash
# 监控检查间隔（分钟）
MONITOR_CHECK_INTERVAL=30

# 每日报告时间
MONITOR_REPORT_TIME=09:00

# 告警阈值（运行时间百分比）
MONITOR_ALERT_THRESHOLD=0.8

# 是否启用告警
MONITOR_ENABLE_ALERTS=true

# 快速检查间隔（分钟）
MONITOR_QUICK_CHECK_INTERVAL=60
```

## 监控报告内容

每日监控报告包含以下内容：

### 1. 调度器运行状态
- 每个调度器的运行状态（✅运行中/❌已停止）
- 进程信息（PID、CPU、内存使用）
- 执行统计（执行次数、错误数、最后执行时间）
- 关键业务指标

### 2. 业务数据统计
- 游戏总数和24小时新增数
- Reddit热度TOP5游戏
- Google Trends爆发游戏
- 其他业务指标

### 3. 系统健康度
- 整体健康指数（基于运行中的调度器比例）
- 各调度器24小时在线率
- 异常和告警汇总

## 监控工作流程

1. **定期检查**（每30分钟）
   - 检查所有调度器进程状态
   - 解析日志提取统计信息
   - 更新监控数据
   - 发送异常告警

2. **快速健康检查**（每小时）
   - 快速检查进程是否存活
   - 立即告警停止的调度器

3. **每日报告**（默认09:00）
   - 生成综合监控报告
   - 查询业务数据统计
   - 发送到飞书群组

## 故障处理

### 调度器停止告警
当监控器检测到调度器停止时，会立即发送飞书告警。收到告警后：

1. 查看具体哪个调度器停止
2. 检查对应的日志文件了解停止原因
3. 使用管理脚本重启调度器：
   ```bash
   python manage_schedulers.py restart <scheduler_name>
   ```

### 监控器本身故障
如果监控器停止工作：

1. 检查监控器日志：`logs/scheduler_monitor.log`
2. 手动重启监控器
3. 考虑使用 systemd 或 supervisor 管理监控器进程

## 最佳实践

1. **确保 venv 环境**：所有脚本都会自动使用项目的虚拟环境
2. **定期检查日志**：即使有监控，也要定期查看日志文件
3. **合理设置告警**：避免过于频繁的告警导致告警疲劳
4. **监控监控器**：考虑使用系统级工具（如 systemd）监控监控器本身