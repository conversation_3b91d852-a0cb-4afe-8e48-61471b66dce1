# HTML5游戏监控系统

一个自动化的HTML5游戏监控系统，通过抓取27个主流游戏网站数据，结合Google Trends分析，提供游戏趋势监控和暴涨关键词预警。

## 功能特性

- **自动游戏发现**：每日自动抓取27个主流游戏网站
- **趋势分析**：Google Trends集成，关键词趋势分析
- **暴涨预警**：实时监控趋势游戏，通过飞书机器人推送通知
- **可视化界面**：响应式Web界面，展示游戏列表和趋势图表
- **Docker部署**：一键式Docker Compose部署

## 快速开始

1. 克隆项目
2. 复制 `.env.example` 到 `.env` 并配置您的设置
3. 使用Docker Compose运行：
   ```bash
   docker-compose up -d
   ```
4. 访问Web界面：`http://localhost:8088`

## 系统架构

- **后端**：Python 3.11, Flask, Gunicorn
- **数据库**：MySQL 8.0, Redis
- **爬虫**：Playwright, BeautifulSoup4
- **分析**：Google Trends API (pytrends)
- **前端**：Bootstrap 5, Chart.js, jQuery
- **部署**：Docker, Nginx

## 监控网站列表

系统自动监控以下27个主流HTML5游戏网站：

### Itch.io系列
- https://itch.io/games/new-and-popular/platform-web
- https://itch.io/games/new-and-popular/free
- https://itch.io/games/new-and-popular/has-demo
- https://itch.io/games/new-and-popular
- https://itch.io/games/has-demo
- https://itch.io/games
- https://itch.io/games/newest/fresh

### 主流游戏平台
- https://poki.com/
- https://y8.com/new/games
- https://y8.com/popular/games
- https://y8.com/
- https://www.crazygames.com/new
- https://www.crazygames.com/hot
- https://www.crazygames.com/updated
- https://kizi.com/

### 其他游戏网站
- https://www.yiv.com/hot-games
- https://www.yiv.com/
- https://www.yiv.com/best-games
- https://www.yad.com/
- https://gamaverse.com/
- https://www.miniplay.com/trending-games
- https://www.miniplay.com/daily-games
- https://www.funnygames.org/new-games.html
- https://www.1001games.com/new
- https://www.sonsaur.com/
- https://www.play123.com/
- https://www.pacogames.com/latest-games

## 管理命令

使用提供的管理脚本：

```bash
# 启动系统
./start.sh start

# 停止系统
./start.sh stop

# 查看日志
./start.sh logs

# 运行测试
./start.sh test

# 检查状态
./start.sh status

# 创建备份
./start.sh backup

# 更新系统
./start.sh update
```

## 环境配置

编辑 `.env` 文件配置系统：

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_strong_root_password
MYSQL_DATABASE=game_monitor
MYSQL_USER=game_monitor_user
MYSQL_PASSWORD=your_strong_user_password

# 飞书机器人配置（可选）
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url
FEISHU_SECRET=your-feishu-secret
ENABLE_FEISHU_ALERTS=true

# Google Trends配置
GOOGLE_TRENDS_GEO=CN
SURGE_THRESHOLD=50.0

# 系统配置
SECRET_KEY=your-very-secret-key-here
LOG_LEVEL=INFO
```

## 核心功能

### 1. 数据抓取
- **智能解析**：自适应CSS选择器，支持多种网站结构
- **反反爬**：随机延时、User-Agent轮换、代理支持
- **容错机制**：单个网站失败不影响整体抓取
- **去重处理**：基于游戏名称+URL的MD5哈希去重

### 2. 趋势分析
- **Google Trends集成**：实时获取关键词搜索趋势
- **增长率计算**：智能算法计算关键词增长率
- **暴涨检测**：可配置阈值的暴涨预警
- **相关关键词**：自动挖掘相关搜索词

### 3. 通知系统
- **飞书机器人**：富文本卡片形式的暴涨预警
- **日报功能**：每日统计报告推送
- **多种通知类型**：暴涨预警、系统状态、错误通知

### 4. Web界面
- **响应式设计**：Bootstrap 5构建，支持移动端
- **实时图表**：Chart.js动态趋势图表
- **搜索筛选**：支持游戏名称、来源、标签搜索
- **分页浏览**：高效的数据分页展示

## 数据结构

### 游戏数据
- 游戏名称和描述
- 缩略图和游戏链接
- 来源网站和分类标签
- 创建时间和唯一标识

### 趋势数据
- 关键词和增长率
- 趋势值和对比基准
- 时间序列数据
- 地区和时间范围

## 性能指标

- **日均抓取**：1000+个游戏
- **API响应时间**：<500ms
- **抓取成功率**：>95%
- **系统可用性**：>99%

## 部署说明

详细部署说明请参考：
- [部署指南](docs/deployment_cn.md)
- [API文档](docs/api_cn.md)
- [Nginx配置](docs/nginx-config_cn.md)

## 许可证

MIT License

## 技术支持

如遇问题，请：
1. 首先检查日志：`./start.sh logs`
2. 运行系统测试：`./start.sh test`
3. 查看健康状态：`http://localhost:8088/health`
4. 参考文档和配置指南