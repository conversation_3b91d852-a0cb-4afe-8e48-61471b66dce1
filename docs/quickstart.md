# 快速开始指南

本指南帮助你在 10 分钟内启动 Game Monitor 系统。

## 前提条件

确保你的系统已安装：
- Git
- Python 3.8+
- MySQL 5.7+
- 虚拟环境工具 (venv)

## 5 分钟快速启动

### 1. 克隆并进入项目

```bash
git clone https://github.com/yourusername/game-monitor.git
cd game-monitor
```

### 2. 使用自动部署脚本

最简单的方式是使用我们的部署向导：

```bash
bash scripts/setup/deploy_schedulers.sh
```

向导会自动：
- ✅ 检查系统环境
- ✅ 创建虚拟环境
- ✅ 安装所有依赖
- ✅ 配置数据库
- ✅ 启动所有服务

### 3. 访问系统

打开浏览器访问：http://localhost:8088

你应该能看到：
- 🎮 游戏列表页面
- 📊 趋势分析页面
- 📈 增强趋势页面
- 🔍 系统监控页面

## 手动快速启动

如果自动脚本失败，可以手动启动：

### 1. 创建配置文件

```bash
cp .env.example .env
```

编辑 `.env` 文件，填入数据库信息：
```env
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=game_monitor
```

### 2. 初始化环境

```bash
# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
playwright install chromium
```

### 3. 初始化数据库

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS game_monitor CHARACTER SET utf8mb4;"

# 导入表结构
mysql -u root -p game_monitor < sql/init.sql
```

### 4. 启动服务

```bash
# 启动 Web 服务
python app.py &

# 启动所有调度器
python scripts/monitoring/manage_schedulers.py start all
```

## 使用统一命令工具

Game Monitor 提供了统一的命令行工具：

```bash
# 查看所有命令
./game-monitor help

# 初始化系统
./game-monitor init

# 启动所有服务
./game-monitor start all

# 查看状态
./game-monitor status

# 查看日志
./game-monitor logs scraper

# 运行测试
./game-monitor test system
```

## 验证安装

### 1. 检查 Web 服务
访问 http://localhost:8088，应该能看到主页

### 2. 检查 API
```bash
curl http://localhost:8088/api/stats
```

### 3. 检查调度器
```bash
python scripts/monitoring/manage_schedulers.py status
```

应该看到所有 5 个调度器都在运行：
- ✅ scheduler_scraper (爬虫)
- ✅ scheduler_reddit (Reddit分析)
- ✅ scheduler_trends (趋势分析)
- ✅ scheduler_reports (报告生成)
- ✅ scheduler_monitor (系统监控)

## 配置飞书通知（可选）

如果你想接收系统通知：

1. 在飞书中创建机器人
2. 获取 Webhook URL
3. 编辑 `.env` 文件：
```env
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx
ENABLE_FEISHU_ALERTS=true
```

## 配置 Reddit 分析（可选）

1. 在 Reddit 创建应用：https://www.reddit.com/prefs/apps
2. 获取 Client ID 和 Secret
3. 编辑 `.env` 文件：
```env
REDDIT_CLIENT_ID=your_client_id
REDDIT_CLIENT_SECRET=your_client_secret
REDDIT_USER_AGENT=game-monitor/1.0
```

## 常见问题

### 端口被占用
```bash
# 检查端口
lsof -i :8088

# 修改端口
# 编辑 .env 文件，修改 PORT=8089
```

### 数据库连接失败
```bash
# 检查 MySQL 服务
systemctl status mysql

# 确认用户权限
mysql -u game_monitor -p
```

### 依赖安装失败
```bash
# 升级 pip
pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 下一步

1. **查看完整文档**：阅读 [README.md](../README.md) 了解所有功能
2. **配置高级功能**：查看 [部署文档](deployment.md) 了解生产环境配置
3. **运行测试**：执行 `./game-monitor test system` 验证所有功能
4. **监控系统**：访问 http://localhost:8088/monitor 查看实时状态

## 获取帮助

- 查看日志：`./game-monitor logs all`
- 提交 Issue：https://github.com/yourusername/game-monitor/issues
- 查看文档：`docs/` 目录

恭喜！你的 Game Monitor 系统已经运行起来了 🎉