# Server Status Testing Guide

This guide provides comprehensive testing procedures for verifying the Game Monitor System's health and functionality on production servers.

## Quick Status Check

```bash
# Check all schedulers status
python manage_schedulers.py status

# Check system health endpoint
curl http://localhost:8088/health

# View recent logs
tail -f logs/*_manager.log
```

## Complete System Test Suite

### 1. Pre-deployment Verification

```bash
# Run pre-deployment checks
./pre-deploy-check.sh

# This script verifies:
# - Port 8088 availability
# - MySQL connectivity
# - Redis connectivity
# - Python dependencies
# - Disk space (minimum 1GB)
# - Memory availability (minimum 512MB)
```

### 2. Component Testing

#### Database Connectivity
```bash
# Test MySQL connection
python -c "from test_system import test_database_connection; test_database_connection()"

# Expected output:
# ✓ Database connection successful
# ✓ Tables exist: games, trends, keywords, alerts, reddit_metrics
```

#### Redis Cache
```bash
# Test Redis connection
python -c "
from app import create_app
app = create_app()
with app.app_context():
    from extensions import redis_client
    print('Redis ping:', redis_client.ping() if redis_client else 'No Redis')
"
```

#### Web Scraper
```bash
# Test scraper with a single site
python -c "
import asyncio
from test_system import test_scraper
asyncio.run(test_scraper())
"
```

#### Reddit Integration
```bash
# Test Reddit API
python test_reddit_integration.py

# Check Reddit analyzer
python -c "
from reddit_analyzer_sync import RedditAnalyzerSync
analyzer = RedditAnalyzerSync()
print('Reddit API initialized successfully')
"
```

#### Google Trends
```bash
# Test trends analyzer
python -c "from test_system import test_trends_analyzer; test_trends_analyzer()"
```

### 3. Scheduler Health Checks

#### Individual Scheduler Status
```bash
# Check each scheduler
for scheduler in scraper reddit trends reports monitor; do
    echo "=== Checking $scheduler ==="
    python manage_schedulers.py logs $scheduler -n 5
    echo ""
done
```

#### Monitor System Test
```bash
# Test monitoring functions
python test_monitor.py --all

# This tests:
# - Status checking
# - Alert notifications
# - Report generation
```

### 4. API Endpoint Testing

```bash
# Test main endpoints
echo "=== Testing API endpoints ==="

# Health check
curl -s http://localhost:8088/health | python -m json.tool

# Game list
curl -s http://localhost:8088/api/games?limit=5 | python -m json.tool

# Statistics
curl -s http://localhost:8088/api/stats | python -m json.tool

# Enhanced trends
curl -s http://localhost:8088/api/enhanced/trends/unified | python -m json.tool
```

### 5. Performance Metrics

```bash
# Check resource usage
python -c "
import psutil
import os

print('=== System Resources ===')
print(f'CPU Usage: {psutil.cpu_percent(interval=1)}%')
print(f'Memory Usage: {psutil.virtual_memory().percent}%')
print(f'Disk Usage: {psutil.disk_usage("/").percent}%')

print('\n=== Process Status ===')
for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
    if 'scheduler' in proc.info['name'] or 'python' in proc.info['name']:
        print(f'PID {proc.info["pid"]}: {proc.info["name"]} - CPU: {proc.info["cpu_percent"]}%, Memory: {proc.info["memory_percent"]:.1f}%')
"
```

### 6. Log Analysis

```bash
# Check for errors in recent logs
echo "=== Checking for recent errors ==="
grep -i error logs/*_manager.log | tail -20

# Check execution statistics
echo "=== Recent execution stats ==="
grep "\[STATS\]" logs/*_manager.log | tail -20
```

### 7. Data Verification

```bash
# Check recent data collection
python -c "
from app import create_app
from models import db, Game, RedditMetric, Trend
from datetime import datetime, timedelta

app = create_app()
with app.app_context():
    # Games added in last 24h
    recent_games = Game.query.filter(
        Game.created_at >= datetime.utcnow() - timedelta(days=1)
    ).count()
    
    # Reddit metrics in last 24h
    recent_reddit = RedditMetric.query.filter(
        RedditMetric.created_at >= datetime.utcnow() - timedelta(days=1)
    ).count()
    
    # Trends in last 24h
    recent_trends = Trend.query.filter(
        Trend.created_at >= datetime.utcnow() - timedelta(days=1)
    ).count()
    
    print(f'Last 24h stats:')
    print(f'  New games: {recent_games}')
    print(f'  Reddit analyses: {recent_reddit}')
    print(f'  Trend analyses: {recent_trends}')
"
```

## Automated Health Check Script

Create `server_health_check.sh`:

```bash
#!/bin/bash

echo "=== Game Monitor System Health Check ==="
echo "Time: $(date)"
echo ""

# Check schedulers
echo "1. Scheduler Status:"
python manage_schedulers.py status

# Check web server
echo -e "\n2. Web Server:"
if curl -s http://localhost:8088/health > /dev/null; then
    echo "✓ Web server is responding"
else
    echo "✗ Web server is not responding"
fi

# Check database
echo -e "\n3. Database:"
mysql -u game_monitor -p$MYSQL_PASSWORD -e "SELECT COUNT(*) as total_games FROM game_monitor.games;" 2>/dev/null && echo "✓ Database accessible" || echo "✗ Database error"

# Check recent activity
echo -e "\n4. Recent Activity:"
tail -1 logs/scraper_manager.log | grep -q "$(date +%Y-%m-%d)" && echo "✓ Scraper active today" || echo "⚠ No scraper activity today"
tail -1 logs/reddit_manager.log | grep -q "$(date +%Y-%m-%d)" && echo "✓ Reddit analyzer active today" || echo "⚠ No Reddit activity today"

# Check disk space
echo -e "\n5. Disk Space:"
df -h / | tail -1 | awk '{print "Used: "$3" / "$2" ("$5")"}'

echo -e "\n=== Health Check Complete ==="
```

## Troubleshooting Commands

### Restart Failed Scheduler
```bash
# Identify failed scheduler
python manage_schedulers.py status

# Restart specific scheduler
python manage_schedulers.py restart <scheduler_name>

# Or restart all
python manage_schedulers.py restart all
```

### Clear Cache
```bash
# Clear Redis cache
redis-cli FLUSHDB

# Clear Python cache
find . -type d -name __pycache__ -exec rm -rf {} +
```

### Database Maintenance
```bash
# Check table sizes
mysql -u root -p -e "
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'game_monitor'
ORDER BY (data_length + index_length) DESC;
"

# Optimize tables
mysql -u root -p game_monitor -e "OPTIMIZE TABLE games, trends, reddit_metrics;"
```

### Emergency Stop
```bash
# Stop all schedulers
./stop_all_schedulers.sh

# Kill all Python processes (careful!)
pkill -f scheduler_

# Stop Docker containers (if using Docker)
docker-compose down
```

## Monitoring Best Practices

1. **Regular Checks**: Run health check script daily
2. **Log Rotation**: Set up logrotate for `logs/*.log` files
3. **Disk Space**: Monitor `/` and `/var/lib/mysql` usage
4. **Backup**: Regular MySQL dumps of game_monitor database
5. **Alerts**: Configure system alerts for disk/memory thresholds

## Expected Metrics

Normal operation should show:
- All 5 schedulers running
- <5% CPU usage per scheduler
- <200MB memory per scheduler
- New games discovered daily
- Reddit analyses every 2 hours
- No ERROR entries in recent logs