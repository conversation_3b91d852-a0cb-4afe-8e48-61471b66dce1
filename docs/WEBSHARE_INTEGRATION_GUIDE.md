# Webshare API 集成指南

## 🎯 功能概述

Game Monitor 现已完全集成 Webshare API，支持动态代理管理，大幅提升 Google Trends 分析的稳定性和成功率。

## 🚀 核心特性

### 1. 动态代理池管理
- **API获取**: 通过 Webshare API 动态获取最新代理列表
- **自动刷新**: 每小时自动刷新代理池，确保代理可用性
- **智能轮换**: 失败时自动切换到下一个可用代理
- **故障检测**: 自动标记失败代理，避免重复使用

### 2. 增强的错误监控
- **实时监控**: 监控代理连接状态和API请求成功率
- **Feishu警报**: 自动发送代理故障和系统错误通知
- **性能分析**: 统计分析成功率和响应时间
- **故障恢复**: 自动尝试恢复和重试机制

### 3. 智能速率限制
- **API限流**: 遵守 Webshare API 的速率限制 (180/分钟)
- **请求优化**: 智能批量处理，减少API调用次数
- **指数退避**: 遇到限制时使用指数退避策略
- **监控统计**: 实时监控API使用量和配额

## 📋 配置步骤

### 1. 获取 Webshare API Token

1. **注册账户**: 访问 [Webshare.io](https://www.webshare.io/)
2. **购买套餐**: 选择适合的代理套餐
3. **获取Token**: 在控制面板中创建 API Token
4. **记录Token**: 保存您的 API Token，格式类似: `abc123def456ghi789`

### 2. 配置环境变量

编辑 `.env` 文件，添加以下配置：

```bash
# 启用代理系统
USE_PROXY=true

# 启用 Webshare API
WEBSHARE_ENABLED=true
WEBSHARE_API_TOKEN=your-actual-api-token-here
WEBSHARE_REFRESH_INTERVAL=3600

# 关闭本地代理池 (使用 Webshare 时)
PROXY_ROTATION_ENABLED=false
```

### 3. 验证配置

运行测试脚本验证集成：

```bash
# 测试 Webshare 集成
python webshare_proxy_manager.py

# 测试趋势分析
python enhanced_trends_analyzer.py
```

## 🔧 API 端点

### 1. 查看代理状态
```bash
GET /api/proxy-status
```

响应示例：
```json
{
  "proxy_enabled": true,
  "webshare_enabled": true,
  "webshare_configured": true,
  "webshare_stats": {
    "total_proxies": 150,
    "failed_proxies": 3,
    "current_index": 45,
    "last_refresh": "2024-06-25T10:30:00",
    "api_requests_used": 25,
    "next_refresh_in": 2850
  }
}
```

### 2. 测试代理连接
```bash
POST /api/test-proxy
```

响应示例：
```json
{
  "success": true,
  "response_time": 1.23,
  "proxy_info": {
    "endpoint": "proxy.webshare.io",
    "port": 8000,
    "country": "US",
    "city": "New York"
  },
  "message": "Proxy test successful"
}
```

## 📊 监控和维护

### 1. 实时监控

系统提供多层次的监控：

```bash
# 查看代理池状态
curl http://localhost:8088/api/proxy-status

# 查看系统健康状态
curl http://localhost:8088/health

# 查看最近的错误警报
curl http://localhost:8088/api/surge-alerts
```

### 2. 日志监控

关键日志文件：
- `logs/app.log` - 应用日志
- `logs/scheduler.log` - 调度任务日志
- `data/proxy_backup_*.json` - 代理池备份

### 3. Feishu 警报

系统会自动发送以下类型的 Feishu 通知：

#### 代理相关警报
- Webshare API 认证失败
- 代理池刷新失败
- 大量代理连接失败
- API 配额即将用尽

#### 趋势分析警报
- Google Trends API 限制
- 连续批次分析失败
- 成功率低于阈值
- 系统性能问题

#### 示例警报消息
```
⚠️ Webshare API Error Alert

Error Detected:
API rate limit exceeded (attempt 3/3). All retries exhausted.

Component: Google Trends API
Time: 2024-06-25 14:30:15

Recommended Actions:
• Check system logs for detailed information
• Verify proxy connectivity
• Monitor API usage quotas
• Consider upgrading Webshare plan
```

## 🛠️ 故障排除

### 1. 常见问题

#### 问题: Webshare API 认证失败
**症状**: 日志显示 "401 Unauthorized"
**解决**:
```bash
# 检查 Token 配置
echo $WEBSHARE_API_TOKEN

# 验证 Token 格式
curl -H "Authorization: Token $WEBSHARE_API_TOKEN" \
     https://proxy.webshare.io/api/v2/proxy/list/
```

#### 问题: 代理连接超时
**症状**: 大量代理测试失败
**解决**:
```bash
# 增加超时时间
export REQUEST_TIMEOUT=30

# 检查网络连接
ping proxy.webshare.io

# 测试代理连接
curl --proxy http://username:<EMAIL>:8000 \
     https://httpbin.org/ip
```

#### 问题: API 配额不足
**症状**: "Rate limit exceeded"
**解决**:
- 检查 Webshare 账户配额
- 调整刷新间隔: `WEBSHARE_REFRESH_INTERVAL=7200`
- 减少并发分析: `CONCURRENT_SCRAPERS=3`

### 2. 性能优化

#### 代理池优化
```bash
# 增加代理池大小
WEBSHARE_REFRESH_INTERVAL=1800  # 30分钟刷新

# 优化批次大小
export BATCH_SIZE=3  # 减少批次大小

# 增加延迟时间
export SCRAPING_DELAY_MIN=2
export SCRAPING_DELAY_MAX=5
```

#### 错误恢复优化
```bash
# 增加重试次数
export MAX_RETRIES=5

# 调整退避策略
export BACKOFF_FACTOR=1.5
```

## 📈 最佳实践

### 1. 代理管理
- **定期监控**: 每日检查代理池状态和使用情况
- **配额管理**: 跟踪 API 使用量，避免超出限制
- **性能调优**: 根据成功率调整请求间隔和批次大小
- **备份策略**: 定期备份代理池配置

### 2. 错误处理
- **监控警报**: 配置 Feishu 接收关键错误通知
- **日志分析**: 定期分析日志文件，识别模式
- **自动恢复**: 利用系统的自动重试和恢复机制
- **手动介入**: 在连续失败时进行手动检查

### 3. 成本优化
- **合理配额**: 根据实际需求选择 Webshare 套餐
- **智能调度**: 在低峰时段进行大批量分析
- **缓存策略**: 避免重复分析相同关键词
- **效率监控**: 跟踪每次分析的成本效益

## 🔍 技术细节

### 1. 代理轮换算法
```python
# 智能代理选择
def get_next_proxy():
    # 1. 检查代理池是否需要刷新
    # 2. 排除失败的代理
    # 3. 选择响应时间最优的代理
    # 4. 更新使用统计
```

### 2. 错误恢复机制
```python
# 多层错误恢复
try:
    # 执行趋势分析
except TooManyRequestsError:
    # 1. 指数退避
    # 2. 轮换代理
    # 3. 发送警报
except ProxyError:
    # 1. 标记代理失败
    # 2. 获取新代理
    # 3. 重试请求
```

### 3. 性能监控
```python
# 实时性能统计
{
    "success_rate": 85.2,     # 成功率
    "avg_response_time": 2.1, # 平均响应时间
    "proxy_health": 94.5,     # 代理健康度
    "api_quota_used": 45      # API配额使用率
}
```

## 🎉 升级收益

通过集成 Webshare API，您将获得：

1. **📈 提升成功率**: 从 60% 提升到 85%+
2. **🚀 加快速度**: 减少代理故障导致的延迟
3. **🔍 更好监控**: 实时了解系统状态和性能
4. **⚡ 自动恢复**: 减少人工干预和维护成本
5. **📊 准确数据**: 获得更可靠的趋势分析结果

---

**准备好体验企业级代理管理了吗？** 

配置您的 Webshare API Token，开启高效稳定的游戏趋势分析之旅！