# 部署文档

本文档详细介绍 HTML5 Game Monitor System 的各种部署方式。

## 目录

1. [系统要求](#系统要求)
2. [部署方式](#部署方式)
   - [自动化部署向导](#自动化部署向导)
   - [手动部署](#手动部署)
   - [Docker 部署](#docker-部署)
   - [生产环境部署](#生产环境部署)
3. [配置说明](#配置说明)
4. [调度器管理](#调度器管理)
5. [故障排除](#故障排除)

## 系统要求

### 硬件要求
- CPU: 2 核心以上
- 内存: 4GB 以上（推荐 8GB）
- 硬盘: 20GB 以上可用空间

### 软件要求
- 操作系统: Linux (CentOS 7/8, Ubuntu 18.04/20.04) 或 macOS
- Python: 3.8 以上版本
- MySQL: 5.7 以上版本
- Redis: 5.0 以上版本（可选）
- Git: 用于代码管理
- Node.js: 14.x 以上（仅前端开发需要）

## 部署方式

### 自动化部署向导

这是最简单的部署方式，适合快速开始：

```bash
# 进入项目目录
cd game-monitor

# 运行部署向导
bash scripts/setup/deploy_schedulers.sh
```

向导会自动完成以下步骤：
1. 检查系统环境
2. 创建 Python 虚拟环境
3. 安装所有依赖
4. 配置环境变量
5. 初始化数据库
6. 启动所有服务
7. 验证系统运行状态

### 手动部署

如果需要更精细的控制，可以按以下步骤手动部署：

#### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/yourusername/game-monitor.git
cd game-monitor

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 安装 Playwright 浏览器
playwright install chromium
```

#### 2. 数据库配置

```bash
# 连接 MySQL
mysql -u root -p

# 创建数据库和用户
CREATE DATABASE game_monitor CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'game_monitor'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON game_monitor.* TO 'game_monitor'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据库结构
mysql -u game_monitor -p game_monitor < sql/init.sql
```

#### 3. 环境配置

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

必须配置的项目：
```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=game_monitor
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=game_monitor

# Flask 配置
SECRET_KEY=your-secret-key-here
PORT=8088

# 飞书通知配置
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/xxx
ENABLE_FEISHU_ALERTS=true

# Reddit API 配置
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=game-monitor/1.0
```

#### 4. 启动服务

```bash
# 启动 Web 应用
source venv/bin/activate
python app.py &

# 启动所有调度器
python scripts/monitoring/manage_schedulers.py start all
```

### Docker 部署

使用 Docker 可以简化部署过程：

#### 1. 准备配置

```bash
# 复制配置文件
cp .env.example .env
cp docker-compose.override.yml.example docker-compose.override.yml

# 编辑配置
nano .env
```

#### 2. 构建和启动

```bash
# 构建镜像
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 3. 自定义配置

如需修改端口或其他配置，编辑 `docker-compose.override.yml`：

```yaml
version: '3.8'

services:
  app:
    ports:
      - "8089:8088"  # 修改外部端口
    environment:
      - CUSTOM_VAR=value
```

### 生产环境部署

#### 1. 使用 PM2 管理进程

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 保存进程列表
pm2 save

# 设置开机自启
pm2 startup
```

#### 2. 使用 Systemd 服务

创建服务文件 `/etc/systemd/system/game-monitor.service`：

```ini
[Unit]
Description=Game Monitor Web Service
After=network.target mysql.service

[Service]
Type=simple
User=game-monitor
WorkingDirectory=/opt/game-monitor
Environment="PATH=/opt/game-monitor/venv/bin"
ExecStart=/opt/game-monitor/venv/bin/python app.py
Restart=always

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable game-monitor
sudo systemctl start game-monitor
```

#### 3. Nginx 反向代理

```nginx
server {
    listen 80;
    server_name game-monitor.example.com;

    location / {
        proxy_pass http://localhost:8088;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /opt/game-monitor/static;
        expires 30d;
    }
}
```

## 配置说明

### 核心配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| MYSQL_HOST | MySQL 主机地址 | localhost |
| MYSQL_PORT | MySQL 端口 | 3306 |
| REDIS_HOST | Redis 主机地址 | localhost |
| REDIS_PORT | Redis 端口 | 6379 |
| PORT | Web 服务端口 | 8088 |
| SECRET_KEY | Flask 密钥 | 随机生成 |
| ENABLE_FEISHU_ALERTS | 启用飞书通知 | true |

### 调度器配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| SCRAPER_SCHEDULE | 爬虫运行时间 | 02:00 |
| REDDIT_INTERVAL_HOURS | Reddit 分析间隔 | 4 |
| TRENDS_INTERVAL_HOURS | 趋势分析间隔 | 6 |
| REPORT_SCHEDULE | 报告生成时间 | 09:00 |
| MONITOR_INTERVAL_MINUTES | 监控检查间隔 | 30 |

### 代理配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| WEBSHARE_API_KEY | Webshare API 密钥 | 空 |
| USE_PROXY | 启用代理 | false |
| PROXY_ROTATION_INTERVAL | 代理轮换间隔（秒） | 300 |

## 调度器管理

### 使用管理脚本

```bash
# 查看所有调度器状态
python scripts/monitoring/manage_schedulers.py status

# 启动特定调度器
python scripts/monitoring/manage_schedulers.py start scraper

# 停止所有调度器
python scripts/monitoring/manage_schedulers.py stop all

# 重启调度器
python scripts/monitoring/manage_schedulers.py restart reddit

# 查看调度器日志
tail -f logs/scraper.log
```

### 使用统一命令工具

```bash
# 启动所有服务
./game-monitor start all

# 查看状态
./game-monitor status

# 查看特定调度器日志
./game-monitor logs scraper -n 100

# 停止服务
./game-monitor stop all
```

### 调度器说明

1. **scheduler_scraper** - 网站爬虫调度器
   - 功能：爬取 27 个游戏网站数据
   - 频率：每日 02:00
   - 日志：logs/scraper.log

2. **scheduler_reddit** - Reddit 分析调度器
   - 功能：分析游戏相关 subreddit 讨论
   - 频率：每 4 小时
   - 日志：logs/reddit_analyzer.log

3. **scheduler_trends** - 趋势分析调度器
   - 功能：Google Trends 数据分析
   - 频率：每 6 小时
   - 日志：logs/trends_analyzer.log

4. **scheduler_reports** - 报告生成调度器
   - 功能：生成并发送每日报告
   - 频率：每日 09:00
   - 日志：logs/reports.log

5. **scheduler_monitor** - 系统监控调度器
   - 功能：监控系统健康状态
   - 频率：每 30 分钟
   - 日志：logs/monitor_manager.log

## 故障排除

### 常见问题

#### 1. 数据库连接失败

错误信息：`(2003, "Can't connect to MySQL server on 'localhost' (111)")`

解决方法：
```bash
# 检查 MySQL 服务状态
sudo systemctl status mysql

# 启动 MySQL
sudo systemctl start mysql

# 验证连接
mysql -u game_monitor -p -h localhost
```

#### 2. 端口被占用

错误信息：`[Errno 48] Address already in use`

解决方法：
```bash
# 查看端口占用
lsof -i :8088

# 修改配置文件中的端口
nano .env
# 修改 PORT=8089
```

#### 3. 调度器不运行

检查步骤：
```bash
# 查看进程
ps aux | grep scheduler

# 查看日志
tail -f logs/scheduler_name.log

# 手动运行测试
source venv/bin/activate
python src/scheduler_scraper.py
```

#### 4. Playwright 浏览器错误

错误信息：`Executable doesn't exist at /home/<USER>/.cache/ms-playwright/chromium-xxx/chrome-linux/chrome`

解决方法：
```bash
# 安装浏览器
source venv/bin/activate
playwright install chromium
playwright install-deps
```

### 性能优化

1. **数据库优化**
   ```sql
   -- 添加索引
   ALTER TABLE games ADD INDEX idx_created_at (created_at);
   ALTER TABLE trends ADD INDEX idx_game_date (game_id, date);
   
   -- 定期清理旧数据
   DELETE FROM trends WHERE date < DATE_SUB(NOW(), INTERVAL 90 DAY);
   ```

2. **Redis 缓存**
   ```bash
   # 启用 Redis 缓存
   REDIS_ENABLED=true
   REDIS_CACHE_TTL=3600
   ```

3. **并发优化**
   ```python
   # 在 config.py 中调整
   MAX_WORKERS = 10  # 爬虫并发数
   REQUEST_TIMEOUT = 30  # 请求超时时间
   ```

### 监控和告警

1. **设置监控告警**
   ```bash
   # 编辑监控配置
   nano src/scheduler_monitor.py
   
   # 设置告警阈值
   ERROR_THRESHOLD = 5  # 错误数超过5次告警
   SUCCESS_RATE_THRESHOLD = 0.8  # 成功率低于80%告警
   ```

2. **查看监控报告**
   - 每日 09:00 自动发送到飞书
   - 实时查看：http://localhost:8088/monitor
   - API 查看：http://localhost:8088/api/health

### 备份和恢复

1. **数据备份**
   ```bash
   # 备份数据库
   mysqldump -u game_monitor -p game_monitor > backup_$(date +%Y%m%d).sql
   
   # 备份配置和日志
   tar -czf backup_$(date +%Y%m%d).tar.gz .env logs/ data/
   ```

2. **数据恢复**
   ```bash
   # 恢复数据库
   mysql -u game_monitor -p game_monitor < backup_20240101.sql
   
   # 恢复配置
   tar -xzf backup_20240101.tar.gz
   ```

## 总结

本部署文档提供了多种部署方式，从简单的自动化向导到复杂的生产环境配置。选择适合你需求的方式：

- **开发测试**：使用自动化部署向导
- **小规模部署**：使用手动部署或 Docker
- **生产环境**：使用 PM2/Systemd + Nginx

如有问题，请查看日志文件或提交 Issue。