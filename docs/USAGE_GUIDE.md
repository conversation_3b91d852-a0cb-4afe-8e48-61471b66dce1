# Game Monitor 系统使用指南

## 🚀 快速开始

### 1. 系统启动
```bash
# 使用Docker (推荐)
./start.sh start

# 或本地运行
source venv/bin/activate
python app.py
```

### 2. 访问界面
- **增强仪表板**: http://localhost:8088/ (新版)
- **经典界面**: http://localhost:8088/classic (原版)
- **API文档**: http://localhost:8088/health

## 📊 核心功能

### 增强趋势分析
系统使用多基准线对比算法，提供比传统方法更准确的趋势分析：

**基准词汇级别**:
- `ultra_high`: 130k+ 搜索量 (如 "good morning images")
- `high`: 50k+ 搜索量 (如 "happy birthday image") 
- `medium`: 25k+ 搜索量 (如 "baby shower")
- `low`: 10k+ 搜索量 (如 "image to text converter")
- `very_low`: 5k+ 搜索量 (如 "gpts")

**爆发检测级别**:
- 🚀 **Viral Explosion**: 增长率>200% + 日增长>100% (置信度95%)
- 🔥 **Strong Surge**: 增长率>100% (置信度85%)
- 📈 **Moderate Surge**: 增长率>=50% (置信度70%)
- 📊 **Normal Growth**: 增长率<50%

### 智能过滤器
仪表板提供6种智能过滤器：
- **🎮 All Games**: 显示所有游戏
- **🚀 Explosive Growth**: 200%+ 增长率游戏
- **🔥 Viral Explosion**: 100-200% 增长率游戏
- **📈 Strong Growth**: 50-100% 增长率游戏
- **⬆️ Moderate Growth**: 0-50% 增长率游戏
- **✨ New Discoveries**: 最新发现的游戏

## 🔧 系统配置

### 环境变量配置
复制并编辑配置文件：
```bash
cp .env.example .env
```

**重要配置项**:
```bash
# 启用代理系统 (提高分析成功率)
USE_PROXY=true
WEBSHARE_ENABLED=true
WEBSHARE_API_TOKEN=your-webshare-api-token

# 基准词汇 (可自定义)
BASELINE_KEYWORDS=minecraft,fortnite,game,mobile game,puzzle game

# Feishu通知 (可选)
FEISHU_WEBHOOK_URL=your-feishu-webhook-url
ENABLE_FEISHU_ALERTS=true

# 爆发检测阈值
SURGE_THRESHOLD=50.0
```

### Webshare代理配置 (推荐)
1. 注册 [Webshare.io](https://www.webshare.io/) 账户
2. 获取API Token
3. 在.env中配置WEBSHARE_API_TOKEN
4. 成功率可从60%提升到85%+

## 📱 Web界面操作

### 主仪表板功能
1. **统计面板**: 实时显示游戏总数、今日新增、趋势数量、爆发数量
2. **过滤系统**: 点击标签筛选不同类型的游戏
3. **趋势图表**: 每个游戏卡片显示7天Mini趋势图
4. **实时刷新**: 5分钟自动更新 + 手动刷新按钮

### 游戏卡片信息
每个游戏卡片显示：
- **增长率**: 综合增长率百分比
- **热度级别**: 🔥🔥 extremely_hot 到 🧊 cold
- **搜索量级别**: Ultra High 到 Very Low
- **趋势强度**: Explosive ↗️ 到 Declining ↘️
- **7天趋势图**: 交互式迷你图表

## 🛠️ API使用

### 主要API端点
```bash
# 获取统计数据
GET /api/stats

# 获取趋势游戏 (增强版)
GET /api/trending-games?limit=20&days=7

# 获取爆发警报
GET /api/surge-alerts?limit=10&hours=24

# 检查代理状态
GET /api/proxy-status

# 触发趋势分析
POST /api/analyze-trends
Content-Type: application/json
{"game_id": 123}
```

### API响应示例
```json
{
  "trending_games": [{
    "id": 123,
    "name": "Super Puzzle Adventure",
    "growth_rate": 245.7,
    "heat_level": "extremely_hot",
    "volume_level": "high",
    "trend_strength": "explosive",
    "relative_heat": 3.8,
    "is_surge": true,
    "surge_type": "viral_explosion",
    "surge_confidence": 0.95,
    "trend_data": [10, 15, 25, 45, 78, 95, 120]
  }]
}
```

## 🔍 故障排除

### 常见问题

**1. 趋势分析失败**
- 检查网络连接
- 验证代理配置
- 查看 logs/app.log

**2. Webshare API错误**
```bash
# 检查API Token
curl -H "Authorization: Token YOUR_TOKEN" \
  https://proxy.webshare.io/api/v2/proxy/list/?mode=direct

# 检查代理状态
curl http://localhost:8088/api/proxy-status
```

**3. 数据库连接问题**
```bash
# Docker环境
docker-compose logs mysql

# 本地环境
# 修改.env中 MYSQL_HOST=localhost
```

### 性能优化
- **代理配置**: 启用Webshare可提升成功率到85%+
- **批次大小**: 默认5个游戏/批次，可调整BATCH_SIZE
- **分析频率**: 默认4小时分析间隔，可调整TREND_CHECK_INTERVAL

## 📈 最佳实践

### 监控建议
1. **每日检查**: 查看仪表板统计面板
2. **关注爆发**: 重点关注🚀和🔥标记的游戏
3. **趋势追踪**: 观察连续几天的增长趋势
4. **设置提醒**: 配置Feishu接收重要警报

### 数据分析技巧
1. **对比分析**: 使用多个基准词汇对比
2. **时间维度**: 观察7天、30天趋势变化
3. **热度评估**: 结合相对热度和搜索量级别
4. **置信度**: 重点关注置信度>80%的爆发信号

## 🆘 技术支持

- **日志文件**: `logs/app.log`, `logs/scheduler.log`
- **健康检查**: http://localhost:8088/health
- **系统状态**: http://localhost:8088/api/proxy-status
- **错误通知**: 配置Feishu接收实时警报

---

*本系统基于最新的多基准线对比算法，提供企业级的游戏趋势分析能力。如需技术支持，请查看相关日志文件。*