# 认证系统设置指南

## 1. 生成密码哈希

运行密码生成脚本：
```bash
python generate_password_hash.py
```

输入你想要的密码（例如 abcd234），脚本会生成 SHA256 哈希值。

## 2. 配置环境变量

在 `.env` 文件中添加以下配置：

```env
# Authentication Configuration
AUTH_USERNAME=Zacharyhu
AUTH_PASSWORD_HASH=你生成的哈希值
AUTH_SECRET_KEY=your-jwt-secret-key-change-in-production
AUTH_TOKEN_EXPIRY=86400  # 24小时
```

### 示例

如果密码是 `abcd234`，生成的哈希值是：
```
AUTH_PASSWORD_HASH=1739540c812a2bbfb526417e14ea3c3068ec300ed0aa047dd1e0d4e3e5c568f2
```

## 3. 安装依赖

```bash
pip install PyJWT
```

## 4. 重启服务

```bash
./manage.sh restart
```

## 5. 登录系统

1. 访问 http://localhost:3000
2. 会自动跳转到登录页面
3. 输入用户名：Zacharyhu
4. 输入密码：你设置的密码
5. 登录成功后会跳转到主页面

## 安全说明

1. **修改默认配置**：
   - 修改 `AUTH_SECRET_KEY` 为随机字符串
   - 可以修改 `AUTH_USERNAME` 为其他用户名

2. **密码要求**：
   - 建议使用强密码
   - 定期更换密码

3. **Token 过期**：
   - 默认 24 小时过期
   - 可通过 `AUTH_TOKEN_EXPIRY` 调整（单位：秒）

## API 认证

所有 API 请求需要在 Header 中携带 token：
```
Authorization: Bearer <your-token>
```

前端已自动处理，手动调用 API 时需要注意。