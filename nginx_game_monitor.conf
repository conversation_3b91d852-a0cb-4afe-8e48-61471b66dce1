# Game Monitor Nginx 配置
# 请将此配置添加到宝塔网站配置中

# API 和旧版页面代理
location /api {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 超时设置
    proxy_connect_timeout 300s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
}

# 旧版 Flask 页面
location /classic {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

location /games {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

location /trends {
    proxy_pass http://127.0.0.1:8088;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}

# 新版 React 静态文件（默认）
location / {
    root /www/wwwroot/game-monitor/frontend/build;
    try_files $uri /index.html;
    
    # 防止缓存 index.html
    location = /index.html {
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }
}

# 静态资源缓存
location /static {
    root /www/wwwroot/game-monitor/frontend/build;
    expires 1y;
    add_header Cache-Control "public, immutable";
}
