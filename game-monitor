#!/bin/bash
# Game Monitor System - 统一命令行工具

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本所在目录
ROOT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$ROOT_DIR"

# 显示帮助信息
show_help() {
    echo -e "${GREEN}Game Monitor System - 游戏监控系统${NC}"
    echo ""
    echo "使用方法:"
    echo "  ./game-monitor <command> [options]"
    echo ""
    echo "命令:"
    echo "  start [all|scraper|reddit|trends|reports|monitor]  启动服务"
    echo "  stop [all|scraper|reddit|trends|reports|monitor]   停止服务"
    echo "  restart [all|scraper|reddit|trends|reports|monitor] 重启服务"
    echo "  status                                              查看所有服务状态"
    echo "  logs <service> [-n lines]                           查看服务日志"
    echo "  test [all|system|reddit|monitor]                    运行测试"
    echo "  deploy                                              部署向导"
    echo "  health                                              健康检查"
    echo "  cleanup                                             清理日志和缓存"
    echo "  help                                                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./game-monitor start all          # 启动所有服务"
    echo "  ./game-monitor status             # 查看状态"
    echo "  ./game-monitor logs scraper -n 50 # 查看爬虫日志"
    echo "  ./game-monitor test system        # 运行系统测试"
}

# 检查Python环境
check_python() {
    if [ -f "venv/bin/python" ]; then
        PYTHON_CMD="venv/bin/python"
    else
        PYTHON_CMD="python3"
    fi
}

# 启动服务
start_service() {
    local service=$1
    check_python
    
    if [ "$service" == "all" ] || [ -z "$service" ]; then
        echo -e "${GREEN}启动所有服务...${NC}"
        $PYTHON_CMD scripts/monitoring/manage_schedulers.py start all
    else
        echo -e "${GREEN}启动 $service 服务...${NC}"
        $PYTHON_CMD scripts/monitoring/manage_schedulers.py start $service
    fi
}

# 停止服务
stop_service() {
    local service=$1
    check_python
    
    if [ "$service" == "all" ] || [ -z "$service" ]; then
        echo -e "${YELLOW}停止所有服务...${NC}"
        $PYTHON_CMD scripts/monitoring/manage_schedulers.py stop all
    else
        echo -e "${YELLOW}停止 $service 服务...${NC}"
        $PYTHON_CMD scripts/monitoring/manage_schedulers.py stop $service
    fi
}

# 重启服务
restart_service() {
    local service=$1
    check_python
    
    if [ "$service" == "all" ] || [ -z "$service" ]; then
        echo -e "${YELLOW}重启所有服务...${NC}"
        $PYTHON_CMD scripts/monitoring/manage_schedulers.py restart all
    else
        echo -e "${YELLOW}重启 $service 服务...${NC}"
        $PYTHON_CMD scripts/monitoring/manage_schedulers.py restart $service
    fi
}

# 查看状态
show_status() {
    check_python
    echo -e "${BLUE}系统状态:${NC}"
    $PYTHON_CMD scripts/monitoring/manage_schedulers.py status
}

# 查看日志
show_logs() {
    local service=$1
    local lines=$3
    check_python
    
    if [ -z "$service" ]; then
        echo -e "${RED}错误: 请指定服务名称${NC}"
        echo "用法: ./game-monitor logs <service> [-n lines]"
        exit 1
    fi
    
    if [ -z "$lines" ]; then
        lines=50
    fi
    
    $PYTHON_CMD scripts/monitoring/manage_schedulers.py logs $service -n $lines
}

# 运行测试
run_test() {
    local test_type=$1
    check_python
    
    case $test_type in
        "all")
            echo -e "${BLUE}运行所有测试...${NC}"
            $PYTHON_CMD scripts/testing/test_system.py
            ;;
        "system")
            echo -e "${BLUE}运行系统测试...${NC}"
            $PYTHON_CMD scripts/testing/test_system.py
            ;;
        "reddit")
            echo -e "${BLUE}运行Reddit测试...${NC}"
            $PYTHON_CMD scripts/testing/test_reddit_integration.py
            ;;
        "monitor")
            echo -e "${BLUE}运行监控测试...${NC}"
            $PYTHON_CMD scripts/testing/test_monitor.py --all
            ;;
        *)
            echo -e "${RED}未知的测试类型: $test_type${NC}"
            echo "可用的测试类型: all, system, reddit, monitor"
            exit 1
            ;;
    esac
}

# 部署向导
deploy_wizard() {
    echo -e "${GREEN}启动部署向导...${NC}"
    bash scripts/setup/deploy_schedulers.sh
}

# 健康检查
health_check() {
    check_python
    echo -e "${BLUE}执行健康检查...${NC}"
    
    # 检查Web服务
    echo -n "Web服务: "
    if curl -s http://localhost:8088/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${RED}✗ 异常${NC}"
    fi
    
    # 检查调度器
    echo ""
    $PYTHON_CMD scripts/monitoring/manage_schedulers.py status
    
    # 检查数据库
    echo ""
    echo -n "数据库连接: "
    $PYTHON_CMD -c "from scripts.testing.test_system import test_database_connection; test_database_connection()" 2>/dev/null && echo -e "${GREEN}✓ 正常${NC}" || echo -e "${RED}✗ 异常${NC}"
}

# 清理日志和缓存
cleanup() {
    echo -e "${YELLOW}清理系统...${NC}"
    
    # 清理旧日志（保留最近7天）
    find logs/ -name "*.log" -mtime +7 -delete 2>/dev/null
    echo "✓ 清理旧日志文件"
    
    # 清理Python缓存
    find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null
    echo "✓ 清理Python缓存"
    
    # 清理临时文件
    rm -f /tmp/game_monitor_* 2>/dev/null
    echo "✓ 清理临时文件"
    
    echo -e "${GREEN}清理完成${NC}"
}

# 主程序
case "$1" in
    start)
        start_service $2
        ;;
    stop)
        stop_service $2
        ;;
    restart)
        restart_service $2
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs $2 $3 $4
        ;;
    test)
        run_test $2
        ;;
    deploy)
        deploy_wizard
        ;;
    health)
        health_check
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        if [ -z "$1" ]; then
            show_help
        else
            echo -e "${RED}未知命令: $1${NC}"
            echo "使用 './game-monitor help' 查看帮助信息"
            exit 1
        fi
        ;;
esac